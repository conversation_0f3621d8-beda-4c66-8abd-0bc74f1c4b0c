"""
智能政务平台后端服务主应用
提供问答、AI检索、知识库检索等API接口
"""

import sys
import os
import logging
from datetime import datetime

# 添加Backend目录到Python路径，支持从项目根目录启动
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from sensitive_filter import sensitive_filter
from ali_knowledge_service import AliCloudKnowledgeService
from qa_database import QADatabase
from smart_policy_service import SmartPolicyService
from city_info_service import CityInfoService
from smart_guide_service import SmartGuideService
from smart_data_service import SmartDataService


app = Flask(__name__)
CORS(app)

# 配置Flask应用日志
if not app.debug:
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置文件日志处理器
    file_handler = logging.FileHandler(
        os.path.join(log_dir, 'flask_app.log'),
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))

    # 配置控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))

    # 添加处理器到Flask应用日志
    app.logger.addHandler(file_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(logging.INFO)

app.logger.info("🚀 智能政务平台后端服务启动")

# 初始化服务
qa_db = QADatabase()

ali_knowledge_service = AliCloudKnowledgeService()
smart_policy_service = SmartPolicyService()
city_info_service = CityInfoService()
smart_guide_service = SmartGuideService()
smart_data_service = SmartDataService()


@app.route('/ask', methods=['POST'])
def ask():
    try:
        data = request.get_json(force=True)
        question = data.get('question', '').strip()

        # 确保正确处理UTF-8编码
        if isinstance(question, bytes):
            question = question.decode('utf-8')

        if not question:
            return jsonify({
                'steps': ['输入校验', '问题为空'],
                'answer': '请输入您的问题。'
            })
        
        print(f"Processing question: {question}")

        # 进行敏感词检测
        safety_check = sensitive_filter.check_sensitive_content(question)
        print(f"Safety check result: {safety_check}")

        # 简化处理逻辑：通过或不通过
        if not safety_check['is_safe']:
            # 校验不通过，直接返回错误，流程图显示红色
            steps = [
                '提问安全校验 ❌'
                
            ]
            
            return jsonify({
                'steps': steps,
                'answer': safety_check['message']
            })
        
        # 校验通过，检查是否为城市介绍问题
        if question.strip() == '城市介绍':
            # 特殊处理城市介绍问题 - 去除校验动画步骤
            # 读取城市介绍内容
            try:
                with open(city_info_service.city_md_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # 处理内容，将图片路径转换为markdown图片格式
                lines = content.split('\n')
                processed_lines = []

                for line in lines:
                    # 处理图片路径行
                    if line.strip().startswith('pic/') and line.strip().endswith('.png'):
                        # 转换为markdown图片格式
                        img_path = line.strip()
                        img_name = img_path.split('/')[-1].replace('.png', '')
                        processed_lines.append(f'![{img_name}]({img_path})')
                    else:
                        processed_lines.append(line)

                processed_content = '\n'.join(processed_lines)

                return jsonify({
                    'steps': [],  # 去除校验动画步骤
                    'answer': processed_content,
                    'source': 'city_info',
                    'is_streaming': True,  # 标识支持流式输出
                    'hide_consult_button': True  # 标识隐藏人工咨询按钮
                })

            except Exception as e:
                print(f"Error reading city introduction: {e}")
                return jsonify({
                    'steps': [],  # 去除校验动画步骤
                    'answer': '抱歉，获取城市介绍时出现错误。请稍后再试。',
                    'source': 'error'
                })

        # 校验通过，检查是否为故宫问题
        if question.strip() == '故宫':
            # 特殊处理故宫问题 - 去除校验动画步骤
            # 读取故宫介绍内容
            try:
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                gugong_md_path = os.path.join(project_root, 'DB', 'gugong.md')

                with open(gugong_md_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # 处理内容，将图片路径转换为markdown图片格式
                lines = content.split('\n')
                processed_lines = []

                for line in lines:
                    # 处理图片路径行
                    if line.strip().startswith('pic/') and line.strip().endswith('.png'):
                        # 转换为markdown图片格式
                        img_path = line.strip()
                        img_name = img_path.split('/')[-1].replace('.png', '')
                        processed_lines.append(f'![{img_name}]({img_path})')
                    else:
                        processed_lines.append(line)

                processed_content = '\n'.join(processed_lines)

                return jsonify({
                    'steps': [],  # 去除校验动画步骤
                    'answer': processed_content,
                    'source': 'attraction_info',
                    'is_streaming': True,  # 标识支持流式输出
                    'hide_consult_button': True  # 标识隐藏人工咨询按钮
                })

            except Exception as e:
                print(f"Error reading gugong introduction: {e}")
                return jsonify({
                    'steps': [],  # 去除校验动画步骤
                    'answer': '抱歉，获取故宫介绍时出现错误。请稍后再试。',
                    'source': 'error'
                })

        # 校验通过，检查是否为西湖问题
        if question.strip() == '西湖':
            # 特殊处理西湖问题 - 去除校验动画步骤
            # 读取西湖介绍内容
            try:
                # 获取项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                xihu_md_path = os.path.join(project_root, 'DB', 'xihu.md')

                with open(xihu_md_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # 处理内容，将图片路径转换为markdown图片格式
                lines = content.split('\n')
                processed_lines = []

                for line in lines:
                    # 处理图片路径行
                    if line.strip().startswith('pic/') and line.strip().endswith('.png'):
                        # 转换为markdown图片格式
                        img_path = line.strip()
                        img_name = img_path.split('/')[-1].replace('.png', '')
                        processed_lines.append(f'![{img_name}]({img_path})')
                    else:
                        processed_lines.append(line)

                processed_content = '\n'.join(processed_lines)

                return jsonify({
                    'steps': [],  # 去除校验动画步骤
                    'answer': processed_content,
                    'source': 'attraction_info',
                    'is_streaming': True,  # 标识支持流式输出
                    'hide_consult_button': True  # 标识隐藏人工咨询按钮
                })

            except Exception as e:
                print(f"Error reading xihu introduction: {e}")
                return jsonify({
                    'steps': [],  # 去除校验动画步骤
                    'answer': '抱歉，获取西湖介绍时出现错误。请稍后再试。',
                    'source': 'error'
                })

        # 处理正常问题
        steps = [
            '提问安全校验',
            '知识库检索',
            '匹配答案',
            '输出安全校验',
            '输出'
        ]

        # 从数据库中查找答案
        qa_result = qa_db.find_answer(question)
        
        if qa_result:
            answer = qa_result['answer']
            
            # 根据doit字段判断是否显示立刻办理按钮
            if qa_result['doit'] == '是':
                answer += "\n\n💡 此事项支持在线办理，您可以点击【立刻办理】按钮进行操作。"
                if qa_result['url']:
                    answer += f"\n🔗 办理链接：{qa_result['url']}"
            
            # 添加咨询电话（特殊处理id=27的问题）
            if qa_result['phone'] and qa_result['id'] != 27:
                answer += f"\n📞 咨询电话：{qa_result['phone']}"
        else:
            # 没有找到匹配的答案
            answer = '这个问题我还在学习。\n\n如需人工服务，请拨打咨询电话：010-88888888'
        
        return jsonify({
            'steps': steps,
            'answer': answer,
            'qa_id': qa_result['id'] if qa_result else None,
            'url': qa_result['url'] if qa_result else None,
            'phone': qa_result['phone'] if qa_result else None,
            'source': 'local_qa'
        })
        
    except Exception as e:
        print(f"Error processing request: {e}")
        return jsonify({
            'steps': ['系统错误'],
            'answer': '系统暂时不可用，请稍后再试或拨打人工咨询电话：010-88888888'
        }), 500

@app.route('/ask_ai', methods=['POST'])
def ask_ai():
    """联网搜索API端点 - 优先检索文档，未命中则调用AI模型"""
    try:
        data = request.get_json()
        question = data.get('question', '').strip()
        online_search_enabled = data.get('online_search', False)
        
        if not question:
            return jsonify({
                'steps': ['输入错误'],
                'answer': '请输入有效的问题'
            }), 400
        
        print(f"Online search request: {question}, enabled: {online_search_enabled}")
        
        # 步骤1：安全校验
        safety_check = sensitive_filter.check_sensitive_content(question)
        if not safety_check['is_safe']:
            steps = [
                '提问安全校验 ❌'
               
            ]
            
            return jsonify({
                'steps': steps,
                'answer': safety_check['message']
            })
        
        # 步骤2：根据是否开启联网搜索选择不同的处理逻辑
        if online_search_enabled:
            # 联网搜索模式：直接调用阿里云联网搜索，跳过本地QAlist检索
            print("Online search mode: calling Alibaba Cloud online search")
            steps = [
                '提问安全校验 ✓',
                '联网搜索',
                '输出安全校验 ✓',
                '生成回答'
            ]

            # 切换到联网搜索模式
            ali_knowledge_service.set_online_search_mode()

            # 调用阿里云联网搜索
            search_result = ali_knowledge_service.search_knowledge(question)

            if search_result['success']:
                answer = search_result['answer']
                return jsonify({
                    'steps': steps,
                    'answer': answer,
                    'source': 'ali_online_search',
                    'request_id': search_result.get('request_id'),
                    'doc_references': search_result.get('doc_references', []),
                    'is_online_search': True  # 标识这是联网搜索结果
                })
            else:
                # 联网搜索失败
                return jsonify({
                    'steps': ['提问安全校验 ✓', '联网搜索 ❌'],
                    'answer': f'联网搜索服务暂时不可用：{search_result["error"]}\n\n如需人工服务，请拨打咨询电话：010-88888888',
                    'source': 'error'
                })
        else:
            # 普通模式：先检索本地QAlist，未命中则返回未找到
            steps = [
                '提问安全校验 ✓',
                '本地知识库检索',
                '匹配答案',
                '输出安全校验 ✓',
                '输出'
            ]

            qa_result = qa_db.find_answer(question)

            if qa_result:
                # 在本地知识库中找到答案
                print(f"Local knowledge hit: {qa_result['question']}")
                answer = qa_result['answer']

                # 根据doit字段判断是否显示立刻办理按钮
                if qa_result['doit'] == '是':
                    answer += "\n\n💡 此事项支持在线办理，您可以点击【立刻办理】按钮进行操作。"
                    if qa_result['url']:
                        answer += f"\n🔗 办理链接：{qa_result['url']}"

                # 添加咨询电话（特殊处理id=27的问题）
                if qa_result['phone'] and qa_result['id'] != 27:
                    answer += f"\n📞 咨询电话：{qa_result['phone']}"

                return jsonify({
                    'steps': steps,
                    'answer': answer,
                    'source': 'local_knowledge',
                    'qa_id': qa_result['id'],
                    'url': qa_result['url'],
                    'phone': qa_result['phone']
                })
            else:
                # 未开启联网搜索且本地未找到，返回标准未找到回答
                return jsonify({
                    'steps': ['提问安全校验 ✓', '本地知识库检索', '未找到匹配'],
                    'answer': '这个问题我还在学习。\n\n💡 您可以开启【联网搜索】功能获取更智能的回答。\n\n📞 如需人工服务，请拨打咨询电话：010-88888888',
                    'source': 'not_found'
                })
            
    except Exception as e:
        print(f"Error processing online search request: {e}")
        return jsonify({
            'steps': ['系统错误'],
            'answer': '系统暂时不可用，请稍后再试或拨打人工咨询电话：010-88888888'
        }), 500

@app.route('/reload_data', methods=['POST'])
def reload_data():
    """重新加载Excel数据的接口"""
    try:
        qa_db.load_qa_data()
        return jsonify({
            'status': 'success',
            'message': f'数据重新加载成功，共 {len(qa_db.qa_data)} 条记录'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'数据加载失败: {e}'
        }), 500

@app.route('/qa_stats', methods=['GET'])
def qa_stats():
    """获取问答数据统计"""
    return jsonify({
        'total_questions': len(qa_db.qa_data),
        'data_source': qa_db.excel_path,
        'sample_questions': [qa['question'] for qa in qa_db.qa_data[:3]]
    })



@app.route('/get_questions_by_type/<type_name>', methods=['GET'])
def get_questions_by_type(type_name):
    """根据类型获取问题列表"""
    try:
        questions = []
        for qa in qa_db.qa_data:
            if qa['type'] and qa['type'].strip() == type_name.strip():
                questions.append({
                    'id': qa['id'],
                    'question': qa['question'],
                    'type': qa['type'],
                    'doit': qa['doit'],
                    'url': qa['url'],
                    'phone': qa['phone']
                })

        return jsonify({
            'status': 'success',
            'type': type_name,
            'total_questions': len(questions),
            'questions': questions
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取问题列表失败: {e}'
        }), 500

@app.route('/get_all_types', methods=['GET'])
def get_all_types():
    """获取所有数据类型及其导办问题统计"""
    try:
        type_stats = {}

        for qa in qa_db.qa_data:
            if qa['type']:
                type_name = qa['type'].strip()
                if type_name not in type_stats:
                    type_stats[type_name] = {
                        'total_questions': 0,
                        'guide_questions': 0,
                        'guide_items': []
                    }

                type_stats[type_name]['total_questions'] += 1

                # 检查是否是导办项目
                if qa['doit'] and str(qa['doit']).strip() == '是':
                    type_stats[type_name]['guide_questions'] += 1
                    type_stats[type_name]['guide_items'].append({
                        'id': qa['id'],
                        'question': qa['question'],
                        'url': qa['url'],
                        'phone': qa['phone']
                    })

        return jsonify({
            'status': 'success',
            'total_types': len(type_stats),
            'types': type_stats
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取类型统计失败: {e}'
        }), 500

@app.route('/ask_knowledge', methods=['POST'])
def ask_knowledge():
    """知识库检索API端点"""
    try:
        data = request.get_json()
        question = data.get('question', '').strip()

        if not question:
            return jsonify({
                'steps': ['输入错误'],
                'answer': '请输入有效的问题'
            }), 400

        print(f"Knowledge base search request: {question}")
        print(f"Question type: {type(question)}")
        print(f"Question length: {len(question)}")
        print(f"Question encoding: {question.encode('utf-8') if question else 'None'}")

        # 步骤1：安全校验
        safety_check = sensitive_filter.check_sensitive_content(question)
        if not safety_check['is_safe']:
            steps = [
                '提问安全校验 ❌'
              
            ]

            return jsonify({
                'steps': steps,
                'answer': safety_check['message']
            })

        # 步骤2：调用知识库检索
        steps = [
            '提问安全校验 ✓',
            '知识库检索',
            '生成回答',
            '输出安全校验 ✓',
            '输出'
        ]

        # 确保使用知识库检索模式
        ali_knowledge_service.set_knowledge_mode()

        knowledge_result = ali_knowledge_service.search_knowledge(question)

        if knowledge_result['success']:
            answer = knowledge_result['answer']
            # 添加知识库检索标识
            

            return jsonify({
                'steps': steps,
                'answer': answer,
                'source': 'ali_knowledge',
                'request_id': knowledge_result.get('request_id'),
                'doc_references': knowledge_result.get('doc_references', [])
            })
        else:
            # 知识库检索失败
            error_steps = [
                '提问安全校验 ✓',
                '知识库检索 ❌',
                '错误处理'
            ]

            return jsonify({
                'steps': error_steps,
                'answer': f'{knowledge_result["error"]}\n\n如需人工服务，请拨打咨询电话：010-88888888',
                'source': 'error'
            })

    except Exception as e:
        print(f"Error processing knowledge base search request: {e}")
        return jsonify({
            'steps': ['系统错误'],
            'answer': '系统暂时不可用，请稍后再试或拨打人工咨询电话：010-88888888'
        }), 500

@app.route('/ask_policy', methods=['POST'])
def ask_policy():
    """智能问策API端点"""
    try:
        data = request.get_json()
        question = data.get('question', '').strip()

        if not question:
            return jsonify({
                'steps': ['输入错误'],
                'answer': '请输入有效的问题或政策链接'
            }), 400

        print(f"Smart policy request: {question}")

        # 步骤1：安全校验
        safety_check = sensitive_filter.check_sensitive_content(question)
        if not safety_check['is_safe']:
            steps = [
                '提问安全校验 ❌'
            ]

            return jsonify({
                'steps': steps,
                'answer': safety_check['message']
            })

        # 步骤2：调用智能问策服务
        steps = [
            '提问安全校验 ✓',
            '政策链接解析',
            '智能问策分析',
            '输出安全校验 ✓',
            '生成回答'
        ]

        policy_result = smart_policy_service.analyze_policy(question)

        if policy_result['success']:
            answer = policy_result['answer']

            # 如果有文档引用，添加引用信息（但不覆盖原始答案格式）
            if policy_result.get('doc_references') and len(policy_result['doc_references']) > 0:
                doc_references = policy_result['doc_references']
                answer += "\n\n📚 **参考文档**："
                for i, doc_ref in enumerate(doc_references[:3], 1):  # 最多显示3个引用
                    doc_name = doc_ref.get('doc_name', '未知文档')
                    doc_title = doc_ref.get('title', '')
                    if doc_title and doc_title != doc_name:
                        answer += f"\n{i}. {doc_name} - {doc_title}"
                    else:
                        answer += f"\n{i}. {doc_name}"

                # 添加使用提示
                answer += "\n\n💡 **使用提示**：您可以继续发送政策链接或政策相关问题，我会为您提供专业的政策解读和分析。"

            return jsonify({
                'steps': steps,
                'answer': answer,
                'source': 'smart_policy',
                'request_id': policy_result.get('request_id'),
                'doc_references': policy_result.get('doc_references', []),
                'has_policy_link': policy_result.get('has_policy_link', False)
            })
        else:
            # 智能问策服务失败
            error_steps = [
                '提问安全校验 ✓',
                '智能问策分析 ❌',
                '错误处理'
            ]

            return jsonify({
                'steps': error_steps,
                'answer': f'{policy_result["error"]}\n\n如需人工服务，请拨打咨询电话：010-88888888',
                'source': 'error'
            })

    except Exception as e:
        print(f"Error processing smart policy request: {e}")
        return jsonify({
            'steps': ['系统错误'],
            'answer': '智能问策服务暂时不可用，请稍后再试或拨打人工咨询电话：010-88888888'
        }), 500

@app.route('/ask_data', methods=['POST'])
def ask_data():
    """智能问数API端点"""
    request_start_time = datetime.now()
    client_ip = request.remote_addr

    app.logger.info(f"📊 智能问数API请求开始 - 客户端IP: {client_ip}")

    try:
        data = request.get_json()
        question = data.get('question', '').strip()

        app.logger.info(f"❓ 用户问题: {question}")
        app.logger.debug(f"📝 请求数据: {data}")

        if not question:
            app.logger.warning("⚠️ 请求参数错误：问题为空")
            return jsonify({
                'steps': ['参数错误'],
                'answer': '请提供有效的问题'
            }), 400

        # 步骤1：安全校验
        app.logger.info("🔒 开始输入安全校验...")
        safety_check = sensitive_filter.check_sensitive_content(question)
        if not safety_check['is_safe']:
            app.logger.warning(f"⚠️ 输入安全校验失败: {safety_check['message']}")
            return jsonify({
                'steps': ['提问安全校验 ❌'],
                'answer': safety_check['message']
            })
        app.logger.info("✅ 输入安全校验通过")

        # 步骤2：调用智能问数服务
        steps = [
            '提问安全校验 ✓',
            '数据提取分析',
            '智能问数分析',
            '输出安全校验 ✓',
            '生成回答'
        ]

        # 调用智能问数服务
        app.logger.info("🤖 开始调用智能问数服务...")
        service_start_time = datetime.now()
        result = smart_data_service.analyze_question(question)
        service_time = (datetime.now() - service_start_time).total_seconds()
        app.logger.info(f"⏱️ 智能问数服务耗时: {service_time:.2f} 秒")

        if result['success']:
            app.logger.info("✅ 智能问数服务调用成功")

            # 构建完整的回答，包含分析报告和图表
            answer = result['analysis_report']
            charts_count = len(result.get('charts', []))
            app.logger.info(f"📊 生成分析报告，长度: {len(answer)} 字符，图表数量: {charts_count}")

            # 输出安全校验
            app.logger.info("🔒 开始输出安全校验...")
            output_safety = sensitive_filter.check_sensitive_content(answer)
            if not output_safety['is_safe']:
                app.logger.warning(f"⚠️ 输出安全校验失败: {output_safety['message']}")
                return jsonify({
                    'steps': steps[:-1] + ['输出安全校验 ❌'],
                    'answer': '回答内容包含不当信息，已被过滤'
                })
            app.logger.info("✅ 输出安全校验通过")

            # 构建响应数据
            response_data = {
                'steps': steps,
                'answer': answer,
                'source': 'smart_data_service',
                'has_chart': len(result.get('charts', [])) > 0
            }

            # 如果有图表数据，添加到响应中
            if result.get('charts'):
                response_data['charts'] = result['charts']
                app.logger.info(f"📈 添加图表数据: {len(result['charts'])} 个图表")

            if result.get('data_summary'):
                response_data['data_summary'] = result['data_summary']
                app.logger.debug("📋 添加数据摘要")

            # 计算总处理时间
            total_time = (datetime.now() - request_start_time).total_seconds()
            app.logger.info(f"⏱️ 请求处理完成，总耗时: {total_time:.2f} 秒")
            app.logger.info(f"✅ 智能问数API请求成功 - 客户端IP: {client_ip}")

            return jsonify(response_data)
        else:
            app.logger.error(f"❌ 智能问数服务调用失败: {result.get('error', '未知错误')}")
            # 如果分析失败，返回错误信息
            return jsonify({
                'steps': ['智能问数分析 ❌'],
                'answer': result.get('error', '智能问数服务暂时不可用，请稍后再试'),
                'source': 'smart_data_service',
                'has_chart': False
            }), 500

    except Exception as e:
        total_time = (datetime.now() - request_start_time).total_seconds()
        app.logger.error(f"❌ 智能问数API请求异常: {e}")
        app.logger.error(f"⏱️ 异常前耗时: {total_time:.2f} 秒")
        app.logger.error(f"❌ 智能问数API请求失败 - 客户端IP: {client_ip}")

        return jsonify({
            'steps': ['系统错误'],
            'answer': '智能问数服务暂时不可用，请稍后再试或拨打人工咨询电话：010-88888888'
        }), 500

@app.route('/get_city_md', methods=['GET'])
def get_city_md():
    """获取城市介绍markdown原始内容"""
    try:
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        city_md_path = os.path.join(project_root, 'DB', 'city.md')

        print(f"🏙️ 读取城市介绍文件: {city_md_path}")

        if not os.path.exists(city_md_path):
            return jsonify({
                'success': False,
                'error': '城市介绍文件不存在'
            }), 404

        # 读取markdown文件原始内容
        with open(city_md_path, 'r', encoding='utf-8') as file:
            content = file.read()

        return jsonify({
            'success': True,
            'content': content  # 返回原始markdown内容
        })

    except Exception as e:
        print(f"Error reading city introduction: {e}")
        return jsonify({
            'success': False,
            'error': '读取城市介绍时出现错误'
        }), 500

def convert_markdown_to_html(markdown_text):
    """简单的markdown转HTML转换"""
    html = markdown_text

    # 转换标题
    html = html.replace('# ', '<h1>').replace('\n', '</h1>\n', 1) if html.startswith('# ') else html
    html = html.replace('## ', '<h2>').replace('\n', '</h2>\n', 1) if '## ' in html else html
    html = html.replace('### ', '<h3>').replace('\n', '</h3>\n', 1) if '### ' in html else html

    # 转换粗体
    import re
    html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)

    # 转换段落
    paragraphs = html.split('\n\n')
    formatted_paragraphs = []

    for para in paragraphs:
        para = para.strip()
        if para:
            # 跳过已经是HTML标签的内容和图片
            if not (para.startswith('<') or para.startswith('![') or para.startswith('data:image')):
                para = f'<p>{para}</p>'
            formatted_paragraphs.append(para)

    return '\n\n'.join(formatted_paragraphs)


@app.route('/city_info/<info_type>', methods=['GET'])
def get_city_info(info_type):
    """获取市情概况信息"""
    try:
        if info_type == 'introduction':
            # 获取城市介绍
            try:
                with open(city_info_service.city_md_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # 处理内容，将图片路径转换为markdown图片格式
                lines = content.split('\n')
                processed_lines = []

                for line in lines:
                    # 处理图片路径行
                    if line.strip().startswith('pic/') and line.strip().endswith('.png'):
                        # 转换为markdown图片格式
                        img_path = line.strip()
                        img_name = img_path.split('/')[-1].replace('.png', '')
                        processed_lines.append(f'![{img_name}]({img_path})')
                    else:
                        processed_lines.append(line)

                processed_content = '\n'.join(processed_lines)

                return jsonify({
                    'success': True,
                    'content': processed_content,
                    'type': 'introduction'
                })

            except Exception as e:
                print(f"Error reading city introduction: {e}")
                return jsonify({
                    'success': False,
                    'error': '读取城市介绍时出现错误'
                }), 500

        elif info_type == 'leadership':
            # 获取领导班子信息
            leadership_data = city_info_service.get_leadership_info()
            formatted_content = city_info_service.format_leadership_response(leadership_data)

            return jsonify({
                'success': True,
                'content': formatted_content,
                'type': 'leadership'
            })

        elif info_type == 'attractions':
            # 获取景点推荐信息
            attractions_data = city_info_service.get_attractions_info()
            formatted_content = city_info_service.format_attractions_response(attractions_data)

            return jsonify({
                'success': True,
                'content': formatted_content,
                'type': 'attractions'
            })

        else:
            return jsonify({
                'success': False,
                'error': '不支持的信息类型'
            }), 400

    except Exception as e:
        print(f"Error processing city info request: {e}")
        return jsonify({
            'success': False,
            'error': '获取市情概况信息时出现错误'
        }), 500


@app.route('/ask_guide', methods=['POST'])
def ask_guide():
    """智能导办API端点"""
    try:
        data = request.get_json()
        question = data.get('question', '').strip()

        if not question:
            return jsonify({
                'steps': ['输入错误'],
                'answer': '请输入您的办事需求或问题'
            }), 400

        print(f"Smart guide request: {question}")

        # 步骤1：安全校验
        safety_check = sensitive_filter.check_sensitive_content(question)
        if not safety_check['is_safe']:
            steps = [
                '提问安全校验 ❌'
            ]

            return jsonify({
                'steps': steps,
                'answer': safety_check['message']
            })

        # 步骤2：意图分析和服务推荐
        steps = [
            '提问安全校验 ✓',
            '意图识别分析',
            '服务匹配推荐',
            '输出安全校验 ✓',
            '生成导办方案'
        ]

        # 调用智能导办服务
        analysis_result = smart_guide_service.analyze_user_intent(question)

        if analysis_result['success']:
            # 生成导办回复
            answer = smart_guide_service.generate_guide_response(analysis_result)

            # 输出安全校验
            output_safety_check = sensitive_filter.check_sensitive_content(answer)
            if not output_safety_check['is_safe']:
                return jsonify({
                    'steps': steps,
                    'answer': '回复内容包含不当信息，请重新提问或联系人工客服：010-88888888'
                })

            return jsonify({
                'steps': steps,
                'answer': answer,
                'source': 'smart_guide',
                'intent': analysis_result.get('intent'),
                'services_count': len(analysis_result.get('services', [])),
                'categories': analysis_result.get('categories', [])
            })
        else:
            # 智能导办服务失败
            error_steps = [
                '提问安全校验 ✓',
                '意图识别分析 ❌',
                '错误处理'
            ]

            return jsonify({
                'steps': error_steps,
                'answer': f'{analysis_result["error"]}\n\n如需人工服务，请拨打咨询电话：010-88888888',
                'source': 'error'
            })

    except Exception as e:
        print(f"Error in ask_guide: {e}")
        return jsonify({
            'steps': ['系统错误'],
            'answer': '智能导办服务暂时不可用，请稍后再试或拨打人工咨询电话：010-88888888'
        }), 500


@app.route('/guide_recommendations/<category>')
def get_guide_recommendations(category):
    """获取智能导办服务推荐"""
    try:
        print(f"🎯 获取导办推荐，分类: {category}")

        # 处理分类参数
        if category == 'all' or category == '全部':
            category = None

        recommendations = smart_guide_service.get_service_recommendations(category)

        if recommendations['success']:
            return jsonify({
                'success': True,
                'category': recommendations['category'],
                'recommendations': recommendations['recommendations'],
                'total_count': recommendations['total_count']
            })
        else:
            return jsonify({
                'success': False,
                'error': recommendations['error']
            }), 500

    except Exception as e:
        print(f"Error getting guide recommendations: {e}")
        return jsonify({
            'success': False,
            'error': '获取服务推荐时出现错误'
        }), 500


@app.route('/pic/<filename>')
def serve_image(filename):
    """提供图片文件服务"""
    try:
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        pic_dir = os.path.join(project_root, 'pic')

        print(f"🖼️ 请求图片: {filename}, 目录: {pic_dir}")

        return send_from_directory(pic_dir, filename)
    except Exception as e:
        print(f"Error serving image {filename}: {e}")
        return jsonify({'error': 'Image not found'}), 404


if __name__ == '__main__':
    print("=" * 50)
    print("Smart Government Platform Backend Service Starting")
    print(f"QA Data Status: {len(qa_db.qa_data)} records loaded")
    print("Service URL: http://127.0.0.1:5000")
    print("=" * 50)
    app.run(debug=True)