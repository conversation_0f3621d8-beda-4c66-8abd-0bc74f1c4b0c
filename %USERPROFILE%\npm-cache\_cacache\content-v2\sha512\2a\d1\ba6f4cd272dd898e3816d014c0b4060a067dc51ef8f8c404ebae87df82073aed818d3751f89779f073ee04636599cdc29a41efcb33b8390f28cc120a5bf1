{"_id": "object-inspect", "_rev": "61-3f2fceaf24235f7beee6182c930d1cd8", "name": "object-inspect", "dist-tags": {"latest": "1.13.4"}, "versions": {"0.0.0": {"name": "object-inspect", "version": "0.0.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.0.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "992c69fa4c6158240faf12031e685aecd5c36501", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.0.0.tgz", "integrity": "sha512-zBtPmG3o8FEUIcB30DkCP3eK2NXJ7atNjg4+uHlUalgEN0kdg43blyqvMgprDtEBWjrjbnlWNTPEsvU260jfLw==", "signatures": [{"sig": "MEUCIQCgPUoOqHq88AhztjTCNT3OObBabCh1jznh7sd5E+JP1wIge8TQubQcYD+2cbAG2XezPKcagm5zCfPa3DLiIfeMUrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "string representations of objects in node and the browser", "directories": {}}, "0.1.0": {"name": "object-inspect", "version": "0.1.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.1.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "221833869b4ed7db0cc90cb1052d802e38151ab5", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.1.0.tgz", "integrity": "sha512-tf3+N2tHnMnX5PnX3HXCj4sILW780Y+0h7AkZhAPthl4hp8GE6LaHfIIaAZvfSXUY4GG1SeqJf0G847fQnl/SA==", "signatures": [{"sig": "MEUCIQDha6BHLVTrnAalwsChRuTpnqDMI/kV1j9YpBe5rwAWTwIgWO4lNz5wjYidr/6/twxRMQQ485reTlyKkSD/Pdf5ncU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "string representations of objects in node and the browser", "directories": {}}, "0.1.1": {"name": "object-inspect", "version": "0.1.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.1.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "b22c0b1d096630af9f0e760857b0e16154473799", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.1.1.tgz", "integrity": "sha512-7xKoEhjbEVc5iVbH4TLEL8Pxj/W+0yQqb32fOBlsvI/quNNhNMj+Vb3qnLPEbZFEBQkZq0jDczPokcBpxX7YZQ==", "signatures": [{"sig": "MEUCIEdBWhwsmBKDyIGUQdC5Ll2kj4lcXT2rN7kjLW4r4jmEAiEAy1Xpcz5NzWxm4S76xL+9C5CuMpxNJCEhjidhjV60P+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "string representations of objects in node and the browser", "directories": {}, "dependencies": {"tape": "~1.0.4"}}, "0.1.2": {"name": "object-inspect", "version": "0.1.2", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.1.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "238f8accda0185263c7ade56046d13f09128487a", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.1.2.tgz", "integrity": "sha512-5cGgxYbh1fUximcsdjQLBjo8iVgR8C1MhGeKK7a0GzbGHL1DiHQkdqidEPrIWE44w7Sy6SsmY/5GpS78tdc7Mw==", "signatures": [{"sig": "MEQCIDHakwyhW2Pso3zpLMjH+oTIICoo8MfB86zvdFoAWqfOAiAziXHCRq+9wzIVksqqc+QgY6ga9qRioDFD422XCPZbXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "string representations of objects in node and the browser", "directories": {}, "dependencies": {"tape": "~1.0.4"}}, "0.1.3": {"name": "object-inspect", "version": "0.1.3", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.1.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "d05a65c2e34fe8225d9fda2e484e4e47b7e2f490", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.1.3.tgz", "integrity": "sha512-E2NX7nhdlM2KDnFGDBZkNoGpCQPj6xqh9+YT8knUuDu4rCF6fRlz+FEMCC37iwHMXuh6J7MoZXW5b9BXQmMOhA==", "signatures": [{"sig": "MEUCIQDci5CM4+kYeubuurCTZL8N3XG21bpxP7Lg0mio9QU5zAIgH8p0/WAQKiPgFLYc719/T+PI5mqWrkfsHNlxliCfdC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "string representations of objects in node and the browser", "directories": {}, "dependencies": {"tape": "~1.0.4"}}, "0.2.0": {"name": "object-inspect", "version": "0.2.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.2.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "826a2044a7116f2d4e3e20628cfaea4398bc2090", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.2.0.tgz", "integrity": "sha512-tpJ2+6DwDAfnpDzqrgJ5fXUJZMABRx0mZh8/8oTXNJpANnwLTGY7mOBBbvn83TktSJl3b6YWUoZY+rQRi1WwQA==", "signatures": [{"sig": "MEUCIQDoumiCWphE+PPPJsXBCuDNWePr557NehJKETzn6r5IyAIgNaaOAOckOeQFxfBn4XGsN0SJKDEyhyAJaBIh73FOSlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "string representations of objects in node and the browser", "directories": {}, "devDependencies": {"tape": "~2.6.0"}}, "0.3.0": {"name": "object-inspect", "version": "0.3.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.3.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "59345a21b6f9c6053ad621024f08179525be6ce3", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.3.0.tgz", "integrity": "sha512-E5EYmJSHrgSBa+HZtvsVkMhRfc+QJcAmU5jzbRqEpLmqTECmQu9II2F9hLuN6t5PUEYIBZl3+nceN4+dvNWwrQ==", "signatures": [{"sig": "MEUCIQCKhLrvxDyOEsQNbeN3eVpKVD9CQ75Ky6vUgBKr34WVQAIgf4w+ySm+jLS3FMBo+Xq38ZX1ByOw6d4WYIPg5nbD1gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "string representations of objects in node and the browser", "directories": {}, "devDependencies": {"tape": "~2.6.0"}}, "0.3.1": {"name": "object-inspect", "version": "0.3.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.3.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "39fdc8ca276408a795f5c736b2c44cd04c1e76a8", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.3.1.tgz", "integrity": "sha512-DXEgd5Db7gq1UtL7cUBSVZeB2ZVL1vu0ut53hmqorhBzciv1d9b+CkhJqdBgupDbKAJ3uCEcEi9l33e7UqfDZg==", "signatures": [{"sig": "MEUCIQC56IVoulwQ9FW/qGM8VfDCUJNVMckQoJ3xO/NKD4WVPgIgP80P0E+63jLAJP8eQNE0t+Cz5ZH7nqmdxw4rQTa5b1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "string representations of objects in node and the browser", "directories": {}, "devDependencies": {"tape": "~2.6.0"}}, "0.4.0": {"name": "object-inspect", "version": "0.4.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@0.4.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "f5157c116c1455b243b06ee97703392c5ad89fec", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.4.0.tgz", "integrity": "sha512-8WvkvUZiKAjjsy/63rJjA7jw9uyF0CLVLjBKEfnPHE3Jxvs1LgwqL2OmJN+LliIX1vrzKW+AAu02Cc+xv27ncQ==", "signatures": [{"sig": "MEUCIBMYAz2ni9/ZmB2NFpaUevJVf9Mbuk5hLA0RuHCdfHUUAiEAg4GwsKfPBl8jmzWPH+dk2py0ZYbyTfu7nbgxsRAd1lI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "string representations of objects in node and the browser", "directories": {}, "devDependencies": {"tape": "~2.6.0"}}, "1.0.0": {"name": "object-inspect", "version": "1.0.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.0.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "df6c525311b57a7d70186915e87b81eb33748468", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.0.0.tgz", "integrity": "sha512-YAoLcg84V6Qj6H34cpF6qH9kn9kjJ+Xnc5vHFjt+fzEUNO4tstfmtSlUq6EgvFYEprhcAYrY2RWUifjamBalPw==", "signatures": [{"sig": "MEUCIG/3V8F0N+Lk/9Yo1madCWsa1dLlgyKbIZK+ZJoj2+P+AiEAqL7q8ttZHoG46Qo9xfKzbVcIg52idjCj/BDZ1jXL/xc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "df6c525311b57a7d70186915e87b81eb33748468", "gitHead": "d497276c1da14234bb5098a59cf20de75fbc316a", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "string representations of objects in node and the browser", "directories": {}, "devDependencies": {"tape": "^2.13.3"}}, "1.0.1": {"name": "object-inspect", "version": "1.0.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.0.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "fe8cdcf326ab2d23c26a6fd5a9d7fd5272c2259d", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.0.1.tgz", "integrity": "sha512-WulCqqD8MNxAbSUHpjPPz+NMiS/IYSFN1b1oJtlIISVzB9xbmkATSX4rMxxpchmGx6v9h+D46daQGj7w7cqyxw==", "signatures": [{"sig": "MEUCICq2InieAMk8hSlFK5q3T0dJd/2aWSC1SDqzCRlO6d/GAiEAnVu4iaM5u428bFYRvoWpZieP35700kPrMap2ebJKndA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "fe8cdcf326ab2d23c26a6fd5a9d7fd5272c2259d", "gitHead": "60a6d82e6e2239f617d952a2d015c592bc551d6b", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "3.1.2", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "2.0.0", "devDependencies": {"tape": "^2.14.0"}}, "1.0.2": {"name": "object-inspect", "version": "1.0.2", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.0.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "a97885b553e575eb4009ebc09bdda9b1cd21979a", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.0.2.tgz", "integrity": "sha512-Bg2UlB5dtn0VQ2BvuisvthOUh9ehgL4FQsnLoXvY2GMJKfwNC6nZO0NFs7llpH6yZJcRUZZAoSb5VSnRSw17ww==", "signatures": [{"sig": "MEQCIDghLSFxTI64pgVNN2XM1J0u3C/qs2MPyRq80fy2LeUrAiBYln/cHIk9LqZEuniJVz5jzBNhgcu9WtW7Z9r01BCqAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a97885b553e575eb4009ebc09bdda9b1cd21979a", "gitHead": "51c683fac155993d9cb3542b18b8fe084dc6cde8", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"tape": "^4.0.3"}}, "1.1.0": {"name": "object-inspect", "version": "1.1.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.1.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "34bca644a80f94f8ba41a0f9d0984eaf63acf14d", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.1.0.tgz", "integrity": "sha512-3lB1I/qcLBbfnLB7xMQJ5SUXZ1wvnWgnmDE3r3tOF20HNH832fUzBiKO3/H4dY7674shfq9XjRgPpfd6dz1uMw==", "signatures": [{"sig": "MEUCIQC1SW/sNSaF1hUF3ihyJvd+KLpELAbMUa7c+MTRuwn8LgIgUq/yikXEtdbMANSeVZ3USFKLVwhO7N6LNtIK9c2hwjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "34bca644a80f94f8ba41a0f9d0984eaf63acf14d", "gitHead": "559ae4cf0785152a1296fef6cf49f4ad8572b6bf", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "5.1.1", "devDependencies": {"tape": "^4.2.2"}}, "1.2.0": {"name": "object-inspect", "version": "1.2.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.2.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "61b0ddcb9473755b87c2bb5dfb30a9475f111914", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.2.0.tgz", "integrity": "sha512-DzcHVitRu5mmaHk4DO06uw2rxzJ61ngj9hQ0xOl7aF78tZbUilf5RQi+ilbyKmuksKBBRsY7kPSzocluAIBNaw==", "signatures": [{"sig": "MEUCIQC68DDYCrhWF1w3LL3asvoqO32DkWzb4P88dIToK1vY6gIgXmn1//8XqUH5k6zOp7HyyeV89a22u8v0MpWbDn+4xdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "61b0ddcb9473755b87c2bb5dfb30a9475f111914", "gitHead": "fc5fe1e33abfb5aebc31809d53c5e4a5d8daa55c", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "5.10.0", "devDependencies": {"tape": "^4.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect-1.2.0.tgz_1460185839695_0.4408472578506917", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.1": {"name": "object-inspect", "version": "1.2.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.2.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "3b62226eb8f6d441751c7d8f22a20ff80ac9dc3f", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.2.1.tgz", "integrity": "sha512-H2Ip4Kk2b7exN2+PNFjxF1MSSECoBmiVWxfnAQ8+tUxBkTQs2+UwFzkYGzICsjoSMYoWjGVruJgLCm6YqjmFrw==", "signatures": [{"sig": "MEUCIQDLw+ElBTqthZ/ubUin1Tvbc+Uuki49yhlhFQsZu29cYgIgGvVNDpnTEMj3EggELBP01h7LdyEu7hB5npG3Byq8/vg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3b62226eb8f6d441751c7d8f22a20ff80ac9dc3f", "gitHead": "bd0329aaaf501de7bf2a6c5d714f533ced5e96f1", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "5.10.0", "devDependencies": {"tape": "^4.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect-1.2.1.tgz_1460217450014_0.6012979652732611", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.2": {"name": "object-inspect", "version": "1.2.2", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.2.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "c82115e4fcc888aea14d64c22e4f17f6a70d5e5a", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.2.2.tgz", "integrity": "sha512-6txw4f05p1h+DpK15sLBejEVKLmtf9yCQ9BFa6U7xSinurwgBt0Awv/rG+389zbstgrkUEOnMeyBPdYWgrd86Q==", "signatures": [{"sig": "MEYCIQC9b0flW8i6hAIry7GPsdbY+Lf+o6V7Jw7C2fJABYXNngIhAKrM4ZP6H5b0w3li+0vP8EXa5zUfulr+EXQO7OV1J1JY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c82115e4fcc888aea14d64c22e4f17f6a70d5e5a", "gitHead": "8fdeb49366a1cb5885a525f7a5b2dbf8eb5ed555", "scripts": {"test": "npm run tests-only", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "7.7.4", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect-1.2.2.tgz_1490409748569_0.4387361933477223", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "object-inspect", "version": "1.3.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.3.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "5b1eb8e6742e2ee83342a637034d844928ba2f6d", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.3.0.tgz", "integrity": "sha512-OHHnLgLNXpM++GnJRyyhbr2bwl3pPVm4YvaraHrRvDt/N3r+s/gDVHciA7EJBTkijKXj61ssgSAikq1fb0IBRg==", "signatures": [{"sig": "MEYCIQCYnNdxrtDsYsjx6QXEtQ52bbRYSX0hTGkMvwc/L4LflgIhAIY2tXOQ5aEQrrnvYFhC2bYBSHdkIWt0EnIHrvo6dyUw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "193f504913532338835ec795330e9479dbf34f99", "scripts": {"test": "npm run tests-only", "tests-only": "tape test/*.js", "pretests-only": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "8.2.1", "devDependencies": {"tape": "^4.7.0", "core-js": "^2.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect-1.3.0.tgz_1501540637892_0.1793239014223218", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "object-inspect", "version": "1.4.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.4.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "7bcd84fd8db2e833285282bf8356ed87dcef7cf8", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.4.0.tgz", "integrity": "sha512-6QW38aBzieGzJqBaq0VBCgVMuluSYAHMH0xR1CMGfVU3mWLimWhkP97NGBLpJZXYDswaY+Qbo7ExFMTE0w/6zQ==", "signatures": [{"sig": "MEYCIQCQKApyoY9cKCbgbFFL/mjZ8KqTRmgWZlsA4aQBroLhMQIhAI18FySytvS/GF6fYaa1595gHfoeUOcDf1qvG5LfF+WK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "browser": {"./util.inspect.js": false}, "gitHead": "5ce2c778eca346357f446eb325d9aad8bad5afa1", "scripts": {"test": "npm run tests-only", "coverage": "nyc npm run tests-only", "tests-only": "tape test/*.js", "pretests-only": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "8.7.0", "devDependencies": {"nyc": "^10.3.2", "tape": "^4.8.0", "core-js": "^2.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect-1.4.0.tgz_1508914217171_0.3441071610432118", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "object-inspect", "version": "1.4.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.4.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "37ffb10e71adaf3748d05f713b4c9452f402cbc4", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.4.1.tgz", "integrity": "sha512-wqdhLpfCUbEsoEwl3FXwGyv8ief1k/1aUdIPCqVnupM6e8l63BEJdiF/0swtn04/8p05tG/T0FrpTlfwvljOdw==", "signatures": [{"sig": "MEUCIQCtItckmj1SjuXQRqaVDJP/1cyFXArmHaKWaQ5M6+ADgQIgevSRP6ungeYH1aG1GTau51NV2lQU+iYxL/1UOaiPnzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "browser": {"./util.inspect.js": false}, "gitHead": "a77008fa48496b26ae3f8df1c23187be2e4a3cfe", "scripts": {"test": "npm run tests-only", "coverage": "nyc npm run tests-only", "tests-only": "tape test/*.js", "pretests-only": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "9.3.0", "devDependencies": {"nyc": "^10.3.2", "tape": "^4.8.0", "core-js": "^2.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect-1.4.1.tgz_1513754585610_0.6385269251186401", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "object-inspect", "version": "1.5.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.5.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "9d876c11e40f485c79215670281b767488f9bfe3", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.5.0.tgz", "integrity": "sha512-UmOFbHbwvv+XHj7BerrhVq+knjceBdkvU5AriwLMvhv2qi+e7DJzxfBeFpILEjVzCp+xA+W/pIf06RGPWlZNfw==", "signatures": [{"sig": "MEYCIQCKHBHmQYcL8/klL1yr+eJRsSQltmGRQ9Sscrm3nC/zUQIhAO0I2Xc+PTYPZg0PW8GpVhwL4w/N3iDV3hV2p+88Ie8p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "browser": {"./util.inspect.js": false}, "gitHead": "ff3f3275ca14d80103461c8892ed164fea0741dc", "scripts": {"test": "npm run tests-only", "coverage": "nyc npm run tests-only", "tests-only": "tape test/*.js", "pretests-only": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "9.3.0", "devDependencies": {"nyc": "^10.3.2", "tape": "^4.8.0", "core-js": "^2.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect-1.5.0.tgz_1514274544888_0.496770242927596", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "object-inspect", "version": "1.6.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.6.0", "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "c70b6cbf72f274aab4c34c0c82f5167bf82cf15b", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.6.0.tgz", "fileCount": 27, "integrity": "sha512-GJzfBZ6DgDAmnuaM3104jR4s1Myxr3Y3zfIyN4z3UdqN69oSRacNK8UhnobDdC+7J2AHCjGwxQubNJfE70SXXQ==", "signatures": [{"sig": "MEUCIEGIvOWPwX4U2xbq/kn811DXtlI8KT2TUqi6BeP1uBT4AiEAoU45WSxVJteQfU9TaR6O1HalXYfm1Jn5BAzJF1vuVH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6kYBCRA9TVsSAnZWagAA20cP/jcCo3zf6qPgSjnt1XSo\n2AIc6fk6K7ozP6kQ29/zLngrhN5oYAHrFZzwyIzoFNjQdHGFqpE18z2vFfir\nARcvV1URWpwSFcLrydp0HlxwmBT4GvIH3sQFTwxbSBF/R6c6XmDp//fZk5U4\nxonMjW77x4KRiX3FZgHNDsiQzjYfzfOVr9k8ab5xHRRsteOMia/GB/bDbZMj\n+nY4j2b4lsmSUQDJTDzQaEYzV2GLMQfMJKaxoIM6Yye9s51b34ER7EbKGjP1\nXLwH3cl8rJwhbRphYR3xlMDS0Jg6wM5J0+AChEIzXzGThsivK3J6bCSOydTD\n3Xyy5Yi/YoB4HvDyXLkBlRu4ebpuqg/p0vBmM3e2QAfSLTh1UtVpaCKsEkQA\nJSgG0+iNAEx18ZH3C9KSnSklj1iCRCE+jopAfkusIYwo8u7HkmdGBJoODVXR\nOLoAv9lVHKkL58wNWtCDkvA3ICaVVj6uYLRbrgfJBy5gqZJabpnD938X4wPr\nyZsCsgtuqKz64WrO2+3TX01bWZSNN+WrH1G9luUD0aNXLqPnB4FZNu/j8cC/\nJPMtLv4nhyvvyY7rH+P3icYZpxTqHJ7FSwEDDkN9uL7c1qoJCVTBTtGccgC+\nRo4/DpFN1eqbJQg6E56g8Cw6clrIIvqo0A2qiM+nBFR+Ko/kNyDO/Rnz1hMy\nWVE5\r\n=owks\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "gitHead": "0ac0417c8898a9b02700ca724db831ec74748100", "scripts": {"test": "npm run tests-only", "coverage": "nyc npm run tests-only", "posttest": "npm run test:bigint", "tests-only": "tape test/*.js", "test:bigint": "node --harmony-bigint test/bigint", "pretests-only": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "string representations of objects in node and the browser", "directories": {}, "_nodeVersion": "10.0.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^10.3.2", "tape": "^4.9.0", "core-js": "^2.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.6.0_1525302784581_0.17553129095753994", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "object-inspect", "version": "1.7.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.7.0", "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/object-inspect", "bugs": {"url": "https://github.com/substack/object-inspect/issues"}, "dist": {"shasum": "f4f6bd181ad77f006b5ece60bd0b6f398ff74a67", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.7.0.tgz", "fileCount": 29, "integrity": "sha512-a7pEHdh1xKIAgTySUGgLMx/xwDZskN1Ud6egYYN3EdRW4ZMPNEDUTF+hwy2LUC+Bl+SyLXANnwz/jyh/qutKUw==", "signatures": [{"sig": "MEUCIQC+XbczEGKAhe99OEvGU4coD3tewUOW9+hSYCNqp3rxzAIgEp9YEUK+k2fkoLyhqY4w12mgoSOgy6VErB6RA/N7gYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyPgmCRA9TVsSAnZWagAA4MMP/ROXXurV7Bri7f2y6szD\nDlMUCXLkJbJqrCFiYc8RxB6CHqzD4A5tOqQfMUGu1n2PE2bIMfxH2TdvS9uM\nFlY5TT1RNKNqUGwO0TZ4P0KFQcp2wWDwlKITkUWatVq7DGgLDUnO4XPNGBlZ\n2/j8Ywb2GHQY5jgWXABW5LXRa/8AE/cygF/uGrSYBvHU3PVW12QcFO9KnumD\n3evFrtyR+6jf8gsuPjP9q5IuJqCB9LL08m8I/o3UUjidlocmUjCF5wYPnh7U\nn2rFkfiiSYOs3Di1y4kXdmBCXlyOUinpI5hhQLOmpjtS/eZ0EtTLylEVy7Zv\nNxCTuT71yjO9uAaV+Ae9tGDDoya4VECuDsVv3IWCvqd3obgefMVeBDbpyQqz\nSBcUcswCE3GZ6ki6Zoa41sVlH3033emEdmjoaY+SgdfuMqmJfYW2nGxDAHv9\nMPWFPUAsIda2Fx1axZh2av+dfL9EBw4xKr8diWkZ9S2qdkkP14AvxlxRytcV\nM0QqjcvwCkH22tUZl2BXjOv+2WmfWhWfR2mNMfM2DgomFLvkRvjyPg4BbIiJ\nTSVjSdENh0P8I66dk3JbQ9TK3NlumXAYJ+veMQO7LwxKWclswpOd8Y7plEz7\nE1SvGsofVbQJwl9qfYF8siNQBM7CBl1a4k6JxHrHGp1uGlI5sSBZiNISwmX7\nB+E8\r\n=sCdK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7dd1224072239f08bd5dbb82ae5e7120d88ac562", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "coverage": "nyc npm run tests-only", "posttest": "npx aud --production", "tests-only": "tape test/*.js", "pretests-only": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/substack/object-inspect.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "13.1.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^10.3.2", "tape": "^4.11.0", "eslint": "^6.6.0", "core-js": "^2.6.10", "@ljharb/eslint-config": "^15.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.7.0_1573451813952_0.8528557885389199", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "object-inspect", "version": "1.8.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.8.0", "maintainers": [{"name": "emilbayes", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "df807e5ecf53a609cc6bfe93eac3cc7be5b3a9d0", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.8.0.tgz", "fileCount": 31, "integrity": "sha512-jLdtEOB112fORuypAyl/50VRVIBIdVQOSUUGQHzJ4xBSbit81zRarz7GThkEFZy1RceYrWYcPcBFPQwHyAc1gA==", "signatures": [{"sig": "MEQCIAtu06WIhNkLAwHa1SzOnvtD33vnBYJ+olpXzbV46fTxAiBp2FfyQg07Zb4Fi/kyAiMYRtv4GTzpm4rsgQ0Meg14jQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7FWuCRA9TVsSAnZWagAAApYP/i25aQeNYlw74vM3OQVK\nMa1t7wPH+qMQrxNm4sxKf9Z/iA9nkg7o/DDEX2U8z36KRkU26RbiaTjgVitq\nh3Kq0SyMxAgjar0f4Od+Y20tHWPuDFPTDycg7qqttsSr02oOivhaQjBQdLzN\nCtNWGMTkH/C97xIWzkOjU/N2L8sUoCLN7knRxNHyyX5weOwN+IZJH01SNJ+G\nhx5FnnKrZneqRSxz/Qo3CDHr3iSZEcOf/mVrWnfN2NA8gUd/LHTk39tPMTlt\njFrb7QFzJyhz/v2u4RhTJxfhaa+LJ6UW4gl2F9QKXQCu8Gbd3SmClQfI6Vaz\njGRfpDN5/qKmyLeqWM2k8tbmO4U9x5IlNJaKf0D+AItLSZjhkFttSgycyuRU\nUS66GtVApefUaX7iEFrESkmJA1QW7ZtNr5MN5MTkb0qN9NKAErHMZmT3zW10\n/Hc2pZ/EQH6XTB5m1pZf+Auxk50nxAOtM/+n8FswrYj9g7neWdsRPJsZL/8s\npt6rUQc5cc/0FJ25YYCsI3VxnRblDY9hF6l8RrYRzCsWSghLuFn6hq4aoAW1\n0BiJI+LB48BWae28OsLvZy+FXbqQgiAPHlpL6q+mnxxZjAZUbZv+SYjO9J0x\ncWgaOjp7SJkqAy1bZ3J00MQ1ymHirRI+uYa7w3UJYjB6V+4/FMoxf0elwx7I\nVeuK\r\n=UsQb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "6b0a54dee12b2361056590a1d3a3feecac3f7db5", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "coverage": "nyc npm run tests-only", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "14.4.0", "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.2", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.1.0", "core-js": "^2.6.11", "for-each": "^0.3.3", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.1.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.8.0_1592546734099_0.5394806680132709", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "object-inspect", "version": "1.9.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.9.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "c90521d74e1127b67266ded3394ad6116986533a", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.9.0.tgz", "fileCount": 36, "integrity": "sha512-i3Bp9iTqwhaLZBxGkRfo5ZbE07BQRT7MGu8+nNgwW9ItGp1TzCTw2DLEoWwjClxBjOFI/hWljTAmYGCEwmtnOw==", "signatures": [{"sig": "MEUCIEMaxUkvpWmRm1wpfR4QKAG5QG8bQlWpqsSqPSbiMaxcAiEArtIONv9FeOpUBE9TI9D+jEe7zZ6PQtsSjojaynbnVXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxeoXCRA9TVsSAnZWagAAKhgP/2U9buzSlr3IbGqwScyD\nRa4hT7K4+CKSibsKJpuz8ezWtlTrfWrZ8NtCSmnyp+BMKp1HWWRKurlYtWDI\n4E02RtWwU/gpoT+yxN9apWmLMd+u+Q2ybkt3f3hjFJcdH9mMy36eoBZ01th0\nqgRnm+mKPp2xNk/TuVbwkVvQV0uQ7Z7IObdeSUPpC9Ytpz3AjhLwlxhisVIo\nE62c0bqNA1oRj8L0mukazLPu+fKhu1CuUXFU4pHZnzkNCN748uKGy7xbzW4y\n0Q/jN9ARWn46xuU0VOfRHA/NG/x6lm5+dW+efdzXGZ4+lO4PTS1RK/DOiF1N\ni8BvwmIaI4oERrnc8xymaicTRbARD5uJ4RPE8oLAZmz+mLd0iEx6VjrDBWEL\nJwn+Qg7GSOalI470hj55VxXhsFPinvvbeVlvVnfSjGxWHgE3mZO0e4UpM1Hc\nXkLyxdDMIIbJBUtWZpflAh2pcPVl5KnE3gTj8U+bS3CssP0DjaWeYkK1Hfnq\nBfYtqZ7c9LYe6CxytyLqqu8rpOQXomQDPyhM0NFTmzT2blSg9Qd2tvuNuEGj\nFcSxg3lGmg66Blvjv45vzoT7Uihipnfme8AC7EqkIlY/t9P4cIqxQ4JvaZis\n4R7eTjMZjDRcmgjOEsedFQ997A6O6xhdFYhbnBgIKrpPeQIW7TrJSTGmx9yO\nRyoD\r\n=hUTD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "33bdee95b862bca5f9db68d9b10b01ca59932e0a", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc npm run tests-only:tape", "tests-only:tape": "tape 'test/*.js'", "pretests-only:tape": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.14.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.9.0_1606806039180_0.8694035315303734", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "object-inspect", "version": "1.10.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.10.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "b7fbe0ff392e09ac138f9d0552920f0c5697a4d1", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.10.0.tgz", "fileCount": 32, "integrity": "sha512-9LnIbH6efKf0CeEZkb3ny2C9fuPIssucCN7UlI60aX68yl40ZUhvECEGhXOlpH8cbuoQ9ex8R4tAvewbVINGOA==", "signatures": [{"sig": "MEUCIAk1gXf9yoP9IR83AR/Lp/HJmG7codyFTv9IDT+Duym3AiEAnBM8Onfwg/hGiaoLh82vFrNCNat+PQmStdsghqkNmLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge0LvCRA9TVsSAnZWagAAm/gP+QDVmyWCgYAZ2pakgkoN\n4roxz5kLUoVNHYVJS94x/EeEJLbPKj9IkspJu7MzUcHEf1c5uPxEbmQI1OAq\nj9tUhE3f/G+5bd0kqZXJBkh/BdgtMfY9Wzuvp08GppZhCZ1VbZLzaM5h92WJ\nu5nCY0+lDXT940t+QqXByKrQLFSEsdcE80cJSPiig7bYjM7pg2K/a4LtOBv+\nHOhP1KNaVzefBCfHPu63vx/lupY3xN5QQg3wdeCjHpvD/2Sx4TD6No9ouPsu\nsHfX2+4D6+4Z56xkaS1pAh6IGzo7ICLMMfbnEpr5zxcOMv2iGk9yffxIgGqX\nChncEsAtTy0K42caO1ppcTbHTqLb0Wn4QV/0CoABxUxUXv6TokvAWuU1IK2L\nmiObDLWBcWUl6b8XH82P23oGLLyo5eVG6v+trBnOrTG+4eMmuB/8VNfdwSm0\nj3d18QFBIEk/nN5dIlS60Z5fLYCM3Fgw138+zdMUek4uvc5/6Ydl+rf9eRlq\nigeIrdWG4j3I3S8TeqC0O2tZsHVlEpNoq8i03UUvuvfwCcs8Yq1f0st4lpXM\n2fjOcw4dLNxt3Mn3PgI0HRdoZ5+hYEEY3doL49UYM22ERRUniE9sbMlOG+An\nV0oGbvb07Gw0G1uodm5pZ24zXoK72h2DHMyPjX0g8EDNz0br5eRYxIE2cx8f\nR6lm\r\n=qAsb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "15e6c3ee23ecc359913fd95be98223893f8332fd", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc npm run tests-only:tape", "prepublishOnly": "safe-publish-latest", "tests-only:tape": "tape 'test/*.js'", "pretests-only:tape": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.24.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^17.5.1", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.10.0_1618690798597_0.31163514189013886", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "object-inspect", "version": "1.10.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.10.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "b69d9494d35a56387bbaebbae61c55510d877a1a", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.10.1.tgz", "fileCount": 32, "integrity": "sha512-WQUIkCSDPWm5ing/PTUkLr2KaOXX2uV/vz1hLGW2XbZ/RDUmtgcsOyEqA1ox0rkyNx9mJX4kxX+YWceje3pmag==", "signatures": [{"sig": "MEUCIQCDSkp01trkxcRoOvOxMOY8b9uNHFSUjfsGazSrUrbblQIgC/nHxO6B9XbUsDs8nWXHl+Yp2IhpWpJT2Wk+PQsGBgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge1V9CRA9TVsSAnZWagAADagP/AqFoGT6qZEwRdjD+Mw+\nO74nzr+t60HrgCJuHEUNaPBqIsluO4BXubHPBZz3iVJNVbaWHayob3tJoPZ7\nhQPaZdlYi72eFhWggkTyKoYUZfxh/D2dUX0CXM0isN/r5wfViT4wR48ZyGuG\nbltUyc+fynCsMAKMCb3KJqEieyvvgjaAEm7hHvDJIxksNMDwUIXcN+4Fy4Aw\nhGSWAoosm4kmIQMDT4EB1QhVfXQ1rMyludnng6dIZii/UlC2WzkmBsldEKW6\nq162okkaOTF3lwih9jvUrXBteT3JXpLSeECkarIxB+n2ZSkdglPy2cmK1QtH\npYZXGgalK3eG3qCpqYVtfx7XIUHMHZopjGRpABEiTCJPWLd0dSYpd9kvJpP5\nL/o9bAJKccKfN2nYQdJD1A3ElGopK1UBZOFzI7M02cs3IieoY6g+RaTtTbeZ\n8fvDP0tnW1vQieabW0WMIljKQwvvDVnB0xhWTrWvST33xNPe7okYY8VgQqUY\nQk5y12vapJPaCy1oifxRfg5WhpP8Bwvi8yDIUPMmSLSFbIgTU9ZuiPnrYKBn\n4ZiInfIvmQpcOSoOh2sdZeAhCfQXT2nnfOjbWYMSbn34rU70Pl5KcRiKsKBw\nJoMsohq/UMkMsvwfwPGzvMeOD7ldTShIn1pwYyuG7sevuwtNzgFTF8o/sZOm\nXZWB\r\n=57iF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "5753aceb7265943e8b363468ec01bdc3de265b7a", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc npm run tests-only:tape", "prepublishOnly": "safe-publish-latest", "tests-only:tape": "tape 'test/*.js'", "pretests-only:tape": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.24.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^17.5.1", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.10.1_1618695548843_0.8701138848953984", "host": "s3://npm-registry-packages"}}, "1.10.2": {"name": "object-inspect", "version": "1.10.2", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.10.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "b6385a3e2b7cae0b5eafcf90cddf85d128767f30", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.10.2.tgz", "fileCount": 32, "integrity": "sha512-gz58rdPpadwztRrPjZE9DZLOABUpTGdcANUgOwBFO1C+HZZhePoP83M65WGDmbpwFYJSWqavbl4SgDn4k8RYTA==", "signatures": [{"sig": "MEUCIClvhVxTwr/eAUmkRoU5CTbFaIKvrMU6Z4FlsY4S4Gq1AiEAhaCKiDS1J8wE0SCvoRpvBxNr01TLV71Qcwz/A2C6ZVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge7TSCRA9TVsSAnZWagAAQcgP/ikfKJtrAD02VnrWy45d\n4RpGxeOdazDdyER3zgVkwaNRdxRF/cbPUxOFNbkCH1oJPY/ZJDgTBz9c8Zvj\nac4JMixULaeWSHImTQnLcygzE4EzlJBS0ih1tmS8VJdYvdMHF4dCAwOtxQ64\nEgKQj/2YzUonqDikWiExjJ1S5yK/jxcFRxecy+Txl4wOs/xTmxBYGeyFsjyu\nCGxfONU+DdfTy+GYvL6YlHLr+DmMNs8Gcy+PDhgScuDLBSEIiF3cDEDYfkie\nDGDkMtm4Sys6iy2nhbvSPqzWY2S5Lj1ZdbhpriPkubZH9IwSU8NcuUMk2Ele\nDvAZgwddrWtgBysquCNNYszrkZpPcfwVCyLT9G7IPjkEgWE78D12Ps38Mm9c\nrNXKLCsw6Bdh3Jv4AcAVf12LEsAILhh2vMCsms9RV6nHGiDcWjs2FdWmw84z\neHH+X2X21lwex/wdMIALc79B+Woc3vm8a4JVKz1mhu9349RkwqU3DESFOgMT\nUqCrgJKhOcy5YmbgagePTVw1SgvSoKUch2QVCIeCDyNdglGOACVVRw+5+0Dn\nK//eT15EMTcRTOFNE1T88CLtUDf8hNuEAFr8474MWdXx2U+NSD6X7naUOHNL\nk9TbswZR2bEWWPXbAjZPCgjY2lON0RdzrQPy2yThAgBI4xpfgxVp2pjacscO\njP0b\r\n=MyXj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "728ebc2eae503b99faefe6cfef462918d7d39200", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc npm run tests-only:tape", "prepublishOnly": "safe-publish-latest", "tests-only:tape": "tape 'test/*.js'", "pretests-only:tape": "node test-core-js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.24.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^17.5.1", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.10.2_1618719953890_0.06338412318375486", "host": "s3://npm-registry-packages"}}, "1.10.3": {"name": "object-inspect", "version": "1.10.3", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.10.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "c2aa7d2d09f50c99375704f7a0adf24c5782d369", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.10.3.tgz", "fileCount": 32, "integrity": "sha512-e5mCJlSH7poANfC8z8S9s9S2IN5/4Zb3aZ33f5s8YqoazCFzNLloLU8r5VCG+G7WoqLvAAZoVMcy3tp/3X0Plw==", "signatures": [{"sig": "MEQCIEzE7S4Ylpote/+a2lyaAtYW/dfcUzFJc0LB1N2NoungAiB3qtSrqykgdr8VXWj3PpLEf97HykfpUomcSKi8jLDerA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglgpsCRA9TVsSAnZWagAA+MIP/jE2jK1ciIUju4hW5rs1\nSMBZJfI9H69zqx3TsLbsLdbbSKy+yRlaHh3qST0/c5zF7Cr3eBd/NcTAV+SH\nB3fLBSex/s1gYQlNIpTvlBiEqEoRHI1xTzFS64yM/hE3kKpuKWbbeK+rqMj/\nn1zcNJwV+apJUhyyr/m5fexjGTG2CUfFQ/W60YCPyJMNLuFr4cztwmdJ4BHP\nGHjQn1vhnPHNps2sFyfJ8s2QqaACa9I26eLuCo2k4e6lnonXYefBxw8tCys8\nJ4eDDl4mrkQpe+i4V5h85HebECnHclfZz3c8Yvv6brMS7b7GLiVrgs6Vz4c1\npwA3jeiHHZgYZpigL561Cyh/4pCGJQuwi7qnUQziSHvEf+bQs03dSONphoTX\nam095MNzNLnLn9cu/zC+vcXEEnOZJ5iEWDAb3y5dKJ26XlJZppKHzIuOvRp7\nCtSKTNxzalp2FwkLFeDaMeWe5rk1qbL/xGHUPhIDDPVTYl7Jnbr0N2b5lZfg\nCbEK2mpeAn4Q2cgdTPMlR66VahaUk7exFH4wDWmktuuSmEmcUISIzjEV4tY7\nu3/aWokjU3WVuL2Va/RJ4vv7VUiYIUhGCC7cfvrstEfl8NjcTfXFUwF4FxKT\nSGbBMzrJ2dKyqQpgr3nV2ZgX08kfRsU7ZeA8LhemLHRYDcCjjISpjnm9D+Lp\nVrzR\r\n=13WU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "45c20d6c06c733d6c420bc790832cf57534277dc", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "pretest": "npm run lint", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.26.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^17.6.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.10.3_1620445803828_0.3122229095742348", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "object-inspect", "version": "1.11.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.11.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "9dceb146cedd4148a0d9e51ab88d34cf509922b1", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.11.0.tgz", "fileCount": 32, "integrity": "sha512-jp7ikS6Sd3GxQfZJPyH3cjcbJF6GZPClgdV+EFygjFLQ5FmW/dRUnTd9PQ9k0JhoNDabWFbpF1yCdSWCC6gexg==", "signatures": [{"sig": "MEUCIH1+8IhHkrorZQaN8495RWQZrKSMkcQ6Ar2HSEQkax+yAiEAkhU8LWV9zlj1l4eQvXH3nndaF8lA5tVFdReku0CHURY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7KEXCRA9TVsSAnZWagAA91IP/2Mno/TOnXsVwf6deejY\nXJtouXQXRpiWQplzf8yjNxkSvsRrwdqNeFCbMIGWUVzVuZHqZQGV/Xu3Aw7E\n+wwFOJL3J3Dj2Pw1SJjUBVIKfA1N119er8ec22Yk69Z6W9cGIaENpPp7ZtEH\nR7BmLEf30eA8nIZMk0dzwuqrhALGXLWStquLYrYue+dKGWE+t0JURd4MQDqL\np+pmXxCMxmKVlfFXQ30BxA+6wqraX47vnbixf/AaYd+ga4b2mHqtYi3KU1lP\nzSHuELE3pB+U/7gESHWVJLkKYrzYEcL3pwKNJwIzERMZYWcAu9JCbWyCWH8t\nmnWH0azSLoQKK5/9zsjqEppA2zIUtHQCthn2zEPclYLigkwg39E9JiaIPasg\n3VDd47dE/ali3Hrt5BxvuaygW+aAmpuPURviMinYa/oWgnSemj+rtltTgM9H\n6L/NVAsGpnyPvcQXeeKLRR1MngbEaEa9Af/5T3tz72v+R6Lb5KWpj8C3lElk\nUo0ZDVRhZtNvwv48Sq9kZKZ3HE7lAfR9SGwxHHnetolW5DmhiqIgCCw6z73X\n6l7z/5Yi+Hsutf9NNN4Qi6q2BSJ0e7ZdLxVtrEzJ1vIQEZD/LJPMAgeiPnYI\n8mqCCsUPrGayZcjBLG9GNJzuHQIDadTd4dlEwytw9agMjPR79X3L7Z0vK80a\nLlRT\r\n=HkEE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "dcee6edfd0e216ab0139242668079bff6310fcf0", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "pretest": "npm run lint", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "string representations of objects in node and the browser", "directories": {}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.30.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^17.6.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.11.0_1626120471172_0.5363827404838917", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "object-inspect", "version": "1.11.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.11.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "d4bd7d7de54b9a75599f59a00bd698c1f1c6549b", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.11.1.tgz", "fileCount": 33, "integrity": "sha512-If7BjFlpkzzBeV1cqgT3OSWT3azyoxDGajR+iGnFBfVV2EWyDyWaZZW2ERDjUaY2QM8i5jI3Sj7mhsM4DDAqWA==", "signatures": [{"sig": "MEYCIQCRrBKp3pC0FBK+6kIeYsQvYYa7Jwc3/+h3OtCWT+UOZQIhAIOZ5KrxyiHlRj4lkog90qwgw4HvnhajMoP61uX4wvoK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrbJGCRA9TVsSAnZWagAAX+4P/joyWc5G+57NoXsdD3J/\n4q97sMFgHc8uDwT/vNhbL9pQzHM3efC13UzJg0/9adA9rU/7S2UIrY2BWDZa\ngRHP9ITuaD1lek+KvipWytM8okcIWXah0wKCs15U1mBf/cjRIJAbwin/gDWz\neRz/LqIvQUevGGfAe4VnzO9OssyysMeI7pVLbLyZGqVMRX/xwyzpMH3DDXfY\nXoPYaPRyaiq0MygIJ2uq4O473QF4poKy1p93heqORV05ZpaeUvhncQTpOQi4\nx85VqPAaDF13EeQ40RU6ttfYhWn0aedoK4G/bzM2p+cGgq0vwtPEVb53juI8\nMTxn9FK7D5QlxyTUw5TntaAtnpYnuLYesAdqcIeasc5rmBN+JiZrNJtw5F6/\naVjF0IwPLC9KuPTeAikXmqdAhpJR7pHg91LT+Uz9295ORgLUNMj5UC2Y8UOj\n3F8rHF5KQMogZFI/4OrK7W39RXNJEsqknMMRi4ZYNqqWyUDsdeo5kYdy4ZCZ\ndxXf1oHXh6VSwQXXCw3tPWhlzp4/Igg4L8ErnYYOkPJsSwpExWhZe2ArrBOV\n7OIXnXd4Wi9be8OhKttBRfiJyY8S/NEiQeTy36VXjHtdBlW3Anp+YpElkkpf\nFgFYHrtGW7b/pn3tn0NxJ8GKcB1N5RM43xt7YuwiKsQL/f3qwmPikWKotfpj\nUcvH\r\n=JPKM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "6732a40c6e5fd5f0bdcf29d8cb375435e01149c9", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "17.1.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.2", "eslint": "^8.4.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "auto-changelog": "^2.3.0", "has-tostringtag": "^1.0.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^20.0.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.11.1_1638773317914_0.7283607927507274", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "object-inspect", "version": "1.12.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.12.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "6e2c120e868fd1fd18cb4f18c31741d0d6e776f0", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.0.tgz", "fileCount": 33, "integrity": "sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g==", "signatures": [{"sig": "MEUCIQCwv/R3OiWuyyYfYP/+adaEJgRqHzj5cuyUIcqRVk6aBAIgR5ckiYC4Tz/lSnl48iGTb2UDIqTsK5gv82NeBs1t9vI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvZalCRA9TVsSAnZWagAAA10QAJu/JgWh++pn+vYm+1Re\n4+/zleDCwLgRPEiANzLKeor5LqMw5gNtd0vfqg0HnAaBDVz+Id4pgSQnKp+j\n1CKuSMPntMW9Sf0p2m0MZrzei9LcyKIbSAwNNMhiGUjGAdxuOX2IecAa7PAT\nnCM6Dh9HfAwYdAGDF4wM0jq9KkfmIo/dUqAi8txp5VzOcIM8v73qtSLjAzZF\nbpCHcMfrqY5FQX5loWAA5qgtL2kSwe9r9zMMLL4MIx3PhKxKf2EG4nNHrwS7\nRPkeFdiC6cUUjlIXAXOescy/w1bYJPO0KMAzq/so4vDR2AqjvFEIgf3+Bo+Z\nqv/GcHgh+EqvUz+MNkrl18SHFQ1lujKfdvBJFfrGolMyoSizHTXlbOAPvzVC\nI9V24sc2GYikgY0Nai5De7h+p1TPUDMgFqYxDVhMqUi6YWDUMtBhgWGrpaih\n+cUdkefu/4yGvqa7ZG/ttHrsERlXi9Ho3RZesOpalP0fHfsbCcl4DOfO7CPp\nJNnTnZojsST4i7JJh3A893S3nnQRgv21onlWEw6JQ40bgtqTEooG5gj1uLV8\nItrr9ehrM2C/vVBIjMXIvWwEa2twwJ2ehqYC+Lwr7Tv48O68zYo6BMgPtl/e\nrFafYMjEd6u3zGAYvIebyTsFcoIKY+ZYj7GIsXm42FoAP13/3yJFl/GDyT1W\n3LAN\r\n=OEtL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "69bbe4c994c38f2f1272af1d9d33dfd8b0a9966c", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.2", "eslint": "^8.5.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "error-cause": "^1.0.3", "auto-changelog": "^2.3.0", "has-tostringtag": "^1.0.0", "es-value-fixtures": "^1.2.1", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^20.1.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.12.0_1639814821534_0.8602372172603512", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "object-inspect", "version": "1.12.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.12.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "28a661153bad7e470e4b01479ef1cb91ce511191", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.1.tgz", "fileCount": 32, "integrity": "sha512-Y/jF6vnvEtOPGiKD1+q+X0CiUYRQtEHp89MLLUJ7TUivtH8Ugn2+3A7Rynqk7BRsAoqeOQWnFnjpDrKSxDgIGA==", "signatures": [{"sig": "MEQCICRZpHRtjRGP7M1fpB4XYygCVQkB8EzV/9HzFpBmhOLiAiAqeptVzZZ/rqiihJvc3yj90Z8O0LuQQyGxbWEqjGAXEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiiVt6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9tRAAmnij3FvOaTD7CIou0eKdswEu6+gFo5yhigjrYO0rVBqLnHw7\r\ngeGQonGXs/zGITH274jKINt3vh+niGo6Mfcot4IIlCdRJM58KD60BPY1UWFA\r\nwQgQkeDNi7yItQUqOXiQiwhZnFDkqfYDJxqvWfT7MDD/+p/9XsuAfmuQG3LA\r\niFVjlnTZuwU+pd/lHMBOauW6BOAqesoT40tTiAVnSYXQ4fJRj1lUmt60/vqv\r\nT6/DgiCYWSO7DQEicbCVIo8n62836V7aesiorXJAP6W683x9EJ5vy/7tAxjZ\r\n1967ZDIDQIQyyXl9PtvsJyb+TKEqIxQAtgFMLL41oGkfODhR4jrnkwdnHjsS\r\nROfpvGit/3kIHnfCzbPVb3kC04RvNt6lvhs4zHvtwUNTnKL7bLGOSscE+hwk\r\n7/Yjz95TRwylVletpVEtevld9E1p0H5u75Ma+jlBWV40b96DkEzZ9a0EUNaV\r\nLNYVa08P1XHm6Q5BoNZ2Av7q7OuG1v8GdF9KViqXBZBkwMftk1IfSbGjS7tS\r\nLgNJBYDAr0fnEOeigYjlW9MJ65iomVM0o4tWVbW2HDigFkquw47o1RlyJFgF\r\n/2wQpy/zcjjycmZfE4tDCvUgq9004fPYG9OkOkQrUjVEu2o+H4hhRhi4VzE9\r\n2WHdTCHpRzLUBlxgCQ4rTNmlrvOL8LZBGNk=\r\n=xkQV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1db93bcb148fa7fc9cc23fa30a1cbb5c03e8090e", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "18.2.0", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eslint": "=8.8.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "npmignore": "^0.3.0", "error-cause": "^1.0.4", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "has-tostringtag": "^1.0.0", "es-value-fixtures": "^1.4.1", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.0.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.12.1_1653169018444_0.941977220023257", "host": "s3://npm-registry-packages"}}, "1.12.2": {"name": "object-inspect", "version": "1.12.2", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.12.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "c0641f26394532f28ab8d796ab954e43c009a8ea", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.2.tgz", "fileCount": 33, "integrity": "sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==", "signatures": [{"sig": "MEUCIBOOZTLZMjKGEZVlbLYjUEIcD3e/LPXcfC2Wy8obJbNIAiEA89Jjn+xE8b/gTNonYAm2FFDcEPz5OSx8CK3AH7TPUuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij909ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDfg//eUTU48zno18gjggjwEZs7pgtbndUocVxT5k5XM8CqS0nEYiI\r\nca6qSLrhbrRBPvSHrmRVBzt2fLU9pMaffeT0F+NjYAZ677xZsxHl9NVjMXps\r\n+mc4mSagoK5W63Z5Lt6SBvE+bYN3+MhCxdEEgYQ4sHHXbkUYb6g/mHCjsfkH\r\nz4NyiYQfd5o7YeeRvQocVU5VFCYV9g//jgIz+a10FcFTw0XM+/DHLFSfTdXq\r\nCYE7juQW1satd+DOkx2SiEzKYASBiFSBo4KI93UaWZIrEFDbO5HQB1hrBFXW\r\nvl+Ob2jajPlOuPjNBc40//Bjrl72hNloXig5TO0IPy69wfCi+ob7YGv2jTbV\r\nUeWjMEOuz4xg4WhbEn8HivkocGMv3XlD50B0F9nNRbgFF1eQ6nMfwWTMbw0f\r\nhcsrBfwAfwoHaYcMrT43t4KTgSvjEBk0mDqs3GPZhUWwdCk1idRB5OHzqyhC\r\nQHRqFDM7WBC2xwhQyFpPB9ivNuykTH67QXhXlGG+d+dT2+96R5GyFY5C2xJ8\r\ngKunhTRWSD16MWDDDbcPZE9+JQvTo6Vj/7z5EpSQKqnRYxOArb2dVbd7jRwm\r\n429BbOW9T7BpVUkOiqAtxfMkLOeHq0TS6Uhfp24DJVwTnE/cnUjijLlbU61Z\r\nhYPSMlzbNmmpYxlSUrvI3oINknNFawA9rwk=\r\n=mDOF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "2cba8e119af13ff9648d5160871c3da22948ca8a", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "support": true, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "18.2.0", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eslint": "=8.8.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "npmignore": "^0.3.0", "error-cause": "^1.0.4", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "has-tostringtag": "^1.0.0", "es-value-fixtures": "^1.4.1", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.0.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.12.2_1653595453444_0.937507376726191", "host": "s3://npm-registry-packages"}}, "1.12.3": {"name": "object-inspect", "version": "1.12.3", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.12.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "ba62dffd67ee256c8c086dfae69e016cd1f198b9", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz", "fileCount": 33, "integrity": "sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==", "signatures": [{"sig": "MEQCIC4q7Ron/ZGj8w9SFYU+D37m7okjxML2nzMR04un2etQAiAcEMiUZ0/9mT6IcFD2USUUpAMDUbQoFMrdYdUzUypdAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwH4yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2khAAkoErEXNnKn3aO9JXe1z12TPkVZvdBbfUeN68FQpROSH8KRUn\r\nPQnIGTnYLtBrPYa/XzUrNZPFKu3VZyhLiczflCQVr3Vsvh7RimRyqD2dV3v2\r\n8XHc5FsSEdAnUky0nqrxFMCau0jCXMhTM7RKE4gyabl5RcAKH+ZiSqqQrX3J\r\nkp47kYuDm/l325ia6Hu+JygbG9jo3fuzqsJtwvuZy4NlWTS1dH4o2Uj0uzPu\r\n3CqnRuwveb8EgTJeFVmJeRvD2hVc72PanQcxVAVnLTSRJnjrvFLpIL3q6IYs\r\nGarhOTrmNDv8w6gmFZWUW+qzTx0/06tFf0LeGiXQYc4kaLMW9BNCGoWq/d6x\r\nBEnYvF/wFi5IghDs4Kh39aXrafB8ZwohJ9WI5X63i9gEFIbjpxOKi97Rfu8v\r\nHFfK21cow3hi/cjftPD5co2VwVRsmzL4rZpC2FlRc2wQGFdQ3QJA3ON98w3B\r\ne8oMcdNwb2HG3zv1ncpV6xZ7EFTJ/JsHFdT+qKlYlkxOhQ+/Omx/JBSAUneD\r\n2djy+rRJ/ba0JOqxpChQ/JH2rRl4DDatb4RIzdL57O+yPiGndI8H7RCwPGVE\r\njhYNWUr8LJeNjGxEjHOuZtby+avqTi2KCOfzrVgiIn30RXxCSCnnYVPKoTSO\r\nc6N7UPVJm3SwTGYJZ4Nh4N6P/R8Uq+wTS04=\r\n=EMOg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "8155d9e0721dcf8d2c07a3d262ae29f444f35e37", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "npx @pkgjs/support validate", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "support": true, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "19.4.0", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.1", "eslint": "=8.8.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "error-cause": "^1.0.5", "mock-property": "^1.0.0", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.4.0", "has-tostringtag": "^1.0.0", "es-value-fixtures": "^1.4.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.0.1", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.12.3_1673559602093_0.****************", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "object-inspect", "version": "1.13.0", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.13.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "42695d3879e1cd5bda6df5062164d80c996e23e2", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.0.tgz", "fileCount": 34, "integrity": "sha512-HQ4J+ic8hKrgIt3mqk6cVOVrW2ozL4KdvHlqpBv9vDYWx9ysAgENAdvy4FoGF+KFdhR7nQTNm5J0ctAeOwn+3g==", "signatures": [{"sig": "MEUCIQDRlile9EncLQll0PUBLwIHFjcevRj0xwdP8cYB9jfeIwIgKLs1OuCLQWNR+EOL+3zP8Jt0YxbZ5T25hTZ9fMGhfls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96838}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f354848b280d9ea922938c429d0bba92d75e5bc8", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "npx @pkgjs/support validate", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "support": true, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "20.8.0", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.7.1", "eslint": "=8.8.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "jackspeak": "=2.1.1", "npmignore": "^0.3.0", "globalthis": "^1.0.3", "in-publish": "^2.0.1", "error-cause": "^1.0.6", "mock-property": "^1.0.2", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.4.0", "has-tostringtag": "^1.0.0", "es-value-fixtures": "^1.4.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.13.0_1697341466541_0.9859804818048863", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "object-inspect", "version": "1.13.1", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.13.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "b96c6109324ccfef6b12216a956ca4dc2ff94bc2", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.1.tgz", "fileCount": 34, "integrity": "sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==", "signatures": [{"sig": "MEUCIQDKs88Q/SHaNjigga2ATrfSZeYEBsupTB3ovR7m4KTIwQIgHvO+w0PbHWqGrbuy19GI9RBz0MIHyawgkaHBnlPXZ28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97244}, "main": "index.js", "browser": {"./util.inspect.js": false}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "79bad8bf5da96f74aec8f64a6f07c37f33150527", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "npx @pkgjs/support validate", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "support": true, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "21.0.0", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.7.1", "eslint": "=8.8.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "jackspeak": "=2.1.1", "npmignore": "^0.3.0", "globalthis": "^1.0.3", "in-publish": "^2.0.1", "error-cause": "^1.0.6", "mock-property": "^1.0.2", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.4.0", "has-tostringtag": "^1.0.0", "es-value-fixtures": "^1.4.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.0", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.13.1_1697752434607_0.****************", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "object-inspect", "version": "1.13.2", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.13.2", "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "dea0088467fb991e67af4058147a24824a3043ff", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.2.tgz", "fileCount": 34, "integrity": "sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==", "signatures": [{"sig": "MEYCIQD71n/c+Fa3OqPojJR6ik1kPDUeWfEtfnbyOhjvQvpargIhAIpLAitjVCAChHEVtVW7Q8MeuoFnnWYYpWh4UCCbCF+1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99074}, "main": "index.js", "browser": {"./util.inspect.js": false}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "6a565ed4ce7181fe6c263b7893652e7a7dd9ae91", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "npx @pkgjs/support validate", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "support": true, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "22.3.0", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.8.1", "eslint": "=8.8.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "globalthis": "^1.0.4", "in-publish": "^2.0.1", "error-cause": "^1.0.8", "has-symbols": "^1.0.3", "safer-buffer": "^2.1.2", "mock-property": "^1.0.3", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.4.0", "has-tostringtag": "^1.0.2", "es-value-fixtures": "^1.4.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.1", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.13.2_1719031547738_0.0840508685686816", "host": "s3://npm-registry-packages"}}, "1.13.3": {"name": "object-inspect", "version": "1.13.3", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-inspect@1.13.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/object-inspect", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "dist": {"shasum": "f14c183de51130243d6d18ae149375ff50ea488a", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.3.tgz", "fileCount": 34, "integrity": "sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==", "signatures": [{"sig": "MEQCIFD7FNdBd0VF4TBDKFThBueMhgILMOvdUWED1Ea+vu0SAiBkXJ+cFaxwTF9q+q1sLhMEZGriqBcHofRBnOZ1u5oIyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101090}, "main": "index.js", "browser": {"./util.inspect.js": false}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "361db0752858a3e195260c54448c1169a96d8a1a", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "npx @pkgjs/support validate", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "prepublishOnly": "safe-publish-latest"}, "support": true, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"url": "git://github.com/inspect-js/object-inspect.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "string representations of objects in node and the browser", "directories": {}, "sideEffects": false, "_nodeVersion": "23.1.0", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.9.0", "eslint": "=8.8.0", "core-js": "^2.6.12", "for-each": "^0.3.3", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "globalthis": "^1.0.4", "in-publish": "^2.0.1", "error-cause": "^1.0.8", "has-symbols": "^1.0.3", "safer-buffer": "^2.1.2", "mock-property": "^1.1.0", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.5.0", "has-tostringtag": "^1.0.2", "es-value-fixtures": "^1.5.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.1", "string.prototype.repeat": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-inspect_1.13.3_1731113867161_0.6237063811498578", "host": "s3://npm-registry-packages"}}, "1.13.4": {"name": "object-inspect", "version": "1.13.4", "description": "string representations of objects in node and the browser", "main": "index.js", "sideEffects": false, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.5.0", "core-js": "^2.6.12", "error-cause": "^1.0.8", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "for-each": "^0.3.4", "functions-have-names": "^1.2.3", "glob": "=10.3.7", "globalthis": "^1.0.4", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "make-arrow-function": "^1.2.0", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "semver": "^6.3.1", "string.prototype.repeat": "^1.0.0", "tape": "^5.9.0"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "postlint": "npx @pkgjs/support validate", "test": "npm run tests-only && npm run test:corejs", "tests-only": "nyc tape 'test/*.js'", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "browser": {"./util.inspect.js": false}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "support": true, "engines": {"node": ">= 0.4"}, "_id": "object-inspect@1.13.4", "gitHead": "21bdaa07377a2d605ba78c774f4aa0381daf0425", "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "shasum": "8375265e21bc20d0fa582c22e1b13485d6e00213", "tarball": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "fileCount": 34, "unpackedSize": 102920, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDmWRl2Pr+NvTG0wMeED1kG9FCJlgPGJHqlz/dPa6LWHQIhALxOZiTuPsDFuDcUfcBlVhCWbqD8EYMlL32qytNN0RDt"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/object-inspect_1.13.4_1738718770027_0.20409612893504492"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-07-26T10:27:13.645Z", "modified": "2025-02-05T01:26:10.498Z", "0.0.0": "2013-07-26T10:27:15.032Z", "0.1.0": "2013-07-26T10:40:40.966Z", "0.1.1": "2013-07-26T11:02:26.154Z", "0.1.2": "2013-07-26T11:13:33.886Z", "0.1.3": "2013-07-26T11:21:00.352Z", "0.2.0": "2014-03-05T00:09:40.598Z", "0.3.0": "2014-03-05T01:11:08.775Z", "0.3.1": "2014-03-05T01:24:38.503Z", "0.4.0": "2014-03-22T02:22:02.042Z", "1.0.0": "2014-08-05T02:06:00.347Z", "1.0.1": "2015-07-19T07:22:54.442Z", "1.0.2": "2015-08-07T07:10:40.762Z", "1.1.0": "2015-12-15T01:37:54.806Z", "1.2.0": "2016-04-09T07:10:40.716Z", "1.2.1": "2016-04-09T15:57:31.022Z", "1.2.2": "2017-03-25T02:42:28.814Z", "1.3.0": "2017-07-31T22:37:18.753Z", "1.4.0": "2017-10-25T06:50:18.131Z", "1.4.1": "2017-12-20T07:23:05.801Z", "1.5.0": "2017-12-26T07:49:05.938Z", "1.6.0": "2018-05-02T23:13:04.663Z", "1.7.0": "2019-11-11T05:56:54.089Z", "1.8.0": "2020-06-19T06:05:34.212Z", "1.9.0": "2020-12-01T07:00:39.306Z", "1.10.0": "2021-04-17T20:19:58.710Z", "1.10.1": "2021-04-17T21:39:09.007Z", "1.10.2": "2021-04-18T04:25:54.040Z", "1.10.3": "2021-05-08T03:50:04.029Z", "1.11.0": "2021-07-12T20:07:51.310Z", "1.11.1": "2021-12-06T06:48:38.120Z", "1.12.0": "2021-12-18T08:07:01.690Z", "1.12.1": "2022-05-21T21:36:58.570Z", "1.12.2": "2022-05-26T20:04:13.693Z", "1.12.3": "2023-01-12T21:40:02.331Z", "1.13.0": "2023-10-15T03:44:26.794Z", "1.13.1": "2023-10-19T21:53:54.842Z", "1.13.2": "2024-06-22T04:45:48.032Z", "1.13.3": "2024-11-09T00:57:47.441Z", "1.13.4": "2025-02-05T01:26:10.272Z"}, "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "description": "string representations of objects in node and the browser", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "readme": "# object-inspect <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\nstring representations of objects in node and the browser\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n# example\n\n## circular\n\n``` js\nvar inspect = require('object-inspect');\nvar obj = { a: 1, b: [3,4] };\nobj.c = obj;\nconsole.log(inspect(obj));\n```\n\n## dom element\n\n``` js\nvar inspect = require('object-inspect');\n\nvar d = document.createElement('div');\nd.setAttribute('id', 'beep');\nd.innerHTML = '<b>wooo</b><i>iiiii</i>';\n\nconsole.log(inspect([ d, { a: 3, b : 4, c: [5,6,[7,[8,[9]]]] } ]));\n```\n\noutput:\n\n```\n[ <div id=\"beep\">...</div>, { a: 3, b: 4, c: [ 5, 6, [ 7, [ 8, [ ... ] ] ] ] } ]\n```\n\n# methods\n\n``` js\nvar inspect = require('object-inspect')\n```\n\n## var s = inspect(obj, opts={})\n\nReturn a string `s` with the string representation of `obj` up to a depth of `opts.depth`.\n\nAdditional options:\n  - `quoteStyle`: must be \"single\" or \"double\", if present. Default `'single'` for strings, `'double'` for HTML elements.\n  - `maxStringLength`: must be `0`, a positive integer, `Infinity`, or `null`, if present. Default `Infinity`.\n  - `customInspect`: When `true`, a custom inspect method function will be invoked (either undere the `util.inspect.custom` symbol, or the `inspect` property). When the string `'symbol'`, only the symbol method will be invoked. Default `true`.\n  - `indent`: must be \"\\t\", `null`, or a positive integer. Default `null`.\n  - `numericSeparator`: must be a boolean, if present. Default `false`. If `true`, all numbers will be printed with numeric separators (eg, `1234.5678` will be printed as `'1_234.567_8'`)\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install object-inspect\n```\n\n# license\n\nMIT\n\n[package-url]: https://npmjs.org/package/object-inspect\n[npm-version-svg]: https://versionbadg.es/inspect-js/object-inspect.svg\n[deps-svg]: https://david-dm.org/inspect-js/object-inspect.svg\n[deps-url]: https://david-dm.org/inspect-js/object-inspect\n[dev-deps-svg]: https://david-dm.org/inspect-js/object-inspect/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/object-inspect#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/object-inspect.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/object-inspect.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/object-inspect.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=object-inspect\n[codecov-image]: https://codecov.io/gh/inspect-js/object-inspect/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/object-inspect/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/object-inspect\n[actions-url]: https://github.com/inspect-js/object-inspect/actions\n", "readmeFilename": "readme.markdown", "users": {"touskar": true, "flumpus-dev": true, "ys_sidson_aidson": true}}