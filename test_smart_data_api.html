<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问数API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-form {
            margin-bottom: 20px;
        }
        .test-form input {
            width: 70%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-form button {
            width: 25%;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .test-form button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .steps {
            margin: 10px 0;
        }
        .step {
            padding: 5px 0;
            font-size: 14px;
        }
        .answer {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            white-space: pre-wrap;
        }
        .charts {
            margin-top: 15px;
        }
        .chart-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能问数API测试</h1>
        
        <div class="test-form">
            <input type="text" id="questionInput" placeholder="请输入您的问题，例如：最近蔬菜价格怎么样？" value="最近蔬菜价格怎么样？">
            <button onclick="testAPI()">测试</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        async function testAPI() {
            const question = document.getElementById('questionInput').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!question) {
                resultDiv.innerHTML = '<div class="result error">请输入问题</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result">正在测试API...</div>';
            
            try {
                const response = await fetch('http://127.0.0.1:5000/ask_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ question: question })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                let html = '<div class="result">';
                html += '<h3>API响应成功</h3>';
                
                // 显示步骤
                if (data.steps && data.steps.length > 0) {
                    html += '<div class="steps"><strong>处理步骤：</strong>';
                    data.steps.forEach(step => {
                        html += `<div class="step">• ${step}</div>`;
                    });
                    html += '</div>';
                }
                
                // 显示答案
                if (data.answer) {
                    html += `<div class="answer"><strong>回答：</strong><br>${data.answer}</div>`;
                }
                
                // 显示图表信息
                if (data.charts && data.charts.length > 0) {
                    html += '<div class="charts"><strong>图表数据：</strong>';
                    data.charts.forEach((chart, index) => {
                        html += `<div class="chart-info">
                            图表 ${index + 1}: ${chart.title || '未命名图表'} (${chart.type})
                            <br>数据集: ${chart.data.datasets ? chart.data.datasets.length : 0} 个
                            <br>标签: ${chart.data.labels ? chart.data.labels.length : 0} 个
                        </div>`;
                    });
                    html += '</div>';
                }
                
                // 显示其他信息
                if (data.source) {
                    html += `<div style="margin-top: 10px;"><strong>数据源：</strong> ${data.source}</div>`;
                }
                
                if (data.has_chart !== undefined) {
                    html += `<div><strong>包含图表：</strong> ${data.has_chart ? '是' : '否'}</div>`;
                }
                
                html += '</div>';
                resultDiv.innerHTML = html;
                
            } catch (error) {
                console.error('API测试失败:', error);
                resultDiv.innerHTML = `<div class="result error">
                    <h3>API调用失败</h3>
                    <div>错误信息: ${error.message}</div>
                    <div style="margin-top: 10px; font-size: 12px; color: #666;">
                        请确保后端服务正在运行 (python Backend/app.py)
                    </div>
                </div>`;
            }
        }
        
        // 回车键提交
        document.getElementById('questionInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testAPI();
            }
        });
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('页面加载完成，可以开始测试API');
        };
    </script>
</body>
</html>
