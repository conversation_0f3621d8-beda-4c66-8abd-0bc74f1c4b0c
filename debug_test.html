<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试测试</title>
</head>
<body>
    <h1>API调试测试</h1>
    
    <div>
        <h2>测试智能问数API</h2>
        <button onclick="testSmartData()">测试智能问数</button>
        <div id="smartDataResult"></div>
    </div>
    
    <div>
        <h2>测试市情概况API</h2>
        <button onclick="testCityInfo()">测试市情概况</button>
        <div id="cityInfoResult"></div>
    </div>
    
    <div>
        <h2>测试智能问策API</h2>
        <button onclick="testSmartPolicy()">测试智能问策</button>
        <div id="smartPolicyResult"></div>
    </div>

    <script>
        async function testSmartData() {
            try {
                const response = await fetch('http://127.0.0.1:5000/ask_data', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: '蔬菜价格怎么样' })
                });
                
                const data = await response.json();
                document.getElementById('smartDataResult').innerHTML = `
                    <h3>响应结果:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                console.log('智能问数API响应:', data);
            } catch (error) {
                document.getElementById('smartDataResult').innerHTML = `
                    <h3>错误:</h3>
                    <pre>${error.message}</pre>
                `;
                console.error('智能问数API错误:', error);
            }
        }
        
        async function testCityInfo() {
            try {
                const response = await fetch('http://127.0.0.1:5000/city_info/introduction', {
                    method: 'GET'
                });
                
                const data = await response.json();
                document.getElementById('cityInfoResult').innerHTML = `
                    <h3>响应结果:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                console.log('市情概况API响应:', data);
            } catch (error) {
                document.getElementById('cityInfoResult').innerHTML = `
                    <h3>错误:</h3>
                    <pre>${error.message}</pre>
                `;
                console.error('市情概况API错误:', error);
            }
        }
        
        async function testSmartPolicy() {
            try {
                const response = await fetch('http://127.0.0.1:5000/ask_policy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: '政策问题测试' })
                });
                
                const data = await response.json();
                document.getElementById('smartPolicyResult').innerHTML = `
                    <h3>响应结果:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                console.log('智能问策API响应:', data);
            } catch (error) {
                document.getElementById('smartPolicyResult').innerHTML = `
                    <h3>错误:</h3>
                    <pre>${error.message}</pre>
                `;
                console.error('智能问策API错误:', error);
            }
        }
    </script>
</body>
</html>
