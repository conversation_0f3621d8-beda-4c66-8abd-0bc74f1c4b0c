"""
知识库检索服务模块
提供基于阿里云百炼平台的知识库检索功能
"""

from http import HTTPStatus
from dashscope import Application


class AliCloudKnowledgeService:
    """知识库检索服务"""

    def __init__(self):
        # 阿里云百炼配置
        self.api_key = "sk-8a9d2ac53b5448c5856c65f51872fd05"
        # 默认使用知识库检索的app_id
        # 注意：如果遇到400错误，可能需要更新这些应用ID
        self.knowledge_app_id = "fd00ab6eaf7c47a6a9ab1d651b3be0b1"  # 知识库检索
        self.online_search_app_id = "ff034050df7a4dc39e491c6307d3e5b3"  # 联网搜索
        self.app_id = self.knowledge_app_id  # 默认使用知识库检索

    def set_knowledge_mode(self):
        """设置为知识库检索模式"""
        self.app_id = self.knowledge_app_id
        print(f"🔄🔄🔄 切换到知识库检索模式: {self.app_id} 🔄🔄🔄")

    def set_online_search_mode(self):
        """设置为联网搜索模式"""
        self.app_id = self.online_search_app_id
        print(f"🌐🌐🌐 切换到联网搜索模式: {self.app_id} 🌐🌐🌐")

    def search_knowledge(self, question):
        """
        调用阿里云百炼知识库检索
        
        Args:
            question (str): 用户问题
            
        Returns:
            dict: 包含success、answer、error等字段的结果字典
        """
        try:
            print(f"🔍 调用知识库检索: {question}")
            print(f"📋 使用API密钥: {self.api_key[:10]}...")
            print(f"📋 使用应用ID: {self.app_id}")
            print(f"📋 问题长度: {len(question)} 字符")

            # 检查问题是否为空或过长
            if not question or not question.strip():
                return {
                    'success': False,
                    'error': '问题内容为空，请输入有效问题'
                }

            if len(question) > 2000:
                return {
                    'success': False,
                    'error': '问题内容过长，请简化问题'
                }

            # 清理问题内容，确保编码正确
            clean_question = str(question).strip()
            print(f"📋 清理后问题: {clean_question}")
            print(f"📋 问题编码: {clean_question.encode('utf-8')}")

            response = Application.call(
                api_key=self.api_key,
                app_id=self.app_id,
                prompt=clean_question
            )

            if response.status_code != HTTPStatus.OK:
                print(f"❌ 阿里云API调用失败:")
                print(f"request_id={response.request_id}")
                print(f"code={response.status_code}")
                print(f"message={response.message}")

                # 根据错误码提供具体的错误信息
                error_message = f'知识库检索服务暂时不可用 (错误码: {response.status_code})'
                if response.status_code == 400:
                    error_message = f'请求参数错误 (错误码: 400)'
                    if hasattr(response, 'message') and response.message:
                        error_message += f' - {response.message}'
                elif response.status_code == 401:
                    error_message = 'API密钥无效或已过期 (错误码: 401)'
                elif response.status_code == 403:
                    error_message = 'API访问权限不足 (错误码: 403)'
                elif response.status_code == 429:
                    error_message = 'API调用频率超限 (错误码: 429)'
                elif response.status_code == 500:
                    error_message = '服务器内部错误 (错误码: 500)'

                return {
                    'success': False,
                    'error': error_message,
                    'error_details': {
                        'request_id': response.request_id,
                        'status_code': response.status_code,
                        'message': response.message,
                        'api_key_prefix': self.api_key[:10] + '...',
                        'app_id': self.app_id
                    }
                }
            else:
                print(f"✅ 知识库检索成功")

                # 提取文档引用信息
                doc_references = []
                try:
                    if hasattr(response.output, 'doc_references') and response.output.doc_references:
                        print(f"🔍 发现 {len(response.output.doc_references)} 个文档引用")
                        for i, doc_ref in enumerate(response.output.doc_references):
                            try:
                                # 先打印doc_ref的所有属性，用于调试
                                print(f"📄 文档引用 {i+1} 属性: {dir(doc_ref)}")

                                doc_info = {}
                                # 安全地获取各个属性
                                for attr_name in ['doc_id', 'doc_name', 'text', 'title', 'url', 'bizId', 'docUuid']:
                                    try:
                                        doc_info[attr_name] = getattr(doc_ref, attr_name, '')
                                    except Exception as attr_error:
                                        print(f"⚠️ 获取属性 {attr_name} 失败: {attr_error}")
                                        doc_info[attr_name] = ''

                                doc_references.append(doc_info)
                                print(f"✅ 文档引用 {i+1}: {doc_info.get('doc_name', 'Unknown')} - {doc_info.get('title', 'No title')}")
                            except Exception as doc_error:
                                print(f"❌ 处理文档引用 {i+1} 时出错: {doc_error}")
                                continue
                    else:
                        print("📄 没有找到文档引用信息")
                except Exception as ref_error:
                    print(f"❌ 处理文档引用时出错: {ref_error}")
                    doc_references = []

                # 获取原始答案
                clean_answer = response.output.text

                # 如果是联网搜索模式，保留markdown格式
                if self.app_id == self.online_search_app_id:
                    print(f"🌐 联网搜索结果，保留markdown格式")
                    # 只移除引用标记，保留其他格式
                    import re
                    clean_answer = re.sub(r'\[\d+\]', '', clean_answer)
                else:
                    # 知识库检索模式，进行常规清理
                    import re
                    # 移除 [数字] 格式的引用标记
                    clean_answer = re.sub(r'\[\d+\]', '', clean_answer)
                    # 移除多余的空格和换行
                    clean_answer = re.sub(r'\s+', ' ', clean_answer).strip()

                return {
                    'success': True,
                    'answer': clean_answer,
                    'request_id': response.request_id,
                    'doc_references': doc_references
                }

        except Exception as e:
            print(f"❌ 知识库检索异常: {e}")
            return {
                'success': False,
                'error': f'知识库检索服务异常: {str(e)}'
            }
