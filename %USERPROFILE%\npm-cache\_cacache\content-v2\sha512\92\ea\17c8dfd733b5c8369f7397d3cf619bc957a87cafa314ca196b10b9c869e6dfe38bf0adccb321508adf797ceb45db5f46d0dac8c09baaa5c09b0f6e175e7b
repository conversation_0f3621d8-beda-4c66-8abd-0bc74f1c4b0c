{"_id": "eventemitter3", "_rev": "96-e190e93bd2c252ee5103ee3ad5e31932", "name": "eventemitter3", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "dist-tags": {"latest": "5.0.1"}, "versions": {"0.0.0": {"name": "eventemitter3", "version": "0.0.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "reactor", "pub/sub", "event", "emitter"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "~1.13.0", "pre-commit": "0.0.4", "chai": "~1.8.0"}, "_id": "eventemitter3@0.0.0", "dist": {"shasum": "90a5cc3c2ef715169ceaed893d797ae1951c8119", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.0.0.tgz", "integrity": "sha512-Zq9OHuw2xxQ2TQv5iHfwbUWMeqBp9iKS6AflqN/Dv/BTZfx7a/rL4EBTDAlildLWO4YaeTNQ3SZDQev5WNRFoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE/Qi/9jFQhLhMZ8Q6GcFOGHsAwO2jJu3/wNguQmZ/13AiEAsv8gv6qVW/5T+ag25oaDV2BsWQ/xjSsMVhfqO4Q8dHU="}]}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "eventemitter3", "version": "0.0.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "reactor", "pub/sub", "event", "emitter"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "~1.13.0", "pre-commit": "0.0.4", "chai": "~1.8.0"}, "_id": "eventemitter3@0.0.1", "dist": {"shasum": "052227d4fc69b1d3f1ec0e569b248e264ccd795b", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.0.1.tgz", "integrity": "sha512-aCRzW8UXN8NUD8oe92Fafzssankj/4mZruKLWaJWU5gQFpRTAkIJK3dND5DH75z2MXWdQFHgWkGgaoFhfTDhrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC3/aXlkvPqEaS32sLIh2zmKgpqP789S4AbBy09jrrg1AiEA4QV8Mguk+Jg0ZBAIgsauYQCKTrvlCUkL3vNyMSm2qc4="}]}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "eventemitter3", "version": "0.1.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "reactor", "pub/sub", "event", "emitter"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "1.13.x", "pre-commit": "0.0.x", "chai": "1.8.x"}, "homepage": "https://github.com/3rd-Eden/EventEmitter3", "_id": "eventemitter3@0.1.0", "dist": {"shasum": "727e1600ea477f50f1f11328cb9a5abf752c1dff", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.1.0.tgz", "integrity": "sha512-Suyfcp/LJAD3yMPgNm3ZO1c2zr22kPPaE85GrZiRT1LBiZ8oNESFIr9//TbtUwVqFgBoM/lDp43Gi5MPkuCjnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExjKxFjlVjM8vgZwJi16KV7XaGQEUAQPyHNdwvRnbxxAiAlbRsaczGzqoF9WWIFE5PdycCPxlGhRNweQaODSjhwaw=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "eventemitter3", "version": "0.1.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "reactor", "pub/sub", "event", "emitter"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "1.13.x", "pre-commit": "0.0.x", "chai": "1.8.x"}, "homepage": "https://github.com/3rd-Eden/EventEmitter3", "_id": "eventemitter3@0.1.1", "dist": {"shasum": "7ea62a9d6b8343cb17ddb07f6ae8d3881ecdb8c3", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.1.1.tgz", "integrity": "sha512-rQmwpNQbRYmBju//ED3cOQ0LeFrMi8SGrQeNSC1J2q8y7gf9pxr0TkjychPn7rl7OUW6z8+h660XE+SS5FDAgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEW4wwKGsWvS4SGyme4VJ9OTC9SY9a6LmEezAN8zj4igIhAPrcjhxN2NegQLTPFOpSFrV8LJLZ7TY4/iY2jpfq4ydg"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "eventemitter3", "version": "0.1.2", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "reactor", "pub/sub", "publish", "subscribe", "event", "emitter", "addListener", "addEventListener"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "1.18.x", "pre-commit": "0.0.x", "chai": "1.9.x"}, "homepage": "https://github.com/3rd-Eden/EventEmitter3", "_id": "eventemitter3@0.1.2", "dist": {"shasum": "4ede96d72b971a217987df4f1d4ca54dd8d20b79", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.1.2.tgz", "integrity": "sha512-YTExP4OnPbV3phPNZdanE9rqHepVkgg1IwheXJBHWDfbjTqflat/1aOq6O55fz+iJQm4HCDTfdelFgU5GxdTGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvMi47ntLSy8B2U5jy6hwqudpF3HRaPkwTQyIfdmVBJAiA/5NRBN6ceA/oRAzQMD5bdNzDB5WkRsS7dgHFlzYrXNw=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "eventemitter3", "version": "0.1.3", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "reactor", "pub/sub", "publish", "subscribe", "event", "emitter", "addListener", "addEventListener"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "1.18.x", "pre-commit": "0.0.x", "chai": "1.9.x"}, "homepage": "https://github.com/3rd-Eden/EventEmitter3", "_id": "eventemitter3@0.1.3", "_shasum": "6b8ac1392ff0a5b4d87e893bdbee79887a86d95a", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "dist": {"shasum": "6b8ac1392ff0a5b4d87e893bdbee79887a86d95a", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.1.3.tgz", "integrity": "sha512-URdOVp3dDQmrx+BLqf8VcHf+2jzrt4tPydLhb0JFCmj+HuBc02/Ar8lEkETs/VD8db6+EDAU9cw9Pkc4jbPvXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcRz6ECbMGDZHvVdb8sAhngKTiqpth4hPsGbuA03tXowIhAJnuHZ380PigwA5kDKBmOPSYp44E0Y0o95/XtOIzErHC"}]}, "directories": {}}, "0.1.4": {"name": "eventemitter3", "version": "0.1.4", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "1.18.x", "pre-commit": "0.0.x", "chai": "1.9.x"}, "homepage": "https://github.com/3rd-Eden/EventEmitter3", "_id": "eventemitter3@0.1.4", "_shasum": "da2be74b7a1a4760272e1390f975503be5cb7e24", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "dist": {"shasum": "da2be74b7a1a4760272e1390f975503be5cb7e24", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.1.4.tgz", "integrity": "sha512-Db0HV0WQhdK2sw30BD1mi6YlFvIAkZYx8aT97R2Da9/thKX7PqL2fUDrUJ1TTrtIPqid9p3dLNeByau9Wj/Npg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDeXtpecUAQL8zN/2CQj5jTEYyqHEAfQTAyYcV+8UlphAiAY6RnzByrCYzZ++y3MpeX04ptulcEZ9mTTrQ7jDohOlQ=="}]}, "directories": {}}, "0.1.5": {"name": "eventemitter3", "version": "0.1.5", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface. This the source of the same EventEmitter that is used in Primus.", "main": "index.js", "scripts": {"test": "NODE_ENV=testing ./node_modules/.bin/mocha $(find test -name '*.test.js')"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/EventEmitter3/issues"}, "devDependencies": {"mocha": "1.18.x", "pre-commit": "0.0.x", "chai": "1.9.x"}, "homepage": "https://github.com/3rd-Eden/EventEmitter3", "_id": "eventemitter3@0.1.5", "_shasum": "fbb0655172b87911ba782bb7175409c801e5059f", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "dist": {"shasum": "fbb0655172b87911ba782bb7175409c801e5059f", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.1.5.tgz", "integrity": "sha512-axKWjfZV4AhCCvkRv7q/woI6sROb/ZiwpTmySf3wYN0Rv7aeCDpL7GYuJit99dMoAGj8dijU8eUgU5qU6Z2CtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMD2LQToiBouW6d/7BII66etzw2VyPTzWLGrDfylPaHQIhAMZ3p9RIxAbbdH5xqxGxI/pL9r2kq7whbkRT6osmgjpU"}]}, "directories": {}}, "0.1.6": {"name": "eventemitter3", "version": "0.1.6", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test": "mocha --reporter spec --ui bdd test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- --reporter spec --ui bdd test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter spec test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/EventEmitter3/issues"}, "devDependencies": {"assume": "0.0.x", "istanbul": "0.3.x", "mocha": "2.0.x", "pre-commit": "0.0.x"}, "gitHead": "965f7968e2a8eb580e1f63b7863fb957b3516b36", "homepage": "https://github.com/primus/EventEmitter3", "_id": "eventemitter3@0.1.6", "_shasum": "8c7ac44b87baab55cd50c828dc38778eac052ea5", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "dist": {"shasum": "8c7ac44b87baab55cd50c828dc38778eac052ea5", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-0.1.6.tgz", "integrity": "sha512-dJbNxPUe9OzF8Hsyy7+lEmb3lIZhhOKZ9rDfV6nXPSl6V/24sUz3K+4v6tFhYyWl0lqC7YDx01mG15+7AoU+9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCONiQe4r2V6KL/vocb/+Sa0EHYfnbwtLtXYRNI8FU/MwIgKnVj6yvSsT6Y+6QboQ5gp0UW81aTBgcUpiroKJip7Lk="}]}, "directories": {}}, "1.0.0": {"name": "eventemitter3", "version": "1.0.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test": "mocha test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/EventEmitter3/issues"}, "devDependencies": {"assume": "1.2.x", "istanbul": "0.3.x", "mocha": "2.2.x", "pre-commit": "1.0.x"}, "gitHead": "b13ef8bdb57b02c97c81c0c4a569befa30e8339a", "homepage": "https://github.com/primus/EventEmitter3", "_id": "eventemitter3@1.0.0", "_shasum": "c738401cb6d29e46e00ee623521689082a52e1cf", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c738401cb6d29e46e00ee623521689082a52e1cf", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-1.0.0.tgz", "integrity": "sha512-Ob9X4nSqpEagVrTk1PTTXMVQrYv/sy0aS0IQPReSKFTPdBh0tZC0sTTNAzUMlfoGDNNBygdMgRF3XnXYlem0JQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8f4j7NUPZa2Kd7T2p8B5kX6eq0UAXAkeVfD6/AAe7nAIhAOLonnnDWyv6AayerYVHKtwghZ5ulBgw1jhjbXSNjih4"}]}, "directories": {}}, "1.0.1": {"name": "eventemitter3", "version": "1.0.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test": "mocha test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/EventEmitter3/issues"}, "devDependencies": {"assume": "1.2.x", "istanbul": "0.3.x", "mocha": "2.2.x", "pre-commit": "1.0.x"}, "gitHead": "cd22a430db13950f3c16a72f943f61a96c9663ce", "homepage": "https://github.com/primus/EventEmitter3", "_id": "eventemitter3@1.0.1", "_shasum": "75a110a2e1bcc5de7999fead1910fcb058f51a14", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "75a110a2e1bcc5de7999fead1910fcb058f51a14", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-1.0.1.tgz", "integrity": "sha512-LMXKmaXGOEh4II3DSH9AOgmIOzXJDCc7pHCRxKKx6a85WwayqemRG7SHfITFRcdvJ8hCo/NBST/2Z/Sqw6dlig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvtfOlU0/XbYPOZ+qzyrymviekDv+YNu1wIkXbyB+fJgIhAJW0eiFaVGcuWSF/44TpM4aAFRccgpqFlUePCPBCgJB7"}]}, "directories": {}}, "1.0.2": {"name": "eventemitter3", "version": "1.0.2", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test": "mocha test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/EventEmitter3/issues"}, "devDependencies": {"assume": "1.2.x", "istanbul": "0.3.x", "mocha": "2.2.x", "pre-commit": "1.0.x"}, "gitHead": "8274c16a934efc7a127766ae191a8a9d0065c1a4", "homepage": "https://github.com/primus/EventEmitter3", "_id": "eventemitter3@1.0.2", "_shasum": "1163487c15b37566bf4ea8dbd5ccb5ddd5c0a9e8", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1163487c15b37566bf4ea8dbd5ccb5ddd5c0a9e8", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-1.0.2.tgz", "integrity": "sha512-QBhOKZqqbgKdq2IffKvbggerT2mb6enlukAFGCa7mOoXJnNsKTO3ehm/dRGTL9Pln8aWO+y8MWfW6On22v4lqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIfFJdDg/ewAR0jkMNYagCIDNy4hrehvpevru/n8WiWAIgAYLyANYvnVqvDzA+hCoDcb63zgwainj0Oez/TZhDaEI="}]}, "directories": {}}, "1.0.3": {"name": "eventemitter3", "version": "1.0.3", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test": "mocha test.js", "sync": "node versions.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/EventEmitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.2.x", "istanbul": "0.3.x", "mocha": "2.2.x", "pre-commit": "1.0.x"}, "gitHead": "1b479ec043fe41156bbf73295674828451b78ea6", "homepage": "https://github.com/primus/EventEmitter3#readme", "_id": "eventemitter3@1.0.3", "_shasum": "15295f06dca6e1f35453a860b0fd43876367e258", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.2", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "15295f06dca6e1f35453a860b0fd43876367e258", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-1.0.3.tgz", "integrity": "sha512-V9Egl/Z7Vb+NLeo/sjAvmG9Ch7bSke6YHVbqKy51Ga1+5x/zWdpm3MVt2vK3gydDwQJnL+ERxy4ExLh+qCD2cQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFlD2RgiLVGkd+stxP3GahB6P4fxAv7qnzgTfEpDFEPaAiEAvPiz7jzAuhd0LDj6NtmWquO/VVaV+H3Tlh5AwtKxCBk="}]}, "directories": {}}, "1.1.0": {"name": "eventemitter3", "version": "1.1.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test": "mocha test.js", "sync": "node versions.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/EventEmitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/EventEmitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.2.x", "istanbul": "0.3.x", "mocha": "2.2.x", "pre-commit": "1.0.x"}, "gitHead": "0cd64db5b271fa2ec3a53f006dd968f8c94f66ff", "homepage": "https://github.com/primus/EventEmitter3", "_id": "eventemitter3@1.1.0", "_shasum": "8d94b51448fa4ae11f8725aeebb7c0c48a8e71ac", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "8d94b51448fa4ae11f8725aeebb7c0c48a8e71ac", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-1.1.0.tgz", "integrity": "sha512-FVUhg9tDlpbwjWkgPGY/5QEEXO5ZT21MiVDdriPpSuThN/fzPrULTmCCr7ZJwjR6lMSLXFT1/yw/t5C2MiXeSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8rxP9Do6GmGAPuP/72QN5zUbUMFEsR6qn8gtYuDZ74QIhAIDQgclVZ9SYRWaTY2HxH2dnqPURcPNdnwPTckHHtcnQ"}]}, "directories": {}}, "1.1.1": {"name": "eventemitter3", "version": "1.1.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test-browser": "zuul --browser-name ${BROWSER_NAME} --browser-version ${BROWSER_VERSION} -- test.js", "test-node": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "sync": "node versions.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.2.x", "istanbul": "0.3.x", "mocha": "2.2.x", "pre-commit": "1.0.x", "zuul": "3.0.x"}, "gitHead": "91f571f40c918d71220747791c05ec33f3402a56", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@1.1.1", "_shasum": "47786bdaa087caf7b1b75e73abc5c7d540158cd0", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "47786bdaa087caf7b1b75e73abc5c7d540158cd0", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-1.1.1.tgz", "integrity": "sha512-idmH3G0vJjQv2a5N74b+oXcOUKYBqSGJGN1eVV6ELGdUnesAO8RZsU74eaS3VfldRet8N9pFupxppBUKztrBdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH8L3oluOS7ApkTOHT1Oyd5HX77SAmIRX1OfmQlD/o2HAiBlT6B77tJeTDCAa9WBtWPZ/GSp4GXCebgHwySQ1pjcAQ=="}]}, "directories": {}}, "1.2.0": {"name": "eventemitter3", "version": "1.2.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"test-node": "istanbul cover _mocha --report lcovonly -- test.js", "coverage": "istanbul cover _mocha -- test.js", "test-browser": "zuul -- test.js", "sync": "node versions.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.3.x", "istanbul": "0.4.x", "mocha": "2.4.x", "pre-commit": "1.1.x", "zuul": "3.10.x"}, "gitHead": "c78d597fed80952c259b916c0a1f4dca91d940e4", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@1.2.0", "_shasum": "1c86991d816ad1e504750e73874224ecf3bec508", "_from": ".", "_npmVersion": "3.8.0", "_nodeVersion": "4.3.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "1c86991d816ad1e504750e73874224ecf3bec508", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-1.2.0.tgz", "integrity": "sha512-DOFqA1MF46fmZl2xtzXR3MPCRsXqgoFqdXcrCVYM3JNnfUeHTm/fh/v/iU7gBFpwkuBmoJPAm5GuhdDfSEJMJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBzSVGj0BUo++p6uU9rkeesBVO+d4yc7LRrpLmYuJJuJAiBcC44piOSmlJQL3MnlbGG4ghCimG25wvNdGvF9KQ1hyQ=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/eventemitter3-1.2.0.tgz_1458148661717_0.1867425285745412"}, "directories": {}}, "2.0.0": {"name": "eventemitter3", "version": "2.0.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test-node": "istanbul cover _mocha --report lcovonly -- test.js", "coverage": "istanbul cover _mocha -- test.js", "test-browser": "zuul -- test.js", "sync": "node versions.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.4.x", "istanbul": "0.4.x", "mocha": "3.0.x", "pre-commit": "1.1.x", "zuul": "3.11.x"}, "gitHead": "5d4cd1928eacf51877e73c6d01ec4daf66f3e547", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@2.0.0", "_shasum": "605f34e75ea702681fcd06b2f4ee2e7b4e019006", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "605f34e75ea702681fcd06b2f4ee2e7b4e019006", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.0.tgz", "integrity": "sha512-SWB7ctiyVMfR2af2l6Po55yrFtlNKLVz/deELM/WawCBTIzfejh08lGDmlT7jX4XF0npqHQXhwhUfqGViGjUtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIACw3LH2iTrtN1SV9vZLdX4AVVs3WDiBgRpHRE3QW5huAiEAlprk172zdKMtTGUrUe4xoK7KrqGZee3wISgoC9Fd1YQ="}]}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/eventemitter3-2.0.0.tgz_1473423580446_0.5355233517475426"}, "directories": {}}, "2.0.1": {"name": "eventemitter3", "version": "2.0.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "scripts": {"benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test-node": "istanbul cover _mocha --report lcovonly -- test.js", "coverage": "istanbul cover _mocha -- test.js", "test-browser": "zuul -- test.js", "sync": "node versions.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.4.x", "istanbul": "0.4.x", "mocha": "3.0.x", "pre-commit": "1.1.x", "zuul": "3.11.x"}, "gitHead": "58f3909300bb6503749c813cf1abaa0058a9778c", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@2.0.1", "_shasum": "59c8930b1d8f4da54ad752854948f44330e7f39c", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.6.0", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "59c8930b1d8f4da54ad752854948f44330e7f39c", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.1.tgz", "integrity": "sha512-UP4AH5APcE2AC0JkesXvGtah5VQO1PGn5v/9kZknFnIwW0pd0A7ZL+Ga/K8jRGiaEapsdNEpb7IjKqYBlUQxWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfBWjShwwAKO1coQnwDrYxfOl17OolEQItvqns2js4IwIhAPUC46UQH8N6k16R2RYYDa+DB6w4KtaHm+7pyhUsKzvF"}]}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/eventemitter3-2.0.1.tgz_1474959547234_0.053572222124785185"}, "directories": {}}, "2.0.2": {"name": "eventemitter3", "version": "2.0.2", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test-node": "istanbul cover _mocha --report lcovonly -- test.js", "coverage": "istanbul cover _mocha -- test.js", "test-browser": "zuul -- test.js", "sync": "node versions.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "1.4.x", "istanbul": "0.4.x", "mocha": "3.1.x", "pre-commit": "1.1.x", "zuul": "3.11.x"}, "gitHead": "a9eedef2a58deae7006028662a83d88579486886", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@2.0.2", "_shasum": "20ce4891909ce9f35b088c94fab40e2c96f473ac", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.7.0", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "20ce4891909ce9f35b088c94fab40e2c96f473ac", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.2.tgz", "integrity": "sha512-dpZBHG9cdLpQZtEaMy1VY5WBlKYAA6Ib70cyQQyfEDNwC+QCj5/xuE2h9AcqgsNyvsTq6ZIfjBb3RTxu55MYxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4qucSOFquOLQNkS1N46Y9ErpCFjPvJrHG+ljrBc2+kQIgcLpUxwl5j/cKFJFVfj3Ecgm8f9ceMKP+VVvlVzjAlZw="}]}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/eventemitter3-2.0.2.tgz_1475216591620_0.3523867195472121"}, "directories": {}}, "2.0.3": {"name": "eventemitter3", "version": "2.0.3", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"build": "mkdir -p umd && browserify index.js -s EventEmitter3 | uglifyjs -m -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha", "test-browser": "zuul -- test.js", "prepublish": "npm run build", "sync": "node versions.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "pre-commit": "sync, test", "devDependencies": {"assume": "~1.4.1", "browserify": "~14.1.0", "mocha": "~3.2.0", "nyc": "~10.2.0", "pre-commit": "~1.2.0", "uglify-js": "~2.8.20", "zuul": "~3.11.1"}, "gitHead": "9afe1b539e52ec4b8eb4e07d69a5deb5f25c326b", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@2.0.3", "_shasum": "b5e1079b59fb5e1ba2771c0a993be060a58c99ba", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "b5e1079b59fb5e1ba2771c0a993be060a58c99ba", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz", "integrity": "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID4ZwUInIj0EhCdMgwNp5uEJbxl8aZ5IMcjbkQIHqj1IAiATrUUqmvdDsSCyf5mjFWR8il07W/miY0wXTDhFFEExnA=="}]}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/eventemitter3-2.0.3.tgz_1490971867525_0.8374074944294989"}, "directories": {}}, "3.0.0": {"name": "eventemitter3", "version": "3.0.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"build": "mkdir -p umd && browserify index.js -s EventEmitter3 | uglifyjs -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "test-browser": "node test/browser.js", "prepublishOnly": "npm run build"}, "files": ["index.js", "index.d.ts", "umd"], "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~1.5.0", "browserify": "~14.5.0", "mocha": "~4.0.0", "nyc": "~11.3.0", "pre-commit": "~1.2.0", "sauce-browsers": "~1.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.2.0"}, "gitHead": "e36eab836feeafe7101e06e7e736d368f27b21ec", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@3.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-62TxCtz4m2LRaOERVEvLJJ4A6rsg8lC9Xm+FLg2y/1fB/v4ZZ9JCOn+/Ppl5KkH6sRih6bhix724PVanmXYZJQ==", "shasum": "fc29ecf233bd19fbd527bb4089bbf665dc90c1e3", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVYW1YeGOG0OEjcuKuxLAOVUwOOw0xHzy5Q/0sQoGvjAiBjdhSZ7Ikd152yvKmiJdGtQiWI3r/mNiLPKCGrgFngUg=="}]}, "maintainers": [{"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3-3.0.0.tgz_1511976245786_0.8547864067368209"}, "directories": {}}, "3.0.1": {"name": "eventemitter3", "version": "3.0.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"build": "mkdir -p umd && browserify index.js -s EventEmitter3 | uglifyjs -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "test-browser": "node test/browser.js", "prepublishOnly": "npm run build"}, "files": ["index.js", "index.d.ts", "umd"], "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~1.5.0", "browserify": "~15.2.0", "mocha": "~5.0.0", "nyc": "~11.4.1", "pre-commit": "~1.2.0", "sauce-browsers": "~1.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.3.0"}, "gitHead": "81025e2543554263f282774e248c18c69fd89e96", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@3.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-QOCPu979MMWX9XNlfRZoin+Wm+bK1SP7vv3NGUniYwuSJK/+cPA10blMaeRgzg31RvoSFk6FsCDVa4vNryBTGA==", "shasum": "4ce66c3fc5b5a6b9f2245e359e1938f1ab10f960", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.0.1.tgz", "fileCount": 6, "unpackedSize": 20382, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5G+1OmWjMgFZOZGKlHq2aR4hJy5KJAGcOkUO78iH/mQIgXnHOIkAEbiVnDyBuvM3w71x4ryYSh1onvl9qKbFNMTI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_3.0.1_1517990256516_0.4655667565871582"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "eventemitter3", "version": "3.1.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "files": ["index.js", "index.d.ts", "umd"], "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~1.5.0", "browserify": "~16.2.0", "mocha": "~5.1.0", "nyc": "~11.7.1", "pre-commit": "~1.2.0", "sauce-browsers": "~1.2.0", "sauce-test": "~1.3.3", "uglify-js": "~3.3.0"}, "gitHead": "5261097d5109946920d0d9d083da7186c838ce30", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@3.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-ivIvhpq/Y0uSjcHDcOIccjmYjGLcP09MFGE7ysAwkAvkXfpZlC985pH2/ui64DKazbTW/4kN3yqozUxlXzI6cA==", "shasum": "090b4d6cdbd645ed10bf750d4b5407942d7ba163", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.1.0.tgz", "fileCount": 8, "unpackedSize": 36241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4NzSCRA9TVsSAnZWagAAYsYP/j3zNmW2uWdBC2A1cPSn\nMsgv21wWhhuCkg7qc1aotD4X2/L/0xYBwI5jbKvXVicz2NQUgcJG68q3BriU\nskYbjblLpct5R9BzPiwEMm6wRmHk0JN0Fz5bBtk1EA4xaQHugkbzrOLLhxxM\nkAa84X4wLJunz/JmTGn4akyz92FzMM6oOxZfNzzIXepw1eAxQF5YGLFRitC7\nKeJQv/p2d0dLb73MjyvDC0IPe7R1ZwMeUF06A2w+Z04/jLK8mdumM9hws9kj\nbhq5WLePhMEgnvLIlHDBAevqj4qBgXaS5GsbNCC7Za6xJaz1fLLkx+jGl8A3\nw8EGDY+DkxmKHgMPrIqM6eCNOYcCc35tUCVsSf73GFqGA+f8luuasRqI23VM\nqbBScHy08sESdFkOeaqZByJClN7zl10CVQTU5Gm/vXNFNjmx8yxYlsJRqUGq\nT8GHlr3q4oEjFmLbcR9tIYwYGx/QyNYeB2DZ0HKu51LNtWeFVbYx+TKnG8zB\nDNNdNbyaAiGOEPXXhUxjUsywxgZatymqtiPQhxd0blnIOSZHJsm1qYBggXbk\nGYLigxrN6D6bBsW4+6c74v7CbaEP3zulHAMukIQQoE5gjfq49J9vP8CjBG0w\nxE7WMeXlre4YdNrBUTxPA312/f9VtypiFzqnj7cSENsm/tynZjCR4xAdJG9z\n++Gc\r\n=f2la\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCd/703SqfYUfjPvLWj6k23U1hb5e52ltpiWWjXo5MvhgIgcqr4Isv8AwhckhifvqeyZT8iQXISAphluTz8cKXXoNw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_3.1.0_1524686033911_0.48391916986617156"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "eventemitter3", "version": "3.1.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~2.2.0", "browserify": "~16.2.0", "mocha": "~6.1.0", "nyc": "~14.0.0", "pre-commit": "~1.2.0", "sauce-browsers": "~2.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.5.0"}, "gitHead": "52941321ad3f42c976eefcd706e891d06a0376bf", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@3.1.1", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-MXmFv3KYbv7MPjPeGlFCTieXB9zNvmHfy4fXzZbrdMeUUk3pxQ8SS0cJ6CcwUDZnIL3ZDa01qQFzhlusB8s51Q==", "shasum": "1ab02a344af74f5cbf528969601bf0fd6aeebf98", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.1.1.tgz", "fileCount": 8, "unpackedSize": 36256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxyTtCRA9TVsSAnZWagAA7xgQAIcgU0Xe8sCjBrhQNTEd\n0tx5RLC/63afKnoM5GDTDOYZUu6aHxDnSH0i4J60AZ6NzS2zH1YCfsLj28t9\nhZ6rb3SDEwK1d7iz1wSytR0QclImlDvtvcAoP/XgUFFZIQFRkLaP7xicFCZd\ndRwTYvUScV/leuljCtN/NXfmo6U6MrWj+xYlhk8GfDchVEg+W7uTXozWIdGf\nUkvmtoOzXky8InN82J49nT/9uvDoqVL+tAi/ppzCd/tcy71U81TXgoDPfGUw\nQx8C9X29DiWohJDkmNLAiVCAjB+RpzgTAf3V8/eJuyNu9BcHL4yJJIjvA+vQ\nJ3nGarNUHNU/7ct0B8ZXvwxCgzdHtYNtqchzT3z3wDNdRCL4lUdulaonoFBB\nBijbOCTggmH1Bd0BM7PnIYv9dGC833zlKd8eyzS3ijT8s1u8tw0WlbCbW25/\nWjr8jhf6fJCH2xZskxE+erQxJLfCk71gNNwBrVIA8UTFyzBcDIPEiM2Hq5OM\nEG/0ECHWAaW6WpCU6icINJ9lK8rU9eWMv7ihArRTRmVS4rVqm565Bgn438+1\n6v6VlfH7qFi1R4QjwzX/S7DHjufsloeHM35lhh9evSHkUr+xjPy7iXhtJBey\njfn4jbghy7Yw5cclCJpc2EZ4BJYrjlAqgTVQpe+PopK6TG8DORhKINfz1EcT\nqdii\r\n=bLMn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDCkrTMgS6O5lVa8F6tV+YzC/7XrwgQr734fSWOUlKgWAiEAixgWi9tMIxMoQ/rgajVwH/PO4y6eUjmYkVFInSsET+Y="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_3.1.1_1556554988590_0.9851152735788387"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "eventemitter3", "version": "3.1.2", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~2.2.0", "browserify": "~16.2.0", "mocha": "~6.1.0", "nyc": "~14.0.0", "pre-commit": "~1.2.0", "sauce-browsers": "~2.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.5.0"}, "gitHead": "54b25612832e6ab21efc836b99e4d0b0f69eeb69", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@3.1.2", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-tvtQIeLVHjDkJYnzf2dgVMxfuSGJeM/7UCG17TT4EumTfNtF+0nebF/4zWOIkCreAbtNqhGEboB6BWrwqNaw4Q==", "shasum": "2d3d48f9c346698fce83a85d7d664e98535df6e7", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.1.2.tgz", "fileCount": 8, "unpackedSize": 36240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcx9cvCRA9TVsSAnZWagAA0n0P/3tqCbPIQ0J24jWYn2Aj\nm/RQ0ALKXFjQF2wf2p1peIpYZuaZnh5P3QaBMH17kYDo5S969nOYxrj0BUwu\nn/reAHnJQxXJGD9IYz22wWoVnwfUewoAGwdeOwTdxDNfrPDBJky42e0u1UvO\nS04QbCeyG8+wNH1eJDtoPsMcs2AN4Ppd2JfwV/SkKlmILD0Tp2wmf8Es5zRB\n0BsYnd4nTqMNHeQbTdQ5bfUs9cMtRE/CzPvwisIX3nhVUPQtc3vw6IuAsg0y\nYCvyrpqRqF/mwmmDFIWKnVZa2ExDAFkgaWvzOdVO1v+orLkQbvAP29k6XG47\nt7q8t6uH2iInp+tErRYzWTm+ZVFgIrvWe6/SLb1LHGYiBxCvULMDLjXA/cng\nHWumLkPjWNy6kUj9Qv/G5519smAfGoUEIc5Q2e7/u22jfGODjko08f31YgNr\nF412cUUK89FKpBv4DhSeAvXWVu3/v10g98vzaPaTf1H8RCs2wADgeWPcaqrw\nrXg3ii2HPCGI00Qhuiv6LfwieP2oHyxRiZP6IH2VA+CFV2RuPie03xHTosJm\nTf6S1c+hU1cFEg5SvP+NF0CiDVe8kn12nZMW077GjvzWsjsqTT0a7vuSxqPu\ngolJvF9rYcQVIVHMC/n+GAlTcfyLANJXwQ5R2D3GPUwD4C0HH5pdANUgiufQ\nLf98\r\n=Mfmc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICrbn0D4J7u5261gqEyYaB/Sc59gHFLvt0/dY4tNS9ubAiBX5NAB9aRgsYJjheRFiFyEXqdnVfzwe9slUumr/Dxtqw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_3.1.2_1556600623068_0.2887200458648349"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "eventemitter3", "version": "4.0.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~2.2.0", "browserify": "~16.2.0", "mocha": "~6.1.0", "nyc": "~14.1.0", "pre-commit": "~1.2.0", "sauce-browsers": "~2.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.6.0"}, "gitHead": "3673253ddeaa4398205bb9513315397e678c7fa0", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-qerSRB0p+UDEssxTtm6EDKcE7W4OaoisfIMl4CngyEhjpYglocpNg6UEqCvemdGhosAsg4sO2dXJOdyBifPGCg==", "shasum": "d65176163887ee59f386d64c82610b696a4a74eb", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.0.tgz", "fileCount": 8, "unpackedSize": 37015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCjhECRA9TVsSAnZWagAAz0sP/jkDyKdZ8sk50fqmttAc\n2UDQYGw76sKL19pI+TPqXoqhlxoP5WsdX1+Wz4MgNBOmzp/iyGoNgokW+aUJ\nbNf6trdIS76blPItTI647EzKur/4AVmAjzz9UbjRdA77HyZ3eE2V+p8vc5eK\nMtr2B6oiGwUw8Cq/1sKBZPySNElEU/OWeRRXyrym4hzYxFYPokx7DGRmYWLv\niK85HBi6aW0zoj5bpC9FzNO3yHGFI+KFKUhhp/N+R2c5bY4IfVqbdfa8dk0H\niYy+CiF3rAudBPGxX2dYukPIaMcM1AQmjyqGqE21hrs9JGxKZ2wsi5zWEYZS\nc4Wow0oQEW/FetDdJJBLp5Epp9vVzly1C/htwZoIZ8Ki9vsERKKw2ZqyY9Ps\nW5BVxdbNxKVILt5jq1qlGoNY9QI17O0mez/etVGgFu31hqoOzGclC+IvaEpI\nRX+d5QfcSrzM/TocYD7mL/TI+Wk1tw0dklFvoM2OZRRalts4F2TyTHfFDiCJ\nmArUlwkFPq4pFlM4nGDH6I3f7KT7omWooHqNNk9bkl4qpF9KdbCzioEzoD2V\nvv55C8Lxz/MDFDc2MZujbj8Q5aUoGS9L6VK07dHyrYSUnsGXnNBDhlEcn+Sa\nsAgPPs6+kLzjPyOhDltBjjj7yZwOcsK0MPaqlFdDRidn/3DJh0G/cX5EyRDN\nZWY9\r\n=WGFs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRLhH8rQSrKeEbSjs/cNc/5PVZnMDz0gXQPVgQBIidmQIhANY2y6WMnxKwJvcm4yremAU7amarTrjYrD1SJnrlG5GM"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.0_1560950851696_0.6319974648156976"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "eventemitter3", "version": "4.0.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~2.2.0", "browserify": "~16.5.0", "mocha": "~7.1.0", "nyc": "~15.0.0", "pre-commit": "~1.2.0", "sauce-browsers": "~2.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.9.0"}, "gitHead": "e8797605bfae450c67943f377e0e64f5d80dacd4", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.1", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-MnI0l35oYL2C/c80rjJN7qu50MDx39yYE7y7oYck2YA3v+y7EaAenY8IU8AP4d1RWqE8VAKWFGSh3rfP87ll3g==", "shasum": "3bcf626b0d3b16ce22ee88625a3772706300ba1f", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.1.tgz", "fileCount": 8, "unpackedSize": 37507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuEABCRA9TVsSAnZWagAA7dYP/16kWKmiPXs8v5KwW5Po\nO0mRMW7pl0y6rKsRusLfibti8xuUX796UkqbrF1G103h8W0k322O0yuCeGdp\niQDIDPAgx0lMKUKH2ky4ySs6sVw7YvKUwa0OtMHGvu0LJhWaQPiOBjlTwbL/\nRVUYJ6mfE0deMblXfbnS8IxE0/rN2T8vvTjFCIuVIXNwD5yDzZnB+oFP0FLW\n7RhOyTYPosdEK3PCOUjvnBrPEaOAYqz0ahVMuahOyqNAFOJT/tcAKN0xXOOW\nveNwncBbjQWaQv2S8v/Dt3OpjhjTLEUcbefWuTDixwP8AQ8pBHadhUuAAM9v\nppovzLm52PT0iZo9ewm8C/Dqr8Qrf84JgEjYKo2wsfFyVaia73pBRKCOugA7\nIlbEmUsZbyUrnedixb7Y2bXuJU4WZGKhhGhPNsyAs3LnYso1NZuLEsmWECIv\nOjUMc6GLcSVFnS2wSsw+BaJAyENQu7TrkH322eE4iB/CIR3fD79BqIbSAzFI\nUI9PVBpQnm6UCM5jtCnRzQnsVuih4/pHd4DBh1dklRilHThUCUUO5N7BeRQl\nm4XQKGNh5U+MlkQx305u/HXQ7OBPQxJHpYerbIhcd45uB6c9YI+4B8FuhHkl\nd6q2CtifGcBeqN/ggHxohnVU55yQMXHAgXX1s13Pvhw59+I6ROzE8+KdRsb9\nvB0E\r\n=R9PK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBlQGO87wBNlqKTQXnqm5wxm0OIIhHt1HUe30lc8tEWLAiEA8kX9WclbPJKB4xknvhgsj4pzdxXd+BCMNulQdnWajU4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.1_1589133313200_0.0028477019643091683"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "eventemitter3", "version": "4.0.2", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~2.2.0", "browserify": "~16.5.0", "mocha": "~7.1.0", "nyc": "~15.0.0", "pre-commit": "~1.2.0", "sauce-browsers": "~2.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.9.0"}, "gitHead": "6ffdbbcd28a010d7c86239e5e339a38c1c963730", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.2", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-c8VhEv9UueTlJtfhTx5mbt94l/N1nrfN8j3H1kgl/APEv/y4E95ny3lABssobpcX+4vy/fBtrdhWi44VxTSsog==", "shasum": "08daca70c33379e6b8dddc56516671f2a4c0172f", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.2.tgz", "fileCount": 8, "unpackedSize": 37800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuVgzCRA9TVsSAnZWagAA6m8P/0Esx0PohpCn1fvO/Rpk\nuzfAPzkx/zhDm0o3ltEGT2aVpdXe2aX9047u8kE0lynwdFUIiO5Td8I23Y9Q\ngM07wiyis66zVgsckJA5EucnnXGY7OWC1mV1ePMPj8mnxi1mL5bJ/ickNkrZ\npQF78lW13341IuYHTH35YzRNNxLQfcbiIQ4nFHEHUYUAi4NEiz1MC/x5OOxt\nHSI1/u6pMz4rPbHtar7rtQzCCOqVe4J5Q/d7Nx6Bx3z5EJ37TcTkp6vda4/P\nPwNB9fqszV7jHE2QZXEaw4u7mIaR973dzxm9W3YWALdslfQ80WpUv0eTtR+l\nEWtCCx/yrnQIFWmPzZ5JavpuWM5pNkV7KP5E/NWdg/AmOgmASh6gFuDJ+Rng\nj8voGKGo9zshJhIutl8ZMy2sS68ukMutDVrZ9Xl+JhIdPJGROhOeiyrL5xgG\nL+vf8xp3+eiSPfXj7/MLt0WJdueDRtyIhgKhis/KW2sAkumUVUaRfhWFk16g\nHf8arNLIDTAhtYBIjq+mUxIujsyQLayfLgoxgGWEjMcVWAeUIqEpwBEXbV9d\nQl7V1kR0/yV0t1JASDZxGQQWWk+ZwDyeRFDPjD82ejFFifCkWJLtVj/WjCGt\nVXTmPU9Sr1eS8mLVtCP+UFeWP048NxzN1L6zGyvcOe0cqqzhWjvn3RRX1OOb\nbn9N\r\n=AG2s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE1cw+Aonyjlnoxi0uRkiKwFMmK9aHwbxK5toFHs6IXzAiBdrVxHVtCejLqEGCP1dPuGa+RoeG6M3PyG92zkaK90/g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.2_1589205042722_0.9263808447016044"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "eventemitter3", "version": "4.0.3", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~2.2.0", "browserify": "~16.5.0", "mocha": "~7.1.0", "nyc": "~15.0.0", "pre-commit": "~1.2.0", "sauce-browsers": "~2.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.9.0"}, "gitHead": "5f89ad3d8e84ccd5da072a2f051997764cc9d3a9", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.3", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-HyaFeyfTa18nYjft59vEPsvuq6ZVcrCC1rBw6Fx8ZV9NcuUITBNCnTOyr0tHHkkHn//d+lzhsL1YybgtLQ7lng==", "shasum": "850b43083fdb36a246f03168f189e9054f90bdb4", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.3.tgz", "fileCount": 8, "unpackedSize": 37763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuVw+CRA9TVsSAnZWagAA50wP+gKNMf0lYlrKcZqYTAqM\nVzZovbf/ptx0kxNrILEUzrXfGzhotzpDw5PClh3OxocoB0kDZrSVS2pn8qhG\nUHJngoaMvacN1HGtjgDWALAma/gA2swhGbe4voicnEzAINH0L0g3FCN3DN9R\nS757IjonbsLy4XhoOqWQ0NQSvDfe8eYmIuD+bmXzlAzt148LZ3ryP+yjT3q8\nqqahkBcO+9bJj1uKu8SuhoiNULNJe3sWRZ+4lGDdEKuAEPDoOLmeCiLlDFzK\nvH7UySLvdMQeJC4lGwpJwxOHKqRzcIp4ehF+M8wvPcCP0P9Ybek2tdVPQUP3\nG1Ffoh2dlx4EWuLAO6SjhxiC1im90XosQf1R+NBr21Ly8otILsTMMVAYmTTV\nQnwD1L8mFZ2MqqpNn2cb73LmleSYz2g/V4tJj3CQKWXrFuVNz5pwVIhhFCTh\nUXKq/CLqZ64LrAJoxTxRlrN/IfoHrmZYx9LDHGwNOKfJzBcF2nsW5DI25bar\neOZ4AXv5Bf+7Iq6nIFH1d7shbYAKZq70OdFUbfpcccDlERCuN7zPnDU4Xj/N\nn57uKTL8FnjUgGElFacSOPMRCoIx5QBoCuZ2G1Dv+WWecm+B6bgQjnuXd4//\n8tj40loPv3VZhRfdGPNYO4z0oNPml7w/L8foUAqE4dMkzRGh2F4TvkuD+iEb\nf7Zt\r\n=3/C3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMaTM3ZpUXVV/qLizR/SQ3RKoGXlLm5ZlET+8/v/DZLwIhAOUs22QA07kv10iulygaX5a1YfjBmyR8B3mp7PwVzV8I"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.3_1589206078038_0.7199565811605375"}, "_hasShrinkwrap": false}, "4.0.4": {"name": "eventemitter3", "version": "4.0.4", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "~2.2.0", "browserify": "~16.5.0", "mocha": "~7.1.0", "nyc": "~15.0.0", "pre-commit": "~1.2.0", "sauce-browsers": "~2.0.0", "sauce-test": "~1.3.3", "uglify-js": "~3.9.0"}, "gitHead": "b73f1902ae9564b70c960e363a904118804f8392", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.4", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-rlaVLnVxtxvoyLsQQFBx53YmXHDxRIzzTLbdfxqi4yocpSjAxXwkU0cScM5JgSKMqEhrZpnvQ2D9gjylR0AimQ==", "shasum": "b5463ace635a083d018bdc7c917b4c5f10a85384", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.4.tgz", "fileCount": 8, "unpackedSize": 37780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuojlCRA9TVsSAnZWagAAUQIP/ijIqEN/78xdqiV6NdY6\ngf69c1ptPoiBIQbTJLQZiKd4CxmPNp0yXotTu30rkIfEkfvsmDwZII2HLZZG\nvd1NsS5kG7biTQk0fa2G16CImFLwoBsxk1KZrbmu/X63GtdCfmCSmCVKu/Sz\nIUkPTeQP8NoPKDs0ZWK32XgZ3qvRItmA/lQfj+PwnWqW3Vw4PqDJIWUtSAEL\nWmNaZreCIzElYX3pzJE3ccBnx011QnvXiEEMe2EgBrHl1qDfdrgLTVxVfjLj\nQUW9wVI6kh+irOKjNdYHbWtygJmX74As2F3T4XAHgqOpSgxbFZycxeUqALgQ\nsSMOkcsdWcXwX3sCOQ+sV7GB7oJk2S7YfLmFbfqYdepTeytSe7aftbD+zbC1\nbSF+mxqbXBNJjvB2XprWTd8NHgiDksyDNE2VWHNSBacN4bQXYtnc3gNerd4k\neGwl3n6j2FyY6cyH24eE5BtqouWJvy/e8IHgbS4wLmbzJ1x9cwh0bEsuJOzf\nEkAzED+MephrBNaRBvjDGdwrPFRsZ5QzFR3BxoQ4Uu7Usu0jQeupCzUs/+yY\nE7pMd+etJ6a9XgFBjj3um891aE1qTpjd2iERyQqldIQAnf7O69lq+BTlJIqF\ntuAEbhQnAZd+rcCZrUYCk4Ls+W4TCj9L0y4pqzT1ggk/crUxl/kkWxFuhfGw\nKNh4\r\n=rUKI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDg5Kcmu6CWTLyAIJ6t5b7j53ODozo07FqPWgnQdfiCHQIgCtcW8Z/ju+R/VkcRGHMPW42jU7Ec+jw6I+ag4rinTsA="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.4_1589283044478_0.019940923541620803"}, "_hasShrinkwrap": false}, "4.0.5": {"name": "eventemitter3", "version": "4.0.5", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "^2.2.0", "browserify": "^16.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "pre-commit": "^1.2.0", "sauce-browsers": "^2.0.0", "sauce-test": "^1.3.3", "uglify-js": "^3.9.0"}, "gitHead": "47251a0649bec2e030ff6f11d056713a31985af9", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.5", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-QR0rh0YiPuxuDQ6+T9GAO/xWTExXpxIes1Nl9RykNGTnE1HJmkuEfxJH9cubjIOQZ/GH4qNBR4u8VSHaKiWs4g==", "shasum": "51d81e4f1ccc8311a04f0c20121ea824377ea6d9", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.5.tgz", "fileCount": 8, "unpackedSize": 37792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQOOVCRA9TVsSAnZWagAAphoQAJtDutCAL8K6VV7UZOwG\nkopJjm9XmxHkAKx/ENe0HG0YMkasPnoHY1kMoKlvJbOjK6algYi4J2Co+ACK\n0Dx4D6G7asPwN0DOQTaJ7O876Apiz+elXlBH47Syh35xgeUVMel9GwwttXx1\nYIfmYSfEyM9R16aFowT7afWMY4N+s/rcgH+zMw/k6h7PrF+wKKGBhBMkS5FN\nyfJ0Arir2WBSn5lmVms/oX3ZOY+kBX90PE491hVFs6em1HjS8fTLt87Vmw+b\n0t49B7g8jpiOzd9BHOwI7lAvy+Q8y9X40abYen/a6S20R0OdBAn8+TcDjR2c\n+jlFJLI3meJkdaCzHv7vQk/TDylHbFk5Ej6foofgKL/2wO2tevU2c1dIJoZv\ny+W+v3aKFFWr4SNqJvLJP83u60xck/Ij3uM2utHZ2aIU2k4u07PxIFcLWb8n\nP4rYVUgKHZzNo2AnbhYETvfHWg8x031gPEBquEY13ajRQUCdhU/HgGhqQiE1\ndPMdc94whJyGTe9bMSUi2YIoO7tBnJuYKTjcVeg+XR8yqSRQ0+HZtH7wovDd\n5nqw/6+SNmhtDBgAMDtCXScK+WFzxeNgR8zUKZ8gaLknI4swQiV/KqKtVSSD\nxg8Hw5Xa2mMMq3Z+9r9NaHXX8Lkumsx+rn+ZHZU79vbnzGCxbkM8LpSKKQlN\nexQq\r\n=8Mou\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD862dS86Fm5CoNPYQJxH3Fn6Cqj0er6xt+LjnSiwwMJgIgV4bpFqmrrLa2bortx787wa1SV140kGTekV4/H12z6Ik="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.5_1598088084558_0.6499614230865858"}, "_hasShrinkwrap": false}, "4.0.6": {"name": "eventemitter3", "version": "4.0.6", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "^2.2.0", "browserify": "^16.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "pre-commit": "^1.2.0", "sauce-browsers": "^2.0.0", "sauce-test": "^1.3.3", "uglify-js": "^3.9.0"}, "gitHead": "38de307205e78c574a53379b71f53058d45236c8", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.6", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-s3GJL04SQoM+gn2c14oyqxvZ3Pcq7cduSDqy3sBFXx6UPSUmgVYwQM9zwkTn9je0lrfg0gHEwR42pF3Q2dCQkQ==", "shasum": "1258f6fa51b4908aadc2cd624fcd6e64f99f49d6", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.6.tgz", "fileCount": 8, "unpackedSize": 37849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRUR+CRA9TVsSAnZWagAAaaQP/1kEhIVrZcYvKC8P9QkH\njsuUiWdMOlI1KlXmkN4WaLSWY2A3w7hK2I7lYFe1e86LW8w63SERxO0+1LRc\nWIQGVSK7GAA3rPsva/OMk3pk3vZJGQSv1g141gDuZxckspVzvk/p4YRyiBTY\nloHeTvQW1D8iOrsBXnKlZ4uq1sT5EqvG3RbCPa0zLrYSvcfiNOKrOxxSjbxR\nFFF2Ewjoutp9BJtcfbpJnYjFJZDmNohO5ie6+NFK1gCwzKJqqyx524IllJC4\nBkkym2WRiRvHMUhwTQkmGILfiupTxjDvl4XqAh1BabKh1GO8/2XDGgBv8jHu\nwm4joCU+nFfFFudA/AjRhxrRUAsXZUcL3GGa3yC0mrdIqUQflKoT3BjoYm9B\n2/IN64RX5Vtc1uU7aQQr2Pm+DxYAZpIZHmcAveR/BLGDTg43HXyc7MHMbgD/\ng8PCberJty37kqYMGidw69d3sl7OGP/mtYrswTL97dOCJwDD9iB72zGGefG9\n02qQttBNSzRbYQGzwxqNeML04/oedAbIZnEVPY1Fc3A1SGAtbarHSTmStV9P\nWguN2ibRF+7f3FnDxULRSVmZSH3Rk7rkikSkungEKMozLuFUt8LwwM4+rriL\nPDcSQUdFT+nEv0vXtn4i8qnKRD9RRFEJDOfSR/zonk+WKF+MCYLRRXszi9Mr\nwH5o\r\n=Q3kF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICHCRW9zE69fIAqm5lek2eXrrrMuOesSZv4YbmHWFadPAiEA+Yh0Gp3NG114W/xDeLGF25pTX4z1N4/W1KgR9GHVaMA="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.6_1598375037581_0.9154824044601892"}, "_hasShrinkwrap": false}, "4.0.7": {"name": "eventemitter3", "version": "4.0.7", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "^2.2.0", "browserify": "^16.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "pre-commit": "^1.2.0", "sauce-browsers": "^2.0.0", "sauce-test": "^1.3.3", "uglify-js": "^3.9.0"}, "gitHead": "00ac01a329b7f2fb29058b6a3aff6850ac304f12", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@4.0.7", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "shasum": "2de9b68f6528d5644ef5c59526a1b4a07306169f", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "fileCount": 8, "unpackedSize": 37967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfR3iVCRA9TVsSAnZWagAA+1YP/RN7vVEWHMzDnY73iQbq\n092QpDsL71RWTTM7Gezwv2L1ANE+GXLA1ZuVZ7LpFMOwkvYv4ep7TuUbVL3I\nuXq60DSql7VVzKhAPL5omm9XaWwbPk7RzuNtstl4u+XcOW+QUEf87JT4SIz/\nZtNKbfvvHArGre5BY1RP2WWmNn4YnPOuGYEehoUeI1l72Ni4uAj0HIJFPak/\nh5SbLshoewiHrYH7IchgXR+gjQAgMeRoLqeGIgEFDl78hejNFZ1FR21B69LM\n9ApOc1I6ghZy04QjfOYlliCgt526fkhlingzjQe5M8czBNvrrpz6rQMZPkHy\nYHLcuEqaoAZW+gK6hlBjhGRHqTgTuLCwn9DfB02YaeFg3InEdzFgnOhv9QiE\nQR146zowzAStPvIPPX+Yb6Ip9M1lIeTwVGLjqW8zkTlQ/wYJU+nUnArMYi6r\nfVesF2Ud3JPmSWLcgiwlhc8Nntm0poBF1Vut/Cyo67D0MLq2qqf9VZ8fFxuM\ngTmuDylKrhHDzGrW9rCdz2v4mP8o1s8D51z1MVUeBaiUS2xTP9r2ROE5bDRN\nk04pbQgawdAxWoFnHO0AATBZBxsvs5WQqCitv+o/4UyTxHyPnJvR9vh+q4q/\nwtrW3TXZsUSzYu2LMTFD2XUYjnmmglTjg/yIMQe/vyN1/hFn4els9ADvzzc7\nnqbC\r\n=x1qZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICSW9UZsMD9iAQbRfhbjqeug7OaiVUrz2CSuchFerVLVAiANxQFHtQC3rqHc9Sj65TPgaASp24iFyL2uCiGAGjfmGA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "luigi<PERSON><PERSON>@gmail.com", "name": "lpinca"}, {"email": "<EMAIL>", "name": "v1"}], "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_4.0.7_1598519444845_0.23416295962222833"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "eventemitter3", "version": "5.0.0", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}, "./package.json": "./package.json"}, "main": "index.js", "types": "index.d.ts", "scripts": {"rollup": "rimraf dist umd && rollup -c", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "c8 --reporter=lcov --reporter=text mocha test/test.js", "test-esm": "mocha test/test.mjs", "prepublishOnly": "npm run rollup", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-terser": "^0.1.0", "assume": "^2.2.0", "c8": "^7.3.1", "mocha": "^10.0.0", "pre-commit": "^1.2.0", "rimraf": "^3.0.2", "rollup": "^3.4.0", "sauce-browsers": "^3.0.0", "sauce-test": "^1.3.3"}, "gitHead": "3cace551f52a890d6c5a74860c2988c4702efd00", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@5.0.0", "_nodeVersion": "19.1.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-riuVbElZZNXLeLEoprfNYoDSwTBRR44X3mnhdI1YcnENpWTCsTTVZ2zFuqQcpoyqPQIUXdiPEU0ECAq0KQRaHg==", "shasum": "084eb7f5b5388df1451e63f4c2aafd71b217ccb3", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.0.tgz", "fileCount": 12, "unpackedSize": 73238, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/zADrKfdMZjwcJOtHDSBLxvRMeK43f84zR9ZdQbajjQIhAJskBHhVImMWAsxIYLlniG8KDlOUXvfmltx0ToCzeOki"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhOm8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrszg//SNb3Kn+AQO+WvJamkUGJ5KbibCoUXio2eI5cKMa5YX5OAZ+N\r\nQLfs+KM2QbstxP6fbIEbcxG4JPR4p8jhii8kroMDYTFaKlia7d+Ost4tp/KY\r\nBeyabXabHBX7UsIVQN3WBl1Il4q4rJ6RLPhYLp6LKheREySHjwRQW2b+CQiE\r\n15WlcJPy0yvUOpys6hjD5YW5eMxNo/bfRuGLyzxfnvHgUsbXdu/P2SN2TkAh\r\n42Q7Ikk9Q6oMtXnB/ME3/OjhqyGquAuX4W0TcQr4ECh8kC1bYl9BMgvn9eF6\r\nE454Y16NZjzUo/2sc08YHfaZSjykohh31vWDjvA6n6/YKbKvRCZLaAVQZL3I\r\nu5YS85VcPQThISfgRllZB6pRQ5sVRICMTizDWUX33/UYL8zksknKApFZg0qm\r\nWHn0gD5VJslpY6DX6dgC8sHwLqXJUa9UmUDpsCRunNgBHMvDn3gYiKEARdej\r\nRNiBaBT0Y+LrtyLbtL5IZcMqE6xgvMdhyoiIHIse9Ny9eie9aHUdt9HMyHA/\r\n/6wjYLx9paPrlADW6PGVLlS8BHrpS21oMg3PRXRZBu8hosLiy0rYbU2ric/U\r\nG/SCBSXuqNyEVJpb8lUBlDnbr70SbwucPbOXPmiIxhOjbYWFXDHPdjJ9GSR4\r\nrYmnYFTZjcAGFvmcDR/p72ChicGXUz8enok=\r\n=QCj6\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_5.0.0_1669654972198_0.7770720485985418"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "eventemitter3", "version": "5.0.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "main": "index.js", "types": "index.d.ts", "scripts": {"rollup": "rimraf dist umd && rollup -c", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "c8 --reporter=lcov --reporter=text mocha test/test.js", "test-esm": "mocha test/test.mjs", "prepublishOnly": "npm run rollup", "test-browser": "node test/browser.js"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-terser": "^0.4.0", "assume": "^2.2.0", "c8": "^7.3.1", "mocha": "^10.0.0", "pre-commit": "^1.2.0", "rimraf": "^4.1.2", "rollup": "^3.4.0", "sauce-browsers": "^3.0.0", "sauce-test": "^1.3.3"}, "gitHead": "a650549176ded0d0dcc52e27b03ae5669d7dcd7c", "homepage": "https://github.com/primus/eventemitter3#readme", "_id": "eventemitter3@5.0.1", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==", "shasum": "53f5ffd0a492ac800721bb42c66b841de96423c4", "tarball": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "fileCount": 12, "unpackedSize": 73390, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGSc0AXdD26F0MJWlqrR1gxSF7rcpwA0Grsz9Nf5ioZ5AiAjKjpvBI/KhNk2EvoZkR5Vn1eYPKhR1fjQ3Sn9LJ3Blw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTsY6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7CQ//UIz2wMRs3HMNaGtTPnIwXji2jKW3wnx6+fjRVNDgwLUtQPSz\r\nTiNx5j2XLj82Rdo23oKp20p/qkEMbyVtOIgFGeJciEWOC+25Vw4ntl9DZmBK\r\neQw8XoekDcuc0kMRqm3ZQfext+qCL6bTofhHW3yFfybeL/Nmco6bMDgWi5zI\r\nzn5Pqg0AumHTSJYufmzfiFWZ0PV/1XYJnUHIHL4fbH+kXbHcwfxOwULdVe9K\r\nH1lt/l73uCALOo8b9AVseCtxysv/xcICgLWLpq1xjY6Cyj/lyZVwuzAK8X3O\r\nWvj7jKB+/jVefbbromZgB5eAe7TPl0Kgsva18S4xHJETVBh8fZngb12Yj5SN\r\nS256/eDsJb9MdiRu/7rYjew6q+dQhzdXz2hPHT1UVnETQlU07XH3k+gqlfo6\r\nQ+kCA37d9/HuKHiQiuT3shGSpsdlX0rMzD0NT5CGG0FWjOQJu2panU2nkV0n\r\nGjSOHgzZL+y9oOB4DWtzFAkhZ3Ib7wMBRw6a5RZIUkuSyRQ8CYejrng66QzK\r\n6mlx2ZUnThv77CQV61pvJJl2Gj8Qyqyesg8caESQx9moJLotwH7ErGXqcIhD\r\nWZqUkw7sR8QFtAbP30kPiXQb/dsN5ql+MkvSXgpUjtpR1mSyJ8cugzZ9UQwb\r\ngXDnNgmnn8A5NXsClyKJkFhOK18I2rvTxfM=\r\n=mvWT\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eventemitter3_5.0.1_1682884154334_0.954440463620783"}, "_hasShrinkwrap": false}}, "readme": "# EventEmitter3\n\n[![Version npm](https://img.shields.io/npm/v/eventemitter3.svg?style=flat-square)](https://www.npmjs.com/package/eventemitter3)[![CI](https://img.shields.io/github/actions/workflow/status/primus/eventemitter3/ci.yml?branch=master&label=CI&style=flat-square)](https://github.com/primus/eventemitter3/actions?query=workflow%3ACI+branch%3Amaster)[![Coverage Status](https://img.shields.io/coveralls/primus/eventemitter3/master.svg?style=flat-square)](https://coveralls.io/r/primus/eventemitter3?branch=master)\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/eventemitter3.svg)](https://saucelabs.com/u/eventemitter3)\n\nEventEmitter3 is a high performance EventEmitter. It has been micro-optimized\nfor various of code paths making this, one of, if not the fastest EventEmitter\navailable for Node.js and browsers. The module is API compatible with the\nEventEmitter that ships by default with Node.js but there are some slight\ndifferences:\n\n- Domain support has been removed.\n- We do not `throw` an error when you emit an `error` event and nobody is\n  listening.\n- The `newListener` and `removeListener` events have been removed as they\n  are useful only in some uncommon use-cases.\n- The `setMaxListeners`, `getMaxListeners`, `prependListener` and\n  `prependOnceListener` methods are not available.\n- Support for custom context for events so there is no need to use `fn.bind`.\n- The `removeListener` method removes all matching listeners, not only the\n  first.\n\nIt's a drop in replacement for existing EventEmitters, but just faster. Free\nperformance, who wouldn't want that? The EventEmitter is written in EcmaScript 3\nso it will work in the oldest browsers and node versions that you need to\nsupport.\n\n## Installation\n\n```bash\n$ npm install --save eventemitter3\n```\n\n## CDN\n\nRecommended CDN:\n\n```text\nhttps://unpkg.com/eventemitter3@latest/dist/eventemitter3.umd.min.js\n```\n\n## Usage\n\nAfter installation the only thing you need to do is require the module:\n\n```js\nvar EventEmitter = require('eventemitter3');\n```\n\nAnd you're ready to create your own EventEmitter instances. For the API\ndocumentation, please follow the official Node.js documentation:\n\nhttp://nodejs.org/api/events.html\n\n### Contextual emits\n\nWe've upgraded the API of the `EventEmitter.on`, `EventEmitter.once` and\n`EventEmitter.removeListener` to accept an extra argument which is the `context`\nor `this` value that should be set for the emitted events. This means you no\nlonger have the overhead of an event that required `fn.bind` in order to get a\ncustom `this` value.\n\n```js\nvar EE = new EventEmitter()\n  , context = { foo: 'bar' };\n\nfunction emitted() {\n  console.log(this === context); // true\n}\n\nEE.once('event-name', emitted, context);\nEE.on('another-event', emitted, context);\nEE.removeListener('another-event', emitted, context);\n```\n\n### Tests and benchmarks\n\nThis module is well tested. You can run:\n\n- `npm test` to run the tests under Node.js.\n- `npm run test-browser` to run the tests in real browsers via Sauce Labs.\n\nWe also have a set of benchmarks to compare EventEmitter3 with some available\nalternatives. To run the benchmarks run `npm run benchmark`.\n\nTests and benchmarks are not included in the npm package. If you want to play\nwith them you have to clone the GitHub repository.\nNote that you will have to run an additional `npm i` in the benchmarks folder\nbefore `npm run benchmark`.\n\n## License\n\n[MIT](LICENSE)\n", "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-04-30T19:49:14.614Z", "created": "2013-09-25T19:19:08.536Z", "0.0.0": "2013-09-25T19:19:12.021Z", "0.0.1": "2013-10-09T15:36:58.698Z", "0.1.0": "2014-01-13T20:24:52.761Z", "0.1.1": "2014-01-15T21:17:43.720Z", "0.1.2": "2014-04-09T10:59:42.145Z", "0.1.3": "2014-08-21T07:37:37.939Z", "0.1.4": "2014-08-21T09:02:10.457Z", "0.1.5": "2014-08-22T07:12:30.643Z", "0.1.6": "2014-11-17T19:08:04.989Z", "1.0.0": "2015-04-22T08:53:23.576Z", "1.0.1": "2015-04-22T13:39:04.428Z", "1.0.2": "2015-05-07T08:32:32.355Z", "1.0.3": "2015-05-12T08:44:05.088Z", "1.1.0": "2015-05-12T10:43:40.461Z", "1.1.1": "2015-06-14T14:09:40.824Z", "1.2.0": "2016-03-16T17:17:43.950Z", "2.0.0": "2016-09-09T12:19:40.852Z", "2.0.1": "2016-09-27T06:59:08.929Z", "2.0.2": "2016-09-30T06:23:12.675Z", "2.0.3": "2017-03-31T14:51:09.611Z", "3.0.0": "2017-11-29T17:24:06.788Z", "3.0.1": "2018-02-07T07:57:37.228Z", "3.1.0": "2018-04-25T19:53:53.968Z", "3.1.1": "2019-04-29T16:23:08.792Z", "3.1.2": "2019-04-30T05:03:43.245Z", "4.0.0": "2019-06-19T13:27:31.801Z", "4.0.1": "2020-05-10T17:55:13.351Z", "4.0.2": "2020-05-11T13:50:42.859Z", "4.0.3": "2020-05-11T14:07:58.155Z", "4.0.4": "2020-05-12T11:30:44.612Z", "4.0.5": "2020-08-22T09:21:24.706Z", "4.0.6": "2020-08-25T17:03:57.720Z", "4.0.7": "2020-08-27T09:10:44.974Z", "5.0.0": "2022-11-28T17:02:52.385Z", "5.0.1": "2023-04-30T19:49:14.500Z"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "homepage": "https://github.com/primus/eventemitter3#readme", "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"V1": true, "borjes": true, "yxqme": true, "koulmomo": true, "lpinca": true, "mikemimik": true, "nickleefly": true, "rochejul": true, "wangnan0610": true, "ziflex": true, "hifaraz": true, "shanewholloway": true, "codebyren": true, "largepuma": true, "mojaray2k": true, "staydan": true, "rocket0191": true, "lestad": true, "zhongyuan": true, "brend": true, "kontrax": true, "xyyjk": true, "oleg_tsyba": true, "arnold-almeida": true, "nbuchanan": true, "travis346": true, "drmercer": true, "dwqs": true, "cedx": true, "addamx": true, "perevezentsev": true, "nilz3ro": true, "r_java": true, "jarvis1024": true, "black-black-cat": true, "heartnett": true, "cr8tiv": true, "arcticicestudio": true, "sinahwz": true, "dandrewgarvin": true}}