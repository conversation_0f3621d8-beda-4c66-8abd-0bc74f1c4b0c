from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import pandas as pd
import requests
import json
import os
from datetime import datetime

app = Flask(__name__)
CORS(app)

# DeepSeek API配置
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_API_URL = "https://api.deepseek.com/chat/completions"

# 加载数据
df = pd.read_excel('veg_price.xlsx')
# 标准化列名
df.columns = ['date', 'max_price', 'min_price', 'variety']
df['date'] = pd.to_datetime(df['date'], format='mixed')

def extract_relevant_data(user_question, max_rows=1000):
    """根据用户问题提取相关数据"""
    question_lower = user_question.lower()

    # 提取特定菜品
    mentioned_varieties = []
    for variety in df['variety'].unique():
        if variety.lower() in question_lower:
            mentioned_varieties.append(variety)

    # 根据问题类型筛选数据
    filtered_df = df.copy()

    # 如果提到了特定菜品，优先显示这些菜品的数据
    if mentioned_varieties:
        filtered_df = filtered_df[filtered_df['variety'].isin(mentioned_varieties)]

    # 根据问题类型进行不同的采样策略
    if '趋势' in question_lower or '变化' in question_lower:
        # 对于趋势分析，按时间排序并采样
        filtered_df = filtered_df.sort_values('date')
        if len(filtered_df) > max_rows:
            # 均匀采样以保持时间分布
            step = len(filtered_df) // max_rows
            filtered_df = filtered_df.iloc[::step]

    elif '排名' in question_lower or '最贵' in question_lower or '最便宜' in question_lower:
        # 对于排名分析，按价格排序
        if '最贵' in question_lower:
            filtered_df = filtered_df.nlargest(max_rows, 'max_price')
        elif '最便宜' in question_lower:
            filtered_df = filtered_df.nsmallest(max_rows, 'max_price')
        else:
            # 显示价格范围的代表性样本
            high_price = filtered_df.nlargest(max_rows//2, 'max_price')
            low_price = filtered_df.nsmallest(max_rows//2, 'max_price')
            filtered_df = pd.concat([high_price, low_price])

    elif '对比' in question_lower or '比较' in question_lower:
        # 对于对比分析，确保每个类别都有代表性数据
        if not mentioned_varieties:
            # 如果没有指定具体对象，选择主要的菜品
            top_varieties = df['variety'].value_counts().head(5).index
            filtered_df = filtered_df[filtered_df['variety'].isin(top_varieties)]

        # 按类别分组采样
        if len(filtered_df) > max_rows:
            if mentioned_varieties or 'variety' in question_lower:
                filtered_df = filtered_df.groupby('variety').apply(
                    lambda x: x.sample(min(len(x), max_rows//len(filtered_df['variety'].unique())))
                ).reset_index(drop=True)

    else:
        # 默认情况：随机采样，但保持数据的代表性
        if len(filtered_df) > max_rows:
            # 分层采样：确保每个菜品和市场都有代表
            sampled_dfs = []
            for variety in filtered_df['variety'].unique()[:10]:  # 限制菜品数量
                variety_data = filtered_df[filtered_df['variety'] == variety]
                sample_size = min(len(variety_data), max_rows // 10)
                sampled_dfs.append(variety_data.sample(sample_size))
            filtered_df = pd.concat(sampled_dfs).drop_duplicates()

    # 最终确保不超过最大行数
    if len(filtered_df) > max_rows:
        filtered_df = filtered_df.sample(max_rows)

    return filtered_df

def format_data_for_llm(data_df):
    """将数据格式化为适合LLM处理的文本格式"""
    if len(data_df) == 0:
        return "没有找到相关数据。"

    # 基本统计信息
    summary = f"""
数据概览：
- 数据条数：{len(data_df)}
- 时间范围：{data_df['date'].min().strftime('%Y-%m-%d')} 到 {data_df['date'].max().strftime('%Y-%m-%d')}
- 涉及菜品：{', '.join(data_df['variety'].unique()[:10])}{'...' if len(data_df['variety'].unique()) > 10 else ''}

价格统计：
- 最高价格：{data_df['max_price'].max():.2f}元
- 最低价格：{data_df['min_price'].min():.2f}元
- 平均最高价：{data_df['max_price'].mean():.2f}元
- 平均最低价：{data_df['min_price'].mean():.2f}元

"""

    # 详细数据样本（前50行）
    sample_data = data_df.head(50)
    data_text = "\n详细数据样本：\n"
    data_text += "日期,菜品,最高价,最低价\n"

    for _, row in sample_data.iterrows():
        data_text += f"{row['date'].strftime('%Y-%m-%d')},{row['variety']},{row['max_price']:.2f},{row['min_price']:.2f}\n"

    if len(data_df) > 50:
        data_text += f"\n... 还有 {len(data_df) - 50} 行数据\n"

    # 关键统计信息
    stats_text = "\n关键统计信息：\n"

    # 按菜品统计
    variety_stats = data_df.groupby('variety').agg({
        'max_price': ['mean', 'max', 'min'],
        'min_price': ['mean', 'max', 'min']
    }).round(2)

    stats_text += "\n各菜品价格统计（平均最高价，最高价，最低价）：\n"
    for variety in variety_stats.index[:10]:  # 只显示前10个
        max_avg = variety_stats.loc[variety, ('max_price', 'mean')]
        max_high = variety_stats.loc[variety, ('max_price', 'max')]
        max_low = variety_stats.loc[variety, ('max_price', 'min')]
        stats_text += f"{variety}: 平均{max_avg:.2f}元, 最高{max_high:.2f}元, 最低{max_low:.2f}元\n"

    return summary + data_text + stats_text

def call_deepseek_api(user_question, relevant_data):
    """调用DeepSeek API"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
    }

    prompt = f"""
你是一个专业的数据分析师。用户提供了一个关于菜品价格变动的数据表：
以下是根据用户问题筛选的相关数据：
{relevant_data}

用户问题：{user_question}

请基于这些实际数据进行深入分析，直接回答用户的问题：

要求：
- 请用markdown格式回答，包含清晰的标题和结构，要求排版美观，字体不要太大，不要加emoji！
- 在分析中引用具体的数据点和数值！
- 如果数据中有异常值或特殊情况，请特别指出！

总字数不要超过400字！总字数不要超过400字！总字数不要超过400字！
"""

    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "stream": False
    }

    try:
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        return result['choices'][0]['message']['content']
    except Exception as e:
        return f"API调用失败: {str(e)}"

def generate_data_summary():
    """生成数据摘要"""
    summary = f"""
    数据集基本信息：
    - 总记录数：{len(df)}
    - 时间范围：{df['date'].min().strftime('%Y-%m-%d')} 到 {df['date'].max().strftime('%Y-%m-%d')}
    - 菜品种类：{df['variety'].nunique()}种菜品

    主要菜品：{', '.join(df['variety'].value_counts().head(10).index.tolist())}

    价格范围：
    - 最高价格：{df['max_price'].max():.2f}元
    - 最低价格：{df['min_price'].min():.2f}元
    - 平均最高价：{df['max_price'].mean():.2f}元
    - 平均最低价：{df['min_price'].mean():.2f}元
    """
    return summary

def create_intelligent_chart(user_question, relevant_data_df):
    """基于用户问题和相关数据智能生成图表"""
    question_lower = user_question.lower()

    # 更智能的菜品名称匹配
    mentioned_varieties = []

    # 改进的菜品匹配逻辑
    for variety in df['variety'].unique():
        # 完全匹配
        if variety in user_question:
            mentioned_varieties.append(variety)
        # 部分匹配（处理"白菜"匹配"小白菜"、"大白菜"的情况）
        elif any(part in variety for part in user_question.split() if len(part) >= 2):
            mentioned_varieties.append(variety)

    # 去重
    mentioned_varieties = list(set(mentioned_varieties))

    print(f"Debug: 问题='{user_question}', 提取的菜品={mentioned_varieties}")

    # 根据问题类型和数据特征选择图表类型
    charts = []

    # 1. 特定菜品对比（优先级最高）
    if len(mentioned_varieties) >= 2 and any(keyword in question_lower for keyword in ['对比', '比较', '差异', '和']):
        chart_data = create_variety_comparison_chart(relevant_data_df, mentioned_varieties, user_question)
        if chart_data:
            charts.append(chart_data)

    # 2. 趋势分析图表
    elif any(keyword in question_lower for keyword in ['趋势', '变化', '时间', '发展', '走势']):
        chart_data = create_trend_chart(relevant_data_df, mentioned_varieties, user_question)
        if chart_data:
            charts.append(chart_data)

    # 3. 排名/对比图表
    elif any(keyword in question_lower for keyword in ['排名', '最贵', '最便宜', '最高', '最低', '前', '后', '哪些']):
        chart_data = create_ranking_chart(relevant_data_df, mentioned_varieties, user_question)
        if chart_data:
            charts.append(chart_data)

    # 4. 分布图表
    elif any(keyword in question_lower for keyword in ['分布', '区间', '范围']):
        chart_data = create_distribution_chart(relevant_data_df, mentioned_varieties, user_question)
        if chart_data:
            charts.append(chart_data)

    # 5. 如果没有匹配到特定类型，根据数据特征自动选择
    else:
        chart_data = create_auto_chart(relevant_data_df, mentioned_varieties, user_question)
        if chart_data:
            charts.append(chart_data)

    return charts

def create_variety_comparison_chart(data_df, varieties, question):
    """创建菜品对比图表数据"""
    if len(data_df) == 0 or len(varieties) < 2:
        return None

    # 准备图表数据
    chart_data = {
        'labels': [],
        'datasets': []
    }

    # 为每个菜品创建数据集
    colors = ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)',
              'rgba(255, 205, 86, 1)', 'rgba(153, 102, 255, 1)']

    for i, variety in enumerate(varieties[:5]):  # 最多显示5种菜品
        variety_data = data_df[data_df['variety'] == variety]
        if len(variety_data) > 0:
            # 按月聚合数据
            monthly_data = variety_data.groupby(variety_data['date'].dt.to_period('M')).agg({
                'max_price': 'mean'
            }).reset_index()
            monthly_data['date'] = monthly_data['date'].dt.to_timestamp()

            # 如果是第一个菜品，设置标签
            if i == 0:
                chart_data['labels'] = [date.strftime('%Y-%m') for date in monthly_data['date']]

            # 添加数据集
            chart_data['datasets'].append({
                'label': variety,
                'data': monthly_data['max_price'].round(2).tolist(),
                'borderColor': colors[i % len(colors)],
                'backgroundColor': colors[i % len(colors)].replace('1)', '0.1)'),
                'tension': 0.4
            })

    title = f"{' vs '.join(varieties[:3])}{'等' if len(varieties) > 3 else ''} 价格对比"

    return {
        'type': 'variety_comparison',
        'title': title,
        'data': chart_data
    }

def create_trend_chart(data_df, varieties, question):
    """创建趋势图表数据"""
    if len(data_df) == 0:
        return None

    # 准备图表数据
    chart_data = {
        'labels': [],
        'datasets': []
    }

    # 如果指定了特定菜品，显示该菜品的趋势
    if varieties:
        variety = varieties[0]  # 只显示第一个菜品
        variety_data = data_df[data_df['variety'] == variety]
        if len(variety_data) > 0:
            # 按月聚合数据
            monthly_data = variety_data.groupby(variety_data['date'].dt.to_period('M')).agg({
                'max_price': 'mean'
            }).reset_index()
            monthly_data['date'] = monthly_data['date'].dt.to_timestamp()

            chart_data['labels'] = [date.strftime('%Y-%m') for date in monthly_data['date']]
            chart_data['datasets'].append({
                'label': f'{variety} 价格趋势',
                'data': monthly_data['max_price'].round(2).tolist(),
                'borderColor': 'rgba(54, 162, 235, 1)',
                'backgroundColor': 'rgba(54, 162, 235, 0.1)',
                'tension': 0.4
            })

        title = f"{variety} 价格趋势"
    else:
        # 显示整体价格趋势
        monthly_data = data_df.groupby(data_df['date'].dt.to_period('M')).agg({
            'max_price': 'mean'
        }).reset_index()
        monthly_data['date'] = monthly_data['date'].dt.to_timestamp()

        chart_data['labels'] = [date.strftime('%Y-%m') for date in monthly_data['date']]
        chart_data['datasets'].append({
            'label': '平均价格',
            'data': monthly_data['max_price'].round(2).tolist(),
            'borderColor': 'rgba(54, 162, 235, 1)',
            'backgroundColor': 'rgba(54, 162, 235, 0.1)',
            'tension': 0.4
        })

        # 根据问题生成更相关的标题
        if '白菜' in question and not varieties:
            # 如果问题提到白菜但没有找到精确匹配，查找相关菜品
            related_varieties = [v for v in data_df['variety'].unique() if '白菜' in v]
            if related_varieties:
                title = f"白菜类菜品价格趋势"
            else:
                title = "菜品价格趋势"
        else:
            title = "菜品价格趋势"

    return {
        'type': 'price_trend',
        'title': title,
        'data': chart_data
    }

def create_ranking_chart(data_df, varieties, question):
    """创建排名图表数据"""
    if len(data_df) == 0:
        return None

    # 根据问题确定排序方式
    if '最贵' in question.lower() or '最高' in question.lower():
        ascending = False
        title_suffix = "价格排名 (从高到低)"
    else:
        ascending = True
        title_suffix = "价格排名 (从低到高)"

    # 按菜品计算平均价格并排序
    variety_avg = data_df.groupby('variety')['max_price'].mean().sort_values(ascending=ascending)

    # 取前15名
    top_varieties = variety_avg.head(15)

    # 准备图表数据
    chart_data = {
        'labels': top_varieties.index.tolist(),
        'datasets': [{
            'label': '平均价格 (元)',
            'data': top_varieties.round(2).tolist(),
            'backgroundColor': [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)',
                'rgba(199, 199, 199, 0.8)',
                'rgba(83, 102, 255, 0.8)',
                'rgba(255, 99, 255, 0.8)',
                'rgba(99, 255, 132, 0.8)',
                'rgba(255, 192, 203, 0.8)',
                'rgba(173, 216, 230, 0.8)',
                'rgba(240, 230, 140, 0.8)',
                'rgba(221, 160, 221, 0.8)',
                'rgba(255, 218, 185, 0.8)'
            ]
        }]
    }

    return {
        'type': 'variety_ranking',
        'title': f'菜品{title_suffix}',
        'data': chart_data
    }



def create_distribution_chart(data_df, varieties, question):
    """创建价格分布图表数据"""
    if len(data_df) == 0:
        return None

    # 创建价格区间
    price_ranges = ['0-5元', '5-10元', '10-15元', '15-20元', '20-25元', '25-30元', '30元以上']
    counts = []

    # 统计各价格区间的菜品数量
    for i, price_range in enumerate(price_ranges):
        if i == 0:  # 0-5元
            count = len(data_df[(data_df['max_price'] >= 0) & (data_df['max_price'] < 5)])
        elif i == len(price_ranges) - 1:  # 30元以上
            count = len(data_df[data_df['max_price'] >= 30])
        else:  # 其他区间
            lower = i * 5
            upper = (i + 1) * 5
            count = len(data_df[(data_df['max_price'] >= lower) & (data_df['max_price'] < upper)])
        counts.append(count)

    # 准备图表数据
    chart_data = {
        'labels': price_ranges,
        'datasets': [{
            'data': counts,
            'backgroundColor': [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)',
                'rgba(199, 199, 199, 0.8)'
            ]
        }]
    }

    if varieties and len(varieties) <= 3:
        title = f"{', '.join(varieties)} 价格分布"
    else:
        title = "菜品价格分布"

    return {
        'type': 'price_distribution',
        'title': title,
        'data': chart_data
    }

def create_auto_chart(data_df, varieties, question):
    """自动选择合适的图表类型"""
    if len(data_df) == 0:
        return None

    # 根据数据特征自动选择图表类型
    unique_varieties = data_df['variety'].nunique()
    date_range = (data_df['date'].max() - data_df['date'].min()).days

    # 如果时间跨度较长，优先显示趋势
    if date_range > 30:
        return create_trend_chart(data_df, varieties, question)
    # 如果菜品种类较多，显示排名
    elif unique_varieties > 5:
        return create_ranking_chart(data_df, varieties, question)
    # 默认显示分布
    else:
        return create_distribution_chart(data_df, varieties, question)


@app.route('/')
def index():
    return render_template('index.html')

@app.route('/test-markdown')
def test_markdown():
    return render_template('test_markdown.html')

@app.route('/api/analyze', methods=['POST'])
def analyze():
    try:
        data = request.json
        user_question = data.get('question', '')
        
        if not user_question:
            return jsonify({'error': '请提供问题'}), 400
        
        # 根据用户问题提取相关数据
        relevant_data_df = extract_relevant_data(user_question)

        # 将数据格式化为LLM可理解的文本
        formatted_data = format_data_for_llm(relevant_data_df)

        # 调用DeepSeek API
        analysis_report = call_deepseek_api(user_question, formatted_data)

        # 生成数据摘要（用于返回给前端）
        data_summary = generate_data_summary()
        
        # 基于用户问题和相关数据智能生成图表
        charts = create_intelligent_chart(user_question, relevant_data_df)
        
        return jsonify({
            'analysis_report': analysis_report,
            'charts': charts,
            'data_summary': data_summary
        })
        
    except Exception as e:
        return jsonify({'error': f'分析失败: {str(e)}'}), 500

@app.route('/api/data-info')
def data_info():
    """获取数据基本信息"""
    try:
        info = {
            'total_records': len(df),
            'date_range': {
                'start': df['date'].min().strftime('%Y-%m-%d'),
                'end': df['date'].max().strftime('%Y-%m-%d')
            },
            'varieties': df['variety'].unique().tolist(),
            'price_stats': {
                'max_price_range': [float(df['max_price'].min()), float(df['max_price'].max())],
                'min_price_range': [float(df['min_price'].min()), float(df['min_price'].max())],
                'avg_max_price': float(df['max_price'].mean()),
                'avg_min_price': float(df['min_price'].mean())
            }
        }
        return jsonify(info)
    except Exception as e:
        return jsonify({'error': f'获取数据信息失败: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
