{"_id": "side-channel", "_rev": "9-88aff442749c515b8f8f68541416078c", "name": "side-channel", "dist-tags": {"latest": "1.1.0"}, "versions": {"1.0.1": {"name": "side-channel", "version": "1.0.1", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel#readme", "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "dist": {"shasum": "4fb6c60e13bf4a69baf1b219c50b7feb87cf5c30", "tarball": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-KhfWUIMFxTnJ1HTWiHhzPZL6CVZubPUFWcaIWY4Fc/551CazpDodWWTVTeJI8AjsC/JpH4fW6hmDa10Dnd4lRg==", "signatures": [{"sig": "MEYCIQDAo/ez+5GIparHCbuVXoOGG2FbBpmct4DY/A1JgO/TJQIhALj4KQlsXU0uqOWcClVDBTI/0gKLSeIQzQCvsMPBiVGw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5MEwCRA9TVsSAnZWagAAjiQP/A8USD9Z5AYNzz1+ZkRA\nFzlWnS6HXhVJjbqJ1VSpMqV2HS0hOoHofeQELpFwew0+sQypOZMn5DP4YunG\nfnw7jdB68aImz4LvuSvJK6XVRgTk5TbUh/o6h1s6V7KISz5+lhcGN6RkdPlM\n9DgWY/Gb5bhCk27wgzrPgJO5EeerymsFMKWUDbKvUZwW09h9DweYEf7f7hGj\ni5ASQPfWVtNkSuo4GKulSdbiYkunHBshdS7t74Lbpj+bBm7RfYwfM3EwIluT\nTCMsRe55q1QZ7glxw5xPYgg4p7Eq1nwHOc5jR9GBrbuiOeA0/0nZlcgjlZZ3\n0VKVyrkYuGIPShJ4HLX07dZqvPTtPZ62k7baLCNCeRjRqchi4Q+JxMgO1005\n6wsW777hftAEnTLNcs7gI3MsemjI9DvhE5SH46l67JhislFaP8XLT9HoiPEo\n2t0PZRxUFQYcTZrWSOY9gkX3WanW43e3CqLd63+QsZEclfGCGFwvT6mQaQcY\nL6NsN1ShduK1pOVCFRDRRz6LMXgHRbz9PXKD1jkLo7KVlMlTZlzFWjBZjXrD\n8kg4eac9j+8Y8quTxCQg9MErpR3Q/t2d2fA+ovQB+PbMyA/7On9puoeSqz//\nubvKwtPdORuoW0/F6heij4VN4mTpMBfg7g8c0TsZkuRjrKzgJfHpglxAj5RV\nfSdc\r\n=XYTu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "eeb5be33213cf47dc88c426bb5f2ab22bd8537df", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"es-abstract": "^1.16.2", "object-inspect": "^1.7.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.11.0", "eslint": "^6.7.2", "auto-changelog": "^1.16.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel_1.0.1_1575272751876_0.5565220906335961", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "side-channel", "version": "1.0.2", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel#readme", "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "dist": {"shasum": "df5d1abadb4e4bf4af1cd8852bf132d2f7876947", "tarball": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-7rL9YlPHg7Ancea1S96Pa8/QWb4BtXL/TZvS6B8XFetGBeuhAsfmUspK6DokBeZ64+Kj9TCNRD/30pVz1BvQNA==", "signatures": [{"sig": "MEQCIDra8YW7upp8Ky4X7ooA/RRvtMUCGJmOnzGDmi7YX8RMAiAuqzFGRY8XiFb568y7m2unurdSF5D0R8WHeH4a6Xp7Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/STlCRA9TVsSAnZWagAAPjAP/j4WktUvVtkGIiUQMJms\nAwWdqFamcdaa3L1BtsAodU7NnFZyazbhN1BCZK/XS9f2gMWuFULeVMS8/11M\n4vHVIkk72HjL8F8H8wJNIvKppE7NB2cVgg+whFvvJy2C8UTMopAIVDE+6bmO\nv2+zTpliXrc603NupDVz8/+8uQ+vD3IAz2Xn7KV6xV7+hzogZDxz8qfb/7iZ\nV3kmvdziqpT6YUNV0oCIzeL1JLdPwwsCLgHb/TPtUrl1asG6PRlEZlJ3frvh\nfYQMfkMWEfdZQr/kuOOhn3rK5YEfTdq+xfbEDFdsILVjih4Emrf7Rd7asNpQ\nt0nDUm4elaPRyPmRFyCM97lUGiEjJI5bhktYNfOSa+Rf1MQ6M1dcik4TnqOT\nsF1u/dtUREGC5/V/ywZjwOyVUH9iZWWbPk/4v94zp6RKrdvzHtxKqePrNZS8\n1kC/W9hUdnYwHnK5ElbxM/1ek3/evk2d0937xJAUFHD8dE0SBSo/KOjCw8r2\n0N51QvXTn5x87sPJA3wQVO45FvdqG9ZjMR3Z4wJf+ci8H2LTW7Uruq1W+SMk\nmqJPmOVAwzZK/jl9nBFBaiK+8FN9/dWQTcQYwyAYF7EQY9tddjJoVfM29kLb\nr/+DzUpEYXOWz+2xSmlcEjforkNBOuX/pkeHi8a9KUlAkUi6CsfaoixOBjAq\nZJGY\r\n=r0sg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f68d532990cdd2e4e7542241787ea3d103340765", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"es-abstract": "^1.17.0-next.1", "object-inspect": "^1.7.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.12.0", "eslint": "^6.7.2", "auto-changelog": "^1.16.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel_1.0.2_1576871141427_0.82681961516941", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "side-channel", "version": "1.0.3", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel#readme", "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "dist": {"shasum": "cdc46b057550bbab63706210838df5d4c19519c3", "tarball": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-A6+ByhlLkksFoUepsGxfj5x1gTSrs+OydsRptUxeNCabQpCFUvcwIczgOigI8vhY/OJCnPnyE9rGiwgvr9cS1g==", "signatures": [{"sig": "MEYCIQC3Xsd8p98KDEb8h8qJqUpFgZWrnJ+/fWZTommBKku8HgIhAPuVSbKzEsyxfaUelPSm3lxe637Lt33dVtFR5FxDEyas", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQs+BCRA9TVsSAnZWagAAr1UP/jqJmJjGWJh/v/UzZALV\nCQ1JkQEAQY7Zrn7cHyJnGFGzRZnhiftxyrO8xgofXZjLwFMY8DajjfkgLKPP\nfaVBNYoWi1fBaINjGBNMXJykc8pzi9Elrlbqpn3V2ZdkfrfB8Bf29Djlff01\n27J27p4PQjEaraJWApvyvDUqMHJWQyXqBjuRi6vK3YxZ2S5kb5JqU8b0KGtg\na/PHXaxaw8ZROiiehnS9WLcFUHEV+nGa+ysrqCrFbAYD4+bvCdmLch5p91kp\n/qcK3+/8x/SJqJtmqSLvVOngUGMHdmg9TVfUz1n5NgJ3JjGqyLe0NfVNK1qd\n8bW/67XUKbH3yEQ4PgP2woviQarOVd4nHnuzrDna6dGNTMeifK9uxAwUZ7OB\nCzyPA5uMfHslXO8eW1QDjvsBPVPjfx4tarz9lInwrkgpe86N6WeCSt/8wQsD\nzvCRCi1TI2UjF6xWUh1JjG6G+xXO60B5usrk2sEZ3oKYmecCg9vx3ldCaJHY\nPJKBmdm95G3mPAg+4UX9ITcVgc0IuH1RJ9375MwPln7C9hpmS+sPIO0RYrHJ\nGmJxTbhaazQsCtMCsyhznKZ4UwHYq6n3t4ef9LvGeGIqSq4/xS5aUQkemTl0\nordeoERxyZHp4H6K5NV1ve5Cm6NZfyAxpUyjzh1PoTEr1FTPNvYav7qQXSSp\nboU5\r\n=boBE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "22f79686135bc418165ff05d066518fe6c7d7ba1", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"es-abstract": "^1.18.0-next.0", "object-inspect": "^1.8.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.2", "tape": "^5.0.1", "eslint": "^7.7.0", "auto-changelog": "^2.2.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel_1.0.3_1598214016718_0.07108655703290734", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "side-channel", "version": "1.0.4", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel@1.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel#readme", "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "dist": {"shasum": "efce5c8fdc104ee751b25c58d4290011fa5ea2cf", "tarball": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "signatures": [{"sig": "MEQCIFB39X9YAUVh2o+8m4lSvSbke7Ln7P9qw0vPtBQeU+JrAiAwOW9FYqAKM/HCPBLRoVNjSPQXiQvJD5urjjcNkU95Qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf64pUCRA9TVsSAnZWagAAoAAP/i0VVskWE8oNYCJ9opkk\nNAcVM1r8KvTcYXzuXvE/v0iB3sYcbGr4dYbBQwDg9BbsOhlVN6qzMRx2h9/x\nEOmEDOcJ+z14dma1qQsxo127GFaVP/zBPmq4cvqn5k1xkeQ67pdhZP3t1Nwc\nQrRQKbsJELypDxVVCydlEkMWX0MjIKTnpVVXWgxXon7A/GzAElMrjPBsuz+g\nFcDzCYFZm+1cx9R5zi8jt70QRcfTL0thCnW0jEgBex3WQe7gs8LaJeWhWxQJ\n1N1mZjS+ky6jKgyy+4x+O5uvoyHZh51VCq1Ty0C1N0uyBTgcZZ3OZdjnZCdF\npe/Wr9iyo52BfCMucckA0GG9re5GYhACtzlbzwkfted60Yg2Wa+O302Xx7zQ\naLz3qlqCjsnoSZ/ETK4Ozs0MbX2SiKpDsZdHyfXi3+gHs4bBujm9+yNPaUqx\nYQ772RFvmN4dnpP5epn+elAx1Tt581Wo0YS5veu41LbvTGAh2G1JaIOHna71\nVlwZApDD5AyG5G2QJ9Zo4vAClMUy3KSGvMlVHyB7qd92QcqP4uzNamoprB95\n2aJkYBHR4H4mjlMZzJVAbfDlPuCVUZxmjHjzcmhQvwVpqmq9vCWc32K786pD\n7GuDf1xkFdhxW2ZujRaPa+gEYtaLyAbKKQafzU/ZtmQvwJFLx1h8t0njMsX7\nQHGw\r\n=fsMQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "5c3c3fe90858eff0dd5c24d1f6a8351f8810755e", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.16.0", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel_1.0.4_1609271892338_0.6413545452249854", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "side-channel", "version": "1.0.5", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel@1.0.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel#readme", "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "dist": {"shasum": "9a84546599b48909fb6af1211708d23b1946221b", "tarball": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.5.tgz", "fileCount": 10, "integrity": "sha512-QcgiIWV4WV7qWExbN5llt6frQB/lBven9pqliLXfGPB+K9ZYXxDozp0wLkHS24kWCm+6YXH/f0HhnObZnZOBnQ==", "signatures": [{"sig": "MEUCIQC4912sjWaANs3sA2cU74sBcg6lRa8gXKe7wCIszMsFZgIgEY+IbTa2N3b5PNqsKX5P354NpSPud8bljlX+JLE/x8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17893}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "4d4f39b360bffd627b637ec31b37d17b02ed9fba", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "21.6.0", "dependencies": {"call-bind": "^1.0.6", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "object-inspect": "^1.13.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.4", "eclint": "^2.8.1", "eslint": "=8.8.0", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel_1.0.5_1707238139477_0.8125806962076723", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "side-channel", "version": "1.0.6", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel@1.0.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel#readme", "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "dist": {"shasum": "abd25fb7cd24baf45466406b1096b7831c9215f2", "tarball": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.6.tgz", "fileCount": 12, "integrity": "sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==", "signatures": [{"sig": "MEUCIHOI5N1dOREuZ4zRQ89UmUi5kxiQ8oW7dhoR0/RSCO66AiEArO63ZAp/s8pucbSGTLTS0JbsTIKkM9NjArUrznKLJLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23240}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c9ac2b5ce66d0024d4590392ac4651db790dd0dc", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p .", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "21.6.2", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "object-inspect": "^1.13.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.5", "eclint": "^2.8.1", "eslint": "=8.8.0", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.2", "@ljharb/eslint-config": "^21.1.0", "@types/object-inspect": "^1.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel_1.0.6_1709233404147_0.09441883501048087", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "side-channel", "version": "1.1.0", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel.git"}, "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "homepage": "https://github.com/ljharb/side-channel#readme", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "side-channel@1.1.0", "gitHead": "d65a5e20fdd9a87c62b0c334e601275e1860c803", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "shasum": "c3fcff9c4da932784873335ec9765fa94ff66bc9", "tarball": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "fileCount": 12, "unpackedSize": 21545, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICkSw2o1JydrynOP7Z2qmjj3jqhMCtMsSiDNf5c1SP+9AiEAiES2DB3OeYsKTo+m7wMGGvPGqkletLoU3ZYTxszu6mw="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/side-channel_1.1.0_1733936433390_0.5985940579488518"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-12-02T07:41:11.448Z", "modified": "2024-12-11T17:00:33.736Z", "1.0.0": "2019-12-02T07:41:11.607Z", "1.0.1": "2019-12-02T07:45:52.012Z", "1.0.2": "2019-12-20T19:45:41.557Z", "1.0.3": "2020-08-23T20:20:16.888Z", "1.0.4": "2020-12-29T19:58:12.459Z", "1.0.5": "2024-02-06T16:48:59.623Z", "1.0.6": "2024-02-29T19:03:24.392Z", "1.1.0": "2024-12-11T17:00:33.553Z"}, "bugs": {"url": "https://github.com/ljharb/side-channel/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/side-channel#readme", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel.git"}, "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# side-channel <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nStore information about any JS value in a side channel. Uses WeakMap if available.\n\nWarning: in an environment that lacks `WeakMap`, this implementation will leak memory until you `delete` the `key`.\n\n## Getting started\n\n```sh\nnpm install --save side-channel\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst getSideChannel = require('side-channel');\n\nconst channel = getSideChannel();\n\nconst key = {};\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n\nchannel.set(key, 42);\n\nchannel.assert(key); // does not throw\nassert.equal(channel.has(key), true);\nassert.equal(channel.get(key), 42);\n\nchannel.delete(key);\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/side-channel\n[npm-version-svg]: https://versionbadg.es/ljharb/side-channel.svg\n[deps-svg]: https://david-dm.org/ljharb/side-channel.svg\n[deps-url]: https://david-dm.org/ljharb/side-channel\n[dev-deps-svg]: https://david-dm.org/ljharb/side-channel/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/side-channel#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/side-channel.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/side-channel.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/side-channel.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=side-channel\n[codecov-image]: https://codecov.io/gh/ljharb/side-channel/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/side-channel/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/side-channel\n[actions-url]: https://github.com/ljharb/side-channel/actions\n", "readmeFilename": "README.md"}