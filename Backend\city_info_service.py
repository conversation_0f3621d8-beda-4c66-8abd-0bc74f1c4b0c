"""
市情概况服务模块
提供城市介绍、领导班子、景点推荐等信息的流式输出功能
"""

import os
import time
import json
from typing import Generator, Dict, Any


class CityInfoService:
    """市情概况服务"""

    def __init__(self):
        # 获取项目根目录
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.city_md_path = os.path.join(self.project_root, 'DB', 'city.md')

    def get_city_introduction_stream(self) -> Generator[Dict[str, Any], None, None]:
        """
        流式输出城市介绍内容
        
        Yields:
            dict: 包含流式数据的字典
        """
        try:
            print(f"🏙️ 开始流式输出城市介绍: {self.city_md_path}")

            if not os.path.exists(self.city_md_path):
                yield {
                    'type': 'error',
                    'content': '城市介绍文件不存在',
                    'finished': True
                }
                return

            # 读取markdown文件内容
            with open(self.city_md_path, 'r', encoding='utf-8') as file:
                content = file.read()

            # 处理内容，将图片路径转换为markdown图片格式
            lines = content.split('\n')
            processed_lines = []

            for line in lines:
                # 处理图片路径行
                if line.strip().startswith('pic/') and line.strip().endswith('.png'):
                    # 转换为markdown图片格式
                    img_path = line.strip()
                    img_name = img_path.split('/')[-1].replace('.png', '')
                    processed_lines.append(f'![{img_name}]({img_path})')
                else:
                    processed_lines.append(line)

            processed_content = '\n'.join(processed_lines)

            # 按字符流式输出
            current_content = ""
            for i, char in enumerate(processed_content):
                current_content += char
                
                # 每5个字符或遇到标点符号时输出一次
                if (i + 1) % 5 == 0 or char in '。！？，；：\n':
                    yield {
                        'type': 'content',
                        'content': current_content,
                        'finished': False
                    }
                    time.sleep(0.05)  # 控制输出速度
            
            # 最终输出
            yield {
                'type': 'content',
                'content': current_content,
                'finished': True
            }

        except Exception as e:
            print(f"Error in city introduction stream: {e}")
            yield {
                'type': 'error',
                'content': f'读取城市介绍时出现错误: {str(e)}',
                'finished': True
            }

    def get_leadership_info(self) -> Dict[str, Any]:
        """
        获取领导班子信息
        
        Returns:
            dict: 领导班子信息
        """
        # 模拟领导班子数据
        leadership_data = {
            'success': True,
            'data': [
                {
                    'name': '张某某',
                    'position': '市委书记',
                    'photo': 'pic/leader1.jpg',
                    'description': '主持市委全面工作，负责党的建设、组织工作等。'
                },
                {
                    'name': '李某某',
                    'position': '市长',
                    'photo': 'pic/leader2.jpg',
                    'description': '主持市政府全面工作，负责经济发展、民生保障等。'
                },
                {
                    'name': '王某某',
                    'position': '常务副市长',
                    'photo': 'pic/leader3.jpg',
                    'description': '协助市长工作，负责政府常务工作、发展改革等。'
                },
                {
                    'name': '刘某某',
                    'position': '副市长',
                    'photo': 'pic/leader4.jpg',
                    'description': '负责教育、文化、卫生、体育等工作。'
                }
            ]
        }
        
        return leadership_data

    def get_attractions_info(self) -> Dict[str, Any]:
        """
        获取景点推荐信息
        
        Returns:
            dict: 景点推荐信息
        """
        # 模拟景点推荐数据
        attractions_data = {
            'success': True,
            'data': [
                {
                    'name': '某某古城',
                    'category': '历史文化',
                    'rating': 4.8,
                    'image': 'pic/attraction1.jpg',
                    'description': '拥有2500年历史的古城，保存完好的明清建筑群，是国家5A级旅游景区。',
                    'address': '某某市古城区中心',
                    'opening_hours': '08:00-18:00',
                    'ticket_price': '免费'
                },
                {
                    'name': '某某湖风景区',
                    'category': '自然风光',
                    'rating': 4.6,
                    'image': 'pic/attraction2.jpg',
                    'description': '碧波荡漾的湖泊，四季景色各异，是休闲度假的理想之地。',
                    'address': '某某市西郊',
                    'opening_hours': '06:00-20:00',
                    'ticket_price': '30元'
                },
                {
                    'name': '某某博物馆',
                    'category': '文化教育',
                    'rating': 4.7,
                    'image': 'pic/attraction3.jpg',
                    'description': '展示本地历史文化的综合性博物馆，馆藏文物丰富。',
                    'address': '某某市文化广场',
                    'opening_hours': '09:00-17:00（周一闭馆）',
                    'ticket_price': '免费（需预约）'
                },
                {
                    'name': '某某山国家森林公园',
                    'category': '自然风光',
                    'rating': 4.5,
                    'image': 'pic/attraction4.jpg',
                    'description': '森林覆盖率达95%，空气清新，是登山健身的好去处。',
                    'address': '某某市东南部',
                    'opening_hours': '07:00-18:00',
                    'ticket_price': '50元'
                }
            ]
        }
        
        return attractions_data

    def format_leadership_response(self, leadership_data: Dict[str, Any]) -> str:
        """
        格式化领导班子信息响应
        
        Args:
            leadership_data: 领导班子数据
            
        Returns:
            str: 格式化后的响应内容
        """
        if not leadership_data.get('success'):
            return "抱歉，暂时无法获取领导班子信息。"
        
        response = "## 某某市领导班子\n\n"
        
        for leader in leadership_data['data']:
            response += f"### {leader['position']} - {leader['name']}\n\n"
            response += f"{leader['description']}\n\n"
            response += "---\n\n"
        
        response += "💡 **说明**：以上信息仅供参考，具体职务分工以官方最新公布为准。"
        
        return response

    def format_attractions_response(self, attractions_data: Dict[str, Any]) -> str:
        """
        格式化景点推荐信息响应
        
        Args:
            attractions_data: 景点推荐数据
            
        Returns:
            str: 格式化后的响应内容
        """
        if not attractions_data.get('success'):
            return "抱歉，暂时无法获取景点推荐信息。"
        
        response = "## 某某市景点推荐\n\n"
        
        for attraction in attractions_data['data']:
            response += f"### 🏛️ {attraction['name']}\n\n"
            response += f"**类型**：{attraction['category']} | **评分**：⭐ {attraction['rating']}/5.0\n\n"
            response += f"**简介**：{attraction['description']}\n\n"
            response += f"📍 **地址**：{attraction['address']}\n\n"
            response += f"🕒 **开放时间**：{attraction['opening_hours']}\n\n"
            response += f"🎫 **门票价格**：{attraction['ticket_price']}\n\n"
            response += "---\n\n"
        
        response += "💡 **温馨提示**：出行前请关注景区官方信息，确认开放状态和票价变动。"
        
        return response
