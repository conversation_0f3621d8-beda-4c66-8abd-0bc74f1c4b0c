{"_id": "call-bound", "_rev": "5-8533d7bdaaa49c7d2337e4b3c9d56ef7", "name": "call-bound", "dist-tags": {"latest": "1.0.4"}, "versions": {"1.0.1": {"name": "call-bound", "version": "1.0.1", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "call-bound@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/call-bound#readme", "bugs": {"url": "https://github.com/ljharb/call-bound/issues"}, "dist": {"shasum": "4b48be3bf3c0869dd8e5a8a7e071921d6d398495", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.1.tgz", "fileCount": 11, "integrity": "sha512-BsIq9Q4b/cM9JPRCYCeYrMwUC/UUgLTidqtVfaJTPH+L6bS+JYwI7mPwxw4Nl+4/SAkwZ2/6EYR6Cf3XIZ8+RQ==", "signatures": [{"sig": "MEYCIQDv4QWOSQGRR+LpdirXu7toGHMDNhf8WNQ6+o0EA0b2DgIhAOp94n00ERKbHkytiYOjT018sPe67U1Er5dsMO2zn6fH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10624}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3b56a6f6ae73524b24d4873e145fe61793867756", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/ljharb/call-bound.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Robust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"call-bind": "^1.0.7", "get-intrinsic": "^1.2.4"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "gopd": "^1.1.0", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/call-bound_1.0.1_1733438548091_0.*****************", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "call-bound", "version": "1.0.2", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "call-bound@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/call-bound#readme", "bugs": {"url": "https://github.com/ljharb/call-bound/issues"}, "dist": {"shasum": "9dbd4daf9f5f753bec3e4c8fbb8a2ecc4de6c39b", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.2.tgz", "fileCount": 11, "integrity": "sha512-0lk0PHFe/uz0vl527fG9CgdE9WdafjDbCXvBbs+LUv000TVt2Jjhqbs4Jwm8gz070w8xXyEAxrPOMullsxXeGg==", "signatures": [{"sig": "MEYCIQC0XMlUsfMhHo2LGVXEUY26hPbc+O/MEekKNO7UlsNJbgIhAPgYO8gdlAP4DSthUbrZrMb6tMsVI06LwbDvAlhrCles", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11181}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "548e51b50ba10fe260768eb198ce882097831813", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/ljharb/call-bound.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Robust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"call-bind": "^1.0.8", "get-intrinsic": "^1.2.5"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "gopd": "^1.2.0", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/call-bound_1.0.2_1733848973151_0.32190092107418367", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.3": {"name": "call-bound", "version": "1.0.3", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "call-bound@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/call-bound#readme", "bugs": {"url": "https://github.com/ljharb/call-bound/issues"}, "dist": {"shasum": "41cfd032b593e39176a71533ab4f384aa04fd681", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.3.tgz", "fileCount": 11, "integrity": "sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==", "signatures": [{"sig": "MEYCIQCkMn+LCstD0IdrnD/wyjFUcB86vjhezSMsjOTX7xrX6QIhAOJQnvBENPNMMsPT6aOr1VRII/Js/XSqTpGyogIcTL6E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12021}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "788152ab3f26195099e540e28f62cb55bcad8830", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/ljharb/call-bound.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Robust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.", "directories": {}, "sideEffects": false, "_nodeVersion": "23.4.0", "dependencies": {"get-intrinsic": "^1.2.6", "call-bind-apply-helpers": "^1.0.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "gopd": "^1.2.0", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/call-bound_1.0.3_1734310625205_0.01919825658550911", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.4": {"name": "call-bound", "version": "1.0.4", "description": "Robust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=auto", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bound.git"}, "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/call-bound/issues"}, "homepage": "https://github.com/ljharb/call-bound#readme", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.3.0", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "gopd": "^1.2.0", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "call-bound@1.0.4", "gitHead": "75d922c1f92a3de1ff9ab94453f45b5f64b50041", "types": "./index.d.ts", "_nodeVersion": "23.9.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "shasum": "238de935d2a2a692928c538c7ccfa91067fd062a", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "fileCount": 11, "unpackedSize": 17106, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDBV4HqqB+gGBZypN0mLd7alhxFA5D8ZGm+mBeCB1nyJAiEAyvuZQ/flopoGNRE9Azbsjp0bf/nB+HkBL8oVv7Od5Qg="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/call-bound_1.0.4_1741024203341_0.12171862790790833"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-05T22:42:28.090Z", "modified": "2025-03-03T17:50:03.694Z", "1.0.0": "2024-06-10T09:00:51.313Z", "1.0.1": "2024-12-05T22:42:28.281Z", "1.0.2": "2024-12-10T16:42:53.435Z", "1.0.3": "2024-12-16T00:57:05.369Z", "1.0.4": "2025-03-03T17:50:03.505Z"}, "bugs": {"url": "https://github.com/ljharb/call-bound/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/call-bound#readme", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bound.git"}, "description": "Robust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# call-bound <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nRobust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.\n\n## Getting started\n\n```sh\nnpm install --save call-bound\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst callBound = require('call-bound');\n\nconst slice = callBound('Array.prototype.slice');\n\ndelete Function.prototype.call;\ndelete Function.prototype.bind;\ndelete Array.prototype.slice;\n\nassert.deepEqual(slice([1, 2, 3, 4], 1, -1), [2, 3]);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/call-bound\n[npm-version-svg]: https://versionbadg.es/ljharb/call-bound.svg\n[deps-svg]: https://david-dm.org/ljharb/call-bound.svg\n[deps-url]: https://david-dm.org/ljharb/call-bound\n[dev-deps-svg]: https://david-dm.org/ljharb/call-bound/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/call-bound#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/call-bound.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/call-bound.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/call-bound.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=call-bound\n[codecov-image]: https://codecov.io/gh/ljharb/call-bound/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/call-bound/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/call-bound\n[actions-url]: https://github.com/ljharb/call-bound/actions\n", "readmeFilename": "README.md"}