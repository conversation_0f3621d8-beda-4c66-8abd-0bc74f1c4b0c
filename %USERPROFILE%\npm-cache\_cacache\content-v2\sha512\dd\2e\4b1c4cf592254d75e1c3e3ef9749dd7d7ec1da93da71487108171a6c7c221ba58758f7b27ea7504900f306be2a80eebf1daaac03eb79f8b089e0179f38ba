{"_id": "secure-compare", "_rev": "8-e23dde791bc4b2c61aec03da457c565b", "name": "secure-compare", "description": "Securely compare two strings, copied from cryptiles", "dist-tags": {"latest": "3.0.1"}, "versions": {"0.9.0": {"name": "secure-compare", "version": "0.9.0", "description": "Constant-time comparison algorithm to prevent timing attacks", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"url": "https://github.com/vdemedes/secure-compare.git", "type": "git"}, "bugs": {"url": "https://github.com/vdemedes/secure-compare/issues"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.0.1"}, "scripts": {"test": "mocha"}, "dependencies": {"bufferpack": "0.0.6"}, "gitHead": "80ef0ccbe998fa6d45e7322df04e24230650dad3", "homepage": "https://github.com/vdemedes/secure-compare", "_id": "secure-compare@0.9.0", "_shasum": "88060173e61c8f862b56a48250ce9a1e50c6ed77", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "vdemedes", "email": "<EMAIL>"}, "maintainers": [{"name": "vdemedes", "email": "<EMAIL>"}], "dist": {"shasum": "88060173e61c8f862b56a48250ce9a1e50c6ed77", "tarball": "https://registry.npmjs.org/secure-compare/-/secure-compare-0.9.0.tgz", "integrity": "sha512-mKYtT+cpEgq9cnhMQ2+PlsLpReyf5dhIM88qL0nbXpAUZXM77S64pIFBLkJFGAc3X8hSknYT7a+yD3/35vedcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqXW0k7cIdBbJROm/1uVmlCM9kbegbRAVa/bJsRKUNHAIgDMiPHn8rWbisNbyiu+9KClknXVWa9vSO/D960s/OQvo="}]}}, "3.0.1": {"name": "secure-compare", "version": "3.0.1", "description": "Securely compare two strings, copied from cryptiles", "main": "index.js", "scripts": {"test": "mocha test"}, "repository": {"type": "git", "url": "https://github.com/vdemedes/secure-compare.git"}, "keywords": ["secure", "compare"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/vdemedes/secure-compare/issues"}, "homepage": "https://github.com/vdemedes/secure-compare", "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.1"}, "gitHead": "13176da54bc4223f435d082a224559b6e168a490", "_id": "secure-compare@3.0.1", "_shasum": "f1a0329b308b221fae37b9974f3d578d0ca999e3", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "vdemedes", "email": "<EMAIL>"}, "maintainers": [{"name": "vdemedes", "email": "<EMAIL>"}], "dist": {"shasum": "f1a0329b308b221fae37b9974f3d578d0ca999e3", "tarball": "https://registry.npmjs.org/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha512-<PERSON>ckIIV90rPDcBcglUwXPF3kg0P0qmPsPXAj6BBEENQE1p5yA1xfmDJzfi1Tappj37Pv2mVbKpL3Z1T+Nn7k1Qw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8UbPjyYRsUqul0jSuxhzj1qyI3T/sz/1tz8i15O7AtQIgOMS1XIdNhJLP/JrsT56QGNjRY7g6b67hGedZDibJ3C8="}]}}}, "readme": "# secure-compare\n\nConstant-time comparison algorithm to prevent timing attacks for Node.js.\nCopied from [cryptiles](https://github.com/hapijs/cryptiles) by [C J Silverio](https://github.com/ceejbot).\n\n\n### Installation\n\n```\n$ npm install secure-compare --save\n```\n\n\n### Usage\n\n```javascript\nvar compare = require('secure-compare');\n\ncompare('hello world', 'hello world').should.equal(true);\ncompare('你好世界', '你好世界').should.equal(true);\n\ncompare('hello', 'not hello').should.equal(false);\n```\n\n\n### Tests\n\n```\n$ npm test\n```\n\n\n### License\n\nsecure-compare is released under the MIT license.\n", "maintainers": [{"email": "<EMAIL>", "name": "vdemedes"}], "time": {"modified": "2022-06-26T17:13:41.302Z", "created": "2014-12-13T10:43:57.306Z", "0.9.0": "2014-12-13T10:43:57.306Z", "3.0.0": "2015-03-29T13:02:29.390Z", "3.0.1": "2015-03-30T22:10:32.432Z"}, "homepage": "https://github.com/vdemedes/secure-compare", "repository": {"type": "git", "url": "https://github.com/vdemedes/secure-compare.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/vdemedes/secure-compare/issues"}, "license": "MIT", "readmeFilename": "README.md", "keywords": ["secure", "compare"]}