import openpyxl
from datetime import datetime

# 读取Excel文件
workbook = openpyxl.load_workbook('DB/veg_price.xlsx')
sheet = workbook.active

print(f'工作表名称: {sheet.title}')
print(f'最大行数: {sheet.max_row}')
print(f'最大列数: {sheet.max_column}')

# 读取前几行数据来了解结构
print('\n前10行数据:')
for row_num in range(1, min(11, sheet.max_row + 1)):
    row_data = []
    for col_num in range(1, sheet.max_column + 1):
        cell_value = sheet.cell(row=row_num, column=col_num).value
        row_data.append(cell_value)
    print(f'第{row_num}行: {row_data}')

# 获取列标题（假设第一行是标题）
headers = []
for col_num in range(1, sheet.max_column + 1):
    header = sheet.cell(row=1, column=col_num).value
    headers.append(header)
print(f'\n列标题: {headers}')

# 统计一些基本信息
print(f'\n数据行数（不包括标题）: {sheet.max_row - 1}')

# 查看一些样本数据
print('\n样本数据（第2-6行）:')
for row_num in range(2, min(7, sheet.max_row + 1)):
    row_data = []
    for col_num in range(1, sheet.max_column + 1):
        cell_value = sheet.cell(row=row_num, column=col_num).value
        row_data.append(cell_value)
    print(f'  {row_data}')

workbook.close()
