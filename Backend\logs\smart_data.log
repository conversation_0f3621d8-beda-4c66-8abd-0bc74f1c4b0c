2025-08-03 21:14:35,006 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:14:35,021 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:14:35,021 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:14:35,345 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:14:35,345 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:14:35,347 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 21:14:35,437 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 21:14:35,438 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 21:14:35,440 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:14:36,412 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:14:36,426 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:14:36,426 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:14:36,721 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:14:36,721 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:14:36,723 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 21:14:36,767 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:14:36,770 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:15:05,127 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:15:05] "OPTIONS /ask HTTP/1.1" 200 -
2025-08-03 21:15:05,133 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:15:05] "POST /ask HTTP/1.1" 200 -
2025-08-03 21:15:27,780 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:15:27] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 21:15:27,783 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 21:15:27,785 - app - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:15:27,785 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 21:15:27,785 - app - INFO - ✅ 输入安全校验通过
2025-08-03 21:15:27,786 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 21:15:27,786 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 21:15:27,787 - SmartData - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:15:27,787 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 21:15:27,787 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 21:15:27,787 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 21:15:27,788 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 21:15:27,789 - SmartData - INFO - 🥬 识别到特定蔬菜品种: ['白菜']
2025-08-03 21:15:27,790 - SmartData - INFO - 📊 按品种筛选后数据量: 366 条 (原始: 2928 条)
2025-08-03 21:15:27,790 - SmartData - INFO - 📈 提取到相关数据: 366 条记录
2025-08-03 21:15:27,791 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 21:15:27,801 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 21:15:27,802 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 21:15:27,802 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 21:15:46,903 - SmartData - INFO - ⏱️ API响应时间: 19.10 秒
2025-08-03 21:15:46,903 - SmartData - INFO - ✅ API调用成功，返回内容长度: 453 字符
2025-08-03 21:15:46,904 - SmartData - INFO - 📋 分析报告生成完成，长度: 453 字符
2025-08-03 21:15:46,904 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 21:15:46,906 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 21:15:46,906 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 21:15:46,907 - SmartData - INFO - 📊 数据概况: 366 条记录, 1 种蔬菜
2025-08-03 21:15:46,907 - SmartData - INFO - 🍩 检测到分布分析需求，生成分布图表...
2025-08-03 21:15:46,910 - SmartData - INFO - ✅ 分布图表生成成功
2025-08-03 21:15:46,910 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 21:15:46,911 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 21:15:46,911 - SmartData - INFO - ⏱️ 分析完成，总耗时: 19.13 秒
2025-08-03 21:15:46,912 - app - INFO - ⏱️ 智能问数服务耗时: 19.13 秒
2025-08-03 21:15:46,917 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 21:15:46,917 - app - INFO - 📊 生成分析报告，长度: 453 字符，图表数量: 1
2025-08-03 21:15:46,918 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 21:15:46,919 - app - INFO - ✅ 输出安全校验通过
2025-08-03 21:15:46,919 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 21:15:46,920 - app - INFO - ⏱️ 请求处理完成，总耗时: 19.14 秒
2025-08-03 21:15:46,920 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 21:15:46,923 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:15:46] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 21:24:50,358 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:24:50,373 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:24:50,374 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:24:50,690 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:24:50,690 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:24:50,692 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 21:24:50,744 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 21:24:50,745 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 21:24:50,746 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:24:51,800 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:24:51,817 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:24:51,817 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:24:52,133 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:24:52,133 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:24:52,135 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 21:24:52,177 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:24:52,180 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:25:09,083 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:25:09] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 21:25:09,087 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 21:25:09,087 - app - INFO - ❓ 用户问题: 10月份小白菜价格变动情况
2025-08-03 21:25:09,089 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 21:25:09,090 - app - INFO - ✅ 输入安全校验通过
2025-08-03 21:25:09,091 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 21:25:09,091 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 21:25:09,091 - SmartData - INFO - ❓ 用户问题: 10月份小白菜价格变动情况
2025-08-03 21:25:09,092 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 21:25:09,092 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 21:25:09,092 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 21:25:09,092 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 21:25:09,093 - SmartData - INFO - 🥬 识别到特定蔬菜品种: ['白菜']
2025-08-03 21:25:09,095 - SmartData - INFO - 📊 按品种筛选后数据量: 366 条 (原始: 2928 条)
2025-08-03 21:25:09,096 - SmartData - INFO - 📈 提取到相关数据: 366 条记录
2025-08-03 21:25:09,096 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 21:25:09,108 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 21:25:09,108 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 21:25:09,108 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 21:25:27,298 - SmartData - INFO - ⏱️ API响应时间: 18.19 秒
2025-08-03 21:25:27,298 - SmartData - INFO - ✅ API调用成功，返回内容长度: 454 字符
2025-08-03 21:25:27,299 - SmartData - INFO - 📋 分析报告生成完成，长度: 454 字符
2025-08-03 21:25:27,299 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 21:25:27,301 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 21:25:27,301 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 21:25:27,302 - SmartData - INFO - 📊 数据概况: 366 条记录, 1 种蔬菜
2025-08-03 21:25:27,302 - SmartData - INFO - 🔄 未匹配到特定图表类型，生成默认图表...
2025-08-03 21:25:27,302 - SmartData - INFO - 📈 单品种数据，生成趋势图表
2025-08-03 21:25:27,316 - SmartData - INFO - ✅ 默认趋势图表生成成功
2025-08-03 21:25:27,316 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 21:25:27,316 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 21:25:27,317 - SmartData - INFO - ⏱️ 分析完成，总耗时: 18.23 秒
2025-08-03 21:25:27,317 - app - INFO - ⏱️ 智能问数服务耗时: 18.23 秒
2025-08-03 21:25:27,318 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 21:25:27,318 - app - INFO - 📊 生成分析报告，长度: 454 字符，图表数量: 1
2025-08-03 21:25:27,318 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 21:25:27,319 - app - INFO - ✅ 输出安全校验通过
2025-08-03 21:25:27,319 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 21:25:27,320 - app - INFO - ⏱️ 请求处理完成，总耗时: 18.23 秒
2025-08-03 21:25:27,322 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 21:25:27,323 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:25:27] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 21:34:25,324 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:34:25,325 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:25,716 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:34:25,717 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:34:25,718 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 21:34:25,744 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:34:25,744 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:26,000 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:34:26,001 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:34:26,001 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 21:34:47,139 - werkzeug - INFO -  * Detected change in 'd:\\Projects\\zhengwudemobase0729\\Backend\\smart_data_service.py', reloading
2025-08-03 21:34:47,273 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:34:48,359 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:34:48,373 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:34:48,373 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:48,658 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:34:48,659 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:34:48,661 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:34:48,661 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:48,661 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:34:48,662 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:34:48,675 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:34:48,677 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:34:55,120 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:34:55,120 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:55,501 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:34:55,501 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:34:55,504 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:34:55,504 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:55,505 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:34:55,505 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:34:55,506 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:34:55,506 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:55,763 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:34:55,763 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:34:55,765 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:34:55,765 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:34:55,766 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:34:55,766 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:35:45,155 - werkzeug - INFO -  * Detected change in 'd:\\Projects\\zhengwudemobase0729\\Backend\\smart_data_service.py', reloading
2025-08-03 21:35:45,302 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:35:46,292 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:35:46,363 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:35:46,363 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:35:46,658 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:35:46,658 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:35:46,660 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:35:46,661 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:35:46,661 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:35:46,662 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:35:46,676 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:35:46,678 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:35:56,761 - werkzeug - INFO -  * Detected change in 'd:\\Projects\\zhengwudemobase0729\\Backend\\smart_data_service.py', reloading
2025-08-03 21:35:56,895 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:35:57,895 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:35:57,908 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:35:57,909 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:35:58,196 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:35:58,196 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:35:58,198 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:35:58,199 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:35:58,199 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:35:58,199 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:35:58,213 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:35:58,215 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:36:05,025 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:36:05,025 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:05,397 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:36:05,398 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:36:05,400 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:36:05,400 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:05,400 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:36:05,401 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:36:05,402 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:36:05,402 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:05,666 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:36:05,666 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:36:05,668 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:36:05,669 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:05,669 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:36:05,669 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:36:05,671 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:36:05,672 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:05,926 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:36:05,927 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:36:05,928 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:36:05,929 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:05,929 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:36:05,929 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:36:05,930 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:36:05,930 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:06,182 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:36:06,182 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:36:06,184 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:36:06,184 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:06,184 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:36:06,185 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:36:06,185 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 20
2025-08-03 21:36:06,186 - SmartData - INFO - 🔍 未识别到特定蔬菜品种，将使用全部数据
2025-08-03 21:36:06,198 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:36:06,198 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:06,462 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:36:06,462 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:36:06,464 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:36:06,464 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:36:06,464 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:36:06,465 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:36:06,466 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 21:36:06,466 - SmartData - INFO - ❓ 用户问题: 最近白菜的价格趋势如何？
2025-08-03 21:36:06,467 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 21:36:06,467 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 21:36:06,467 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 21:36:06,467 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 21:36:06,468 - SmartData - INFO - 🔍 未识别到特定蔬菜品种，将使用全部数据
2025-08-03 21:36:06,469 - SmartData - INFO - 📈 提取到相关数据: 1000 条记录
2025-08-03 21:36:06,469 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 21:36:06,480 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 21:36:06,480 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 21:36:06,480 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 21:36:25,709 - SmartData - INFO - ⏱️ API响应时间: 19.23 秒
2025-08-03 21:36:25,710 - SmartData - INFO - ✅ API调用成功，返回内容长度: 444 字符
2025-08-03 21:36:25,711 - SmartData - INFO - 📋 分析报告生成完成，长度: 444 字符
2025-08-03 21:36:25,711 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 21:36:25,713 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 21:36:25,714 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 21:36:25,714 - SmartData - INFO - 📊 数据概况: 1000 条记录, 50 种蔬菜
2025-08-03 21:36:25,714 - SmartData - INFO - 📈 检测到趋势分析需求，生成趋势图表...
2025-08-03 21:36:25,724 - SmartData - INFO - ✅ 趋势图表生成成功
2025-08-03 21:36:25,724 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 21:36:25,726 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 21:36:25,726 - SmartData - INFO - ⏱️ 分析完成，总耗时: 19.26 秒
2025-08-03 21:38:45,319 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:38:45,339 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:38:45,340 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:38:45,650 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:38:45,651 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:38:45,653 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:38:45,654 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:38:45,654 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:38:45,654 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:38:45,680 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 21:38:45,680 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 21:38:45,681 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:38:46,671 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:38:46,688 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:38:46,689 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:38:47,012 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:38:47,013 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:38:47,014 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:38:47,015 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:38:47,015 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:38:47,015 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:38:47,030 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:38:47,032 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:39:07,637 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:39:07] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 21:39:07,641 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 21:39:07,642 - app - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:39:07,642 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 21:39:07,643 - app - INFO - ✅ 输入安全校验通过
2025-08-03 21:39:07,643 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 21:39:07,644 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 21:39:07,644 - SmartData - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:39:07,644 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 21:39:07,645 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 21:39:07,645 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 21:39:07,645 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 21:39:07,646 - SmartData - INFO - 🥬 识别到特定蔬菜品种: ['小白菜']
2025-08-03 21:39:07,648 - SmartData - INFO - 📊 按品种筛选后数据量: 258 条 (原始: 12900 条)
2025-08-03 21:39:07,648 - SmartData - INFO - 📈 提取到相关数据: 258 条记录
2025-08-03 21:39:07,648 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 21:39:07,659 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 21:39:07,659 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 21:39:07,660 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 21:39:28,391 - SmartData - INFO - ⏱️ API响应时间: 20.73 秒
2025-08-03 21:39:28,392 - SmartData - INFO - ✅ API调用成功，返回内容长度: 482 字符
2025-08-03 21:39:28,393 - SmartData - INFO - 📋 分析报告生成完成，长度: 482 字符
2025-08-03 21:39:28,394 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 21:39:28,397 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 21:39:28,397 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 21:39:28,397 - SmartData - INFO - 📊 数据概况: 258 条记录, 1 种蔬菜
2025-08-03 21:39:28,397 - SmartData - INFO - 🍩 检测到分布分析需求，生成分布图表...
2025-08-03 21:39:28,400 - SmartData - INFO - ✅ 分布图表生成成功
2025-08-03 21:39:28,400 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 21:39:28,401 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 21:39:28,401 - SmartData - INFO - ⏱️ 分析完成，总耗时: 20.76 秒
2025-08-03 21:39:28,401 - app - INFO - ⏱️ 智能问数服务耗时: 20.76 秒
2025-08-03 21:39:28,402 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 21:39:28,402 - app - INFO - 📊 生成分析报告，长度: 482 字符，图表数量: 1
2025-08-03 21:39:28,402 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 21:39:28,403 - app - INFO - ✅ 输出安全校验通过
2025-08-03 21:39:28,403 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 21:39:28,404 - app - INFO - ⏱️ 请求处理完成，总耗时: 20.76 秒
2025-08-03 21:39:28,404 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 21:39:28,405 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:39:28] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 21:50:01,990 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:50:02,004 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:50:02,004 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:50:02,330 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:50:02,331 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:50:02,332 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:50:02,333 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:50:02,333 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:50:02,333 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:50:02,352 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 21:50:02,353 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 21:50:02,354 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:50:03,433 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:50:03,446 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:50:03,446 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:50:03,738 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:50:03,738 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:50:03,740 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:50:03,741 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:50:03,741 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:50:03,742 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:50:03,755 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:50:03,758 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:50:09,106 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:50:09] "OPTIONS /ask HTTP/1.1" 200 -
2025-08-03 21:50:09,114 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:50:09] "POST /ask HTTP/1.1" 200 -
2025-08-03 21:50:18,349 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:50:18] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 21:50:18,354 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 21:50:18,355 - app - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:50:18,356 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 21:50:18,356 - app - INFO - ✅ 输入安全校验通过
2025-08-03 21:50:18,357 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 21:50:18,357 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 21:50:18,358 - SmartData - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:50:18,358 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 21:50:18,358 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 21:50:18,358 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 21:50:18,358 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 21:50:18,359 - SmartData - INFO - 🥬 识别到特定蔬菜品种: ['小白菜']
2025-08-03 21:50:18,361 - SmartData - INFO - 📊 按品种筛选后数据量: 258 条 (原始: 12900 条)
2025-08-03 21:50:18,362 - SmartData - INFO - 📈 提取到相关数据: 258 条记录
2025-08-03 21:50:18,362 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 21:50:18,374 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 21:50:18,374 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 21:50:18,374 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 21:50:35,472 - SmartData - INFO - ⏱️ API响应时间: 17.10 秒
2025-08-03 21:50:35,472 - SmartData - INFO - ✅ API调用成功，返回内容长度: 408 字符
2025-08-03 21:50:35,474 - SmartData - INFO - 📋 分析报告生成完成，长度: 408 字符
2025-08-03 21:50:35,475 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 21:50:35,479 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 21:50:35,479 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 21:50:35,479 - SmartData - INFO - 📊 数据概况: 258 条记录, 1 种蔬菜
2025-08-03 21:50:35,480 - SmartData - INFO - 🍩 检测到分布分析需求，生成分布图表...
2025-08-03 21:50:35,483 - SmartData - INFO - ✅ 分布图表生成成功
2025-08-03 21:50:35,484 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 21:50:35,484 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 21:50:35,484 - SmartData - INFO - ⏱️ 分析完成，总耗时: 17.13 秒
2025-08-03 21:50:35,484 - app - INFO - ⏱️ 智能问数服务耗时: 17.13 秒
2025-08-03 21:50:35,485 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 21:50:35,485 - app - INFO - 📊 生成分析报告，长度: 408 字符，图表数量: 1
2025-08-03 21:50:35,486 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 21:50:35,486 - app - INFO - ✅ 输出安全校验通过
2025-08-03 21:50:35,487 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 21:50:35,487 - app - INFO - ⏱️ 请求处理完成，总耗时: 17.13 秒
2025-08-03 21:50:35,487 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 21:50:35,489 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:50:35] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 21:58:46,773 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:58:46,786 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:58:46,787 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:58:47,115 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:58:47,116 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:58:47,118 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:58:47,119 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:58:47,119 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:58:47,119 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:58:47,148 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 21:58:47,148 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 21:58:47,149 - werkzeug - INFO -  * Restarting with stat
2025-08-03 21:58:48,108 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 21:58:48,122 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 21:58:48,122 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 21:58:48,409 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 21:58:48,409 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 21:58:48,411 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 21:58:48,412 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 21:58:48,412 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 21:58:48,412 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 21:58:48,426 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 21:58:48,428 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 21:59:06,354 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:59:06] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 21:59:06,357 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 21:59:06,358 - app - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:59:06,359 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 21:59:06,360 - app - INFO - ✅ 输入安全校验通过
2025-08-03 21:59:06,361 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 21:59:06,361 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 21:59:06,361 - SmartData - INFO - ❓ 用户问题: 10月份小白菜价格波动情况
2025-08-03 21:59:06,361 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 21:59:06,362 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 21:59:06,362 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 21:59:06,362 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 21:59:06,363 - SmartData - INFO - 🥬 识别到特定蔬菜品种: ['小白菜']
2025-08-03 21:59:06,366 - SmartData - INFO - 📊 按品种筛选后数据量: 258 条 (原始: 12900 条)
2025-08-03 21:59:06,367 - SmartData - INFO - 📈 提取到相关数据: 258 条记录
2025-08-03 21:59:06,367 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 21:59:06,379 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 21:59:06,379 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 21:59:06,382 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 21:59:24,567 - SmartData - INFO - ⏱️ API响应时间: 18.19 秒
2025-08-03 21:59:24,568 - SmartData - INFO - ✅ API调用成功，返回内容长度: 431 字符
2025-08-03 21:59:24,568 - SmartData - INFO - 📋 分析报告生成完成，长度: 431 字符
2025-08-03 21:59:24,568 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 21:59:24,570 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 21:59:24,570 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 21:59:24,570 - SmartData - INFO - 📊 数据概况: 258 条记录, 1 种蔬菜
2025-08-03 21:59:24,571 - SmartData - INFO - 🍩 检测到分布分析需求，生成分布图表...
2025-08-03 21:59:24,572 - SmartData - INFO - ✅ 分布图表生成成功
2025-08-03 21:59:24,572 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 21:59:24,572 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 21:59:24,572 - SmartData - INFO - ⏱️ 分析完成，总耗时: 18.21 秒
2025-08-03 21:59:24,573 - app - INFO - ⏱️ 智能问数服务耗时: 18.21 秒
2025-08-03 21:59:24,574 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 21:59:24,575 - app - INFO - 📊 生成分析报告，长度: 431 字符，图表数量: 1
2025-08-03 21:59:24,575 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 21:59:24,575 - app - INFO - ✅ 输出安全校验通过
2025-08-03 21:59:24,576 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 21:59:24,576 - app - INFO - ⏱️ 请求处理完成，总耗时: 18.22 秒
2025-08-03 21:59:24,577 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 21:59:24,577 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 21:59:24] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 22:05:18,958 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:05:18,972 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:05:18,972 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:05:19,352 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:05:19,352 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:05:19,355 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 22:05:19,355 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 22:05:19,355 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 22:05:19,356 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 22:05:19,383 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 22:05:19,383 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 22:05:19,384 - werkzeug - INFO -  * Restarting with stat
2025-08-03 22:05:20,406 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:05:20,421 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:05:20,421 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:05:20,753 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:05:20,753 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:05:20,755 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 22:05:20,756 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 22:05:20,756 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 22:05:20,757 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 22:05:20,778 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 22:05:20,780 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 22:06:28,505 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:06:28] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 22:06:28,508 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 22:06:28,509 - app - INFO - ❓ 用户问题: 蔬菜价格趋势如何？
2025-08-03 22:06:28,510 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 22:06:28,510 - app - INFO - ✅ 输入安全校验通过
2025-08-03 22:06:28,511 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 22:06:28,512 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 22:06:28,512 - SmartData - INFO - ❓ 用户问题: 蔬菜价格趋势如何？
2025-08-03 22:06:28,512 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 22:06:28,513 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 22:06:28,513 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 22:06:28,513 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 22:06:28,514 - SmartData - INFO - 🔍 未识别到特定蔬菜品种，将使用全部数据
2025-08-03 22:06:28,519 - SmartData - INFO - 📈 提取到相关数据: 1000 条记录
2025-08-03 22:06:28,520 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 22:06:28,534 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 22:06:28,539 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 22:06:28,540 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 22:06:51,300 - SmartData - INFO - ⏱️ API响应时间: 22.76 秒
2025-08-03 22:06:51,300 - SmartData - INFO - ✅ API调用成功，返回内容长度: 475 字符
2025-08-03 22:06:51,300 - SmartData - INFO - 📋 分析报告生成完成，长度: 475 字符
2025-08-03 22:06:51,300 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 22:06:51,302 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 22:06:51,302 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 22:06:51,302 - SmartData - INFO - 📊 数据概况: 1000 条记录, 50 种蔬菜
2025-08-03 22:06:51,302 - SmartData - INFO - 📈 检测到趋势分析需求，生成趋势图表...
2025-08-03 22:06:51,307 - SmartData - INFO - ✅ 趋势图表生成成功
2025-08-03 22:06:51,307 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 22:06:51,307 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 22:06:51,308 - SmartData - INFO - ⏱️ 分析完成，总耗时: 22.80 秒
2025-08-03 22:06:51,308 - app - INFO - ⏱️ 智能问数服务耗时: 22.80 秒
2025-08-03 22:06:51,309 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 22:06:51,310 - app - INFO - 📊 生成分析报告，长度: 475 字符，图表数量: 1
2025-08-03 22:06:51,311 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 22:06:51,311 - app - INFO - ✅ 输出安全校验通过
2025-08-03 22:06:51,311 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 22:06:51,311 - app - INFO - ⏱️ 请求处理完成，总耗时: 22.80 秒
2025-08-03 22:06:51,312 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 22:06:51,313 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:06:51] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 22:11:58,268 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:11:58,282 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:11:58,282 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:11:58,576 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:11:58,576 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:11:58,578 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 22:11:58,579 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 22:11:58,579 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 22:11:58,579 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 22:11:58,607 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 22:11:58,607 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 22:11:58,609 - werkzeug - INFO -  * Restarting with stat
2025-08-03 22:11:59,696 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:11:59,711 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:11:59,711 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:12:00,028 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:12:00,029 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:12:00,031 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 22:12:00,031 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 22:12:00,031 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 22:12:00,032 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 22:12:00,048 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 22:12:00,050 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 22:12:10,249 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:12:10] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 22:12:10,256 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 22:12:10,260 - app - INFO - ❓ 用户问题: 蔬菜价格趋势如何？
2025-08-03 22:12:10,261 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 22:12:10,262 - app - INFO - ✅ 输入安全校验通过
2025-08-03 22:12:10,262 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 22:12:10,266 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 22:12:10,266 - SmartData - INFO - ❓ 用户问题: 蔬菜价格趋势如何？
2025-08-03 22:12:10,267 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 22:12:10,267 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 22:12:10,268 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 22:12:10,268 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 22:12:10,269 - SmartData - INFO - 🔍 未识别到特定蔬菜品种，将使用全部数据
2025-08-03 22:12:10,272 - SmartData - INFO - 📈 提取到相关数据: 1000 条记录
2025-08-03 22:12:10,272 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 22:12:10,294 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 22:12:10,294 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 22:12:10,295 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 22:12:32,854 - SmartData - INFO - ⏱️ API响应时间: 22.56 秒
2025-08-03 22:12:32,854 - SmartData - INFO - ✅ API调用成功，返回内容长度: 483 字符
2025-08-03 22:12:32,855 - SmartData - INFO - 📋 分析报告生成完成，长度: 483 字符
2025-08-03 22:12:32,855 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 22:12:32,858 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 22:12:32,859 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 22:12:32,859 - SmartData - INFO - 📊 数据概况: 1000 条记录, 50 种蔬菜
2025-08-03 22:12:32,859 - SmartData - INFO - 📈 检测到趋势分析需求，生成趋势图表...
2025-08-03 22:12:32,869 - SmartData - INFO - ✅ 趋势图表生成成功
2025-08-03 22:12:32,869 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 22:12:32,870 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 22:12:32,870 - SmartData - INFO - ⏱️ 分析完成，总耗时: 22.60 秒
2025-08-03 22:12:32,871 - app - INFO - ⏱️ 智能问数服务耗时: 22.61 秒
2025-08-03 22:12:32,871 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 22:12:32,871 - app - INFO - 📊 生成分析报告，长度: 483 字符，图表数量: 1
2025-08-03 22:12:32,872 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 22:12:32,873 - app - INFO - ✅ 输出安全校验通过
2025-08-03 22:12:32,873 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 22:12:32,874 - app - INFO - ⏱️ 请求处理完成，总耗时: 22.62 秒
2025-08-03 22:12:32,874 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 22:12:32,875 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:12:32] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 22:17:31,773 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:17:31,787 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:17:31,787 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:17:32,092 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:17:32,092 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:17:32,094 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 22:17:32,094 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 22:17:32,094 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 22:17:32,095 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 22:17:32,118 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 22:17:32,118 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 22:17:32,120 - werkzeug - INFO -  * Restarting with stat
2025-08-03 22:17:33,169 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:17:33,184 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:17:33,184 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:17:33,509 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:17:33,509 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:17:33,511 - SmartData - INFO - 📅 日期格式转换成功，日期范围: 2018-01-29 00:00:00 到 2018-12-29 00:00:00
2025-08-03 22:17:33,512 - SmartData - INFO - ✅ 成功加载数据文件: ../DB/veg_price.xlsx
2025-08-03 22:17:33,512 - SmartData - INFO - 📊 数据概览: 12900 条记录, 50 种蔬菜
2025-08-03 22:17:33,513 - SmartData - INFO - 🥬 蔬菜品种: ['小白菜', '大白菜', '白萝卜', '菠菜', '白菜秧', '包菜', '竹叶菜', '苋菜', '生菜', '油麦菜', '薯尖', '香菜', '芹菜', '花菜', '西兰花', '西红柿', '青椒', '茄子', '土豆', '胡萝卜', '莴苣', '芋头', '莲藕', '茭白', '红薯', '山药', '黄瓜', '丝瓜', '苦瓜', '冬瓜', '南瓜', '瓠子', '角瓜', '毛豆', '四季豆', '豆角', '豌豆角', '蚕豆角', '韭菜', '蒜苗', '蒜头', '大葱', '葱头', '生姜', '小葱', '芦笋', '香菇', '平菇', '西洋芹', '玉米棒']
2025-08-03 22:17:33,529 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 22:17:33,531 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 22:18:01,125 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:18:01] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 22:18:01,130 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 22:18:01,133 - app - INFO - ❓ 用户问题: 10月份菠菜价格波动情况
2025-08-03 22:18:01,133 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 22:18:01,134 - app - INFO - ✅ 输入安全校验通过
2025-08-03 22:18:01,135 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 22:18:01,135 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 22:18:01,135 - SmartData - INFO - ❓ 用户问题: 10月份菠菜价格波动情况
2025-08-03 22:18:01,136 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 22:18:01,136 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 22:18:01,136 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 22:18:01,136 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 22:18:01,141 - SmartData - INFO - 🥬 识别到特定蔬菜品种: ['菠菜']
2025-08-03 22:18:01,145 - SmartData - INFO - 📊 按品种筛选后数据量: 258 条 (原始: 12900 条)
2025-08-03 22:18:01,145 - SmartData - INFO - 📈 提取到相关数据: 258 条记录
2025-08-03 22:18:01,145 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 22:18:01,158 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 22:18:01,158 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 22:18:01,159 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 22:18:23,883 - SmartData - INFO - ⏱️ API响应时间: 22.73 秒
2025-08-03 22:18:23,884 - SmartData - INFO - ✅ API调用成功，返回内容长度: 567 字符
2025-08-03 22:18:23,884 - SmartData - INFO - 📋 分析报告生成完成，长度: 567 字符
2025-08-03 22:18:23,884 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 22:18:23,888 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 22:18:23,888 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 22:18:23,889 - SmartData - INFO - 📊 数据概况: 258 条记录, 1 种蔬菜
2025-08-03 22:18:23,889 - SmartData - INFO - 🍩 检测到分布分析需求，生成分布图表...
2025-08-03 22:18:23,893 - SmartData - INFO - ✅ 分布图表生成成功
2025-08-03 22:18:23,894 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 22:18:23,894 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 22:18:23,894 - SmartData - INFO - ⏱️ 分析完成，总耗时: 22.76 秒
2025-08-03 22:18:23,895 - app - INFO - ⏱️ 智能问数服务耗时: 22.76 秒
2025-08-03 22:18:23,895 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 22:18:23,895 - app - INFO - 📊 生成分析报告，长度: 567 字符，图表数量: 1
2025-08-03 22:18:23,896 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 22:18:23,897 - app - WARNING - ⚠️ 输出安全校验失败: 问题内容过长，请简化您的问题
2025-08-03 22:18:23,898 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:18:23] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 22:19:03,112 - werkzeug - INFO -  * Detected change in 'd:\\Projects\\zhengwudemobase0729\\Backend\\smart_data_service.py', reloading
2025-08-03 22:19:03,275 - werkzeug - INFO -  * Restarting with stat
2025-08-03 22:19:04,422 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:19:04,438 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:19:04,439 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:19:04,814 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:19:04,814 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:19:04,816 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 22:19:04,862 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 22:19:04,864 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 22:19:10,699 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:19:10,714 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:19:10,714 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:19:11,022 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:19:11,023 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:19:11,024 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 22:19:11,082 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-03 22:19:11,082 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-03 22:19:11,084 - werkzeug - INFO -  * Restarting with stat
2025-08-03 22:19:12,216 - app - INFO - 🚀 智能政务平台后端服务启动
2025-08-03 22:19:12,231 - SmartData - INFO - 🔄 开始加载蔬菜价格数据...
2025-08-03 22:19:12,231 - SmartData - INFO - 📁 找到数据文件: ../DB/veg_price.xlsx
2025-08-03 22:19:12,542 - SmartData - INFO - 📊 成功读取Excel文件，数据形状: (12900, 4)
2025-08-03 22:19:12,542 - SmartData - INFO - 🏷️ 列名标准化: ['日期', '最高价格', '最低价格', '品类'] -> ['date', 'max_price', 'min_price', 'variety']
2025-08-03 22:19:12,544 - SmartData - ERROR - ⚠️ 日期格式转换失败: time data "2018-12-14" doesn't match format "%Y/%m/%d", at position 253. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.，使用示例数据
2025-08-03 22:19:12,588 - werkzeug - WARNING -  * Debugger is active!
2025-08-03 22:19:12,591 - werkzeug - INFO -  * Debugger PIN: 446-118-540
2025-08-03 22:19:18,667 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:19:18] "OPTIONS /ask HTTP/1.1" 200 -
2025-08-03 22:19:18,674 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:19:18] "POST /ask HTTP/1.1" 200 -
2025-08-03 22:19:29,099 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:19:29] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 22:19:29,108 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 22:19:29,113 - app - INFO - ❓ 用户问题: 10月份菠菜价格波动情况
2025-08-03 22:19:29,114 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 22:19:29,115 - app - INFO - ✅ 输入安全校验通过
2025-08-03 22:19:29,115 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 22:19:29,119 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 22:19:29,119 - SmartData - INFO - ❓ 用户问题: 10月份菠菜价格波动情况
2025-08-03 22:19:29,120 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 22:19:29,120 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 22:19:29,120 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 22:19:29,125 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 22:19:29,130 - SmartData - INFO - 🔍 未识别到特定蔬菜品种，将使用全部数据
2025-08-03 22:19:29,144 - SmartData - INFO - 📈 提取到相关数据: 800 条记录
2025-08-03 22:19:29,145 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 22:19:29,159 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 22:19:29,159 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 22:19:29,160 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
2025-08-03 22:19:43,970 - SmartData - INFO - ⏱️ API响应时间: 14.81 秒
2025-08-03 22:19:43,971 - SmartData - INFO - ✅ API调用成功，返回内容长度: 349 字符
2025-08-03 22:19:43,972 - SmartData - INFO - 📋 分析报告生成完成，长度: 349 字符
2025-08-03 22:19:43,972 - SmartData - INFO - 📊 生成数据摘要...
2025-08-03 22:19:43,973 - SmartData - INFO - 📈 开始生成智能图表...
2025-08-03 22:19:43,973 - SmartData - INFO - 📈 开始智能图表生成...
2025-08-03 22:19:43,974 - SmartData - INFO - 📊 数据概况: 800 条记录, 8 种蔬菜
2025-08-03 22:19:43,974 - SmartData - INFO - 🍩 检测到分布分析需求，生成分布图表...
2025-08-03 22:19:43,977 - SmartData - INFO - ✅ 分布图表生成成功
2025-08-03 22:19:43,977 - SmartData - INFO - 📊 图表生成完成，共生成 1 个图表
2025-08-03 22:19:43,978 - SmartData - INFO - 📊 图表生成完成，共 1 个图表
2025-08-03 22:19:43,978 - SmartData - INFO - ⏱️ 分析完成，总耗时: 14.86 秒
2025-08-03 22:19:43,979 - app - INFO - ⏱️ 智能问数服务耗时: 14.86 秒
2025-08-03 22:19:43,979 - app - INFO - ✅ 智能问数服务调用成功
2025-08-03 22:19:43,980 - app - INFO - 📊 生成分析报告，长度: 349 字符，图表数量: 1
2025-08-03 22:19:43,980 - app - INFO - 🔒 开始输出安全校验...
2025-08-03 22:19:43,981 - app - INFO - ✅ 输出安全校验通过
2025-08-03 22:19:43,981 - app - INFO - 📈 添加图表数据: 1 个图表
2025-08-03 22:19:43,981 - app - INFO - ⏱️ 请求处理完成，总耗时: 14.87 秒
2025-08-03 22:19:43,982 - app - INFO - ✅ 智能问数API请求成功 - 客户端IP: 127.0.0.1
2025-08-03 22:19:43,983 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:19:43] "POST /ask_data HTTP/1.1" 200 -
2025-08-03 22:20:03,443 - werkzeug - INFO - 127.0.0.1 - - [03/Aug/2025 22:20:03] "OPTIONS /ask_data HTTP/1.1" 200 -
2025-08-03 22:20:03,448 - app - INFO - 📊 智能问数API请求开始 - 客户端IP: 127.0.0.1
2025-08-03 22:20:03,449 - app - INFO - ❓ 用户问题: 萝卜
2025-08-03 22:20:03,450 - app - INFO - 🔒 开始输入安全校验...
2025-08-03 22:20:03,451 - app - INFO - ✅ 输入安全校验通过
2025-08-03 22:20:03,452 - app - INFO - 🤖 开始调用智能问数服务...
2025-08-03 22:20:03,458 - SmartData - INFO - 🔍 开始智能问数分析
2025-08-03 22:20:03,461 - SmartData - INFO - ❓ 用户问题: 萝卜
2025-08-03 22:20:03,462 - SmartData - INFO - 🔒 开始安全内容校验...
2025-08-03 22:20:03,465 - SmartData - INFO - ✅ 安全校验通过
2025-08-03 22:20:03,466 - SmartData - INFO - 📊 开始提取相关数据...
2025-08-03 22:20:03,467 - SmartData - INFO - 🔍 开始提取相关数据，最大行数: 1000
2025-08-03 22:20:03,468 - SmartData - INFO - 🥬 识别到特定蔬菜品种: ['萝卜']
2025-08-03 22:20:03,472 - SmartData - INFO - 📊 按品种筛选后数据量: 366 条 (原始: 2928 条)
2025-08-03 22:20:03,472 - SmartData - INFO - 📈 提取到相关数据: 366 条记录
2025-08-03 22:20:03,473 - SmartData - INFO - 🔄 格式化数据为LLM输入...
2025-08-03 22:20:03,491 - SmartData - INFO - 🤖 调用DeepSeek API进行智能分析...
2025-08-03 22:20:03,493 - SmartData - INFO - 🤖 开始调用DeepSeek API...
2025-08-03 22:20:03,493 - SmartData - INFO - 📡 发送API请求到: https://api.deepseek.com/chat/completions
