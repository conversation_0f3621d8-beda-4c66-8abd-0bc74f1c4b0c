#!/usr/bin/env python3
"""
简单的日志功能测试
"""

import os
import logging
from datetime import datetime

def test_basic_logging():
    """测试基本日志功能"""
    print("🧪 测试基本日志功能...")
    
    # 确保日志目录存在
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        print(f"✅ 创建日志目录: {log_dir}")
    
    log_file_path = os.path.join(log_dir, 'test_smart_data.log')
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file_path, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 创建专用日志记录器
    logger = logging.getLogger('SmartDataTest')
    
    # 测试各种日志级别
    logger.info("🔍 开始智能问数分析测试")
    logger.info("❓ 用户问题: 测试问题")
    logger.debug("🔤 问题关键词: 测试")
    logger.warning("⚠️ 这是一个警告信息")
    logger.error("❌ 这是一个错误信息")
    logger.info("✅ 测试完成")
    
    # 检查日志文件是否创建
    if os.path.exists(log_file_path):
        file_size = os.path.getsize(log_file_path)
        print(f"✅ 日志文件创建成功: {log_file_path}")
        print(f"📄 文件大小: {file_size} 字节")
        
        # 读取并显示日志内容
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print("📋 日志内容:")
            print("-" * 50)
            print(content)
            print("-" * 50)
        
        return True
    else:
        print("❌ 日志文件创建失败")
        return False

def test_smart_data_import():
    """测试智能问数模块导入"""
    print("\n🧪 测试智能问数模块导入...")
    
    try:
        # 尝试导入模块
        from smart_data_service import SmartDataService
        print("✅ 智能问数模块导入成功")
        
        # 尝试创建服务实例
        service = SmartDataService()
        print("✅ 智能问数服务实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 智能问数日志功能简单测试")
    print("=" * 50)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试基本日志功能
    basic_test = test_basic_logging()
    
    # 测试模块导入
    import_test = test_smart_data_import()
    
    # 总结
    print("\n" + "=" * 50)
    print("🏁 测试总结:")
    print(f"  基本日志功能: {'✅ 通过' if basic_test else '❌ 失败'}")
    print(f"  模块导入测试: {'✅ 通过' if import_test else '❌ 失败'}")
    
    if basic_test and import_test:
        print("🎉 所有测试通过！日志功能正常工作")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
