#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的服务器启动脚本
"""

import sys
import os

# 添加Backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'Backend')
sys.path.insert(0, backend_dir)

# 切换到Backend目录
os.chdir(backend_dir)

try:
    # 导入并运行app
    from app import app
    print("=" * 50)
    print("Smart Government Platform Backend Service Starting")
    print("Service URL: http://127.0.0.1:5000")
    print("=" * 50)
    app.run(debug=True, host='127.0.0.1', port=5000)
except Exception as e:
    print(f"Error starting server: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
