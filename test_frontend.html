<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>前端功能测试</h1>
    
    <div class="test-section">
        <h2>测试智能问数模式</h2>
        <button onclick="testSmartDataMode()">激活智能问数模式</button>
        <button onclick="testSmartDataAPI()">测试智能问数API</button>
        <div id="smartDataResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试智能问策模式</h2>
        <button onclick="testSmartPolicyMode()">激活智能问策模式</button>
        <button onclick="testSmartPolicyAPI()">测试智能问策API</button>
        <div id="smartPolicyResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试市情概况</h2>
        <button onclick="testCityInfoAPI()">测试市情概况API</button>
        <div id="cityInfoResult" class="result"></div>
    </div>

    <script>
        // 模拟前端状态变量
        let smartDataMode = false;
        let smartPolicyMode = false;
        
        function testSmartDataMode() {
            smartDataMode = true;
            smartPolicyMode = false;
            document.body.classList.add('smart-data-mode');
            document.body.classList.remove('smart-policy-mode');
            
            document.getElementById('smartDataResult').innerHTML = `
                <div class="success">
                    ✅ 智能问数模式已激活<br>
                    smartDataMode: ${smartDataMode}<br>
                    smartPolicyMode: ${smartPolicyMode}<br>
                    body classes: ${document.body.className}
                </div>
            `;
        }
        
        function testSmartPolicyMode() {
            smartPolicyMode = true;
            smartDataMode = false;
            document.body.classList.add('smart-policy-mode');
            document.body.classList.remove('smart-data-mode');
            
            document.getElementById('smartPolicyResult').innerHTML = `
                <div class="success">
                    ✅ 智能问策模式已激活<br>
                    smartPolicyMode: ${smartPolicyMode}<br>
                    smartDataMode: ${smartDataMode}<br>
                    body classes: ${document.body.className}
                </div>
            `;
        }
        
        async function testSmartDataAPI() {
            try {
                const response = await fetch('http://127.0.0.1:5000/ask_data', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: '蔬菜价格怎么样' })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                document.getElementById('smartDataResult').innerHTML = `
                    <div class="success">
                        ✅ 智能问数API调用成功<br>
                        状态码: ${response.status}<br>
                        响应数据: <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('smartDataResult').innerHTML = `
                    <div class="error">
                        ❌ 智能问数API调用失败<br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testSmartPolicyAPI() {
            try {
                const response = await fetch('http://127.0.0.1:5000/ask_policy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: '政策问题测试' })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                document.getElementById('smartPolicyResult').innerHTML = `
                    <div class="success">
                        ✅ 智能问策API调用成功<br>
                        状态码: ${response.status}<br>
                        响应数据: <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('smartPolicyResult').innerHTML = `
                    <div class="error">
                        ❌ 智能问策API调用失败<br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testCityInfoAPI() {
            try {
                const response = await fetch('http://127.0.0.1:5000/city_info/introduction', {
                    method: 'GET'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                document.getElementById('cityInfoResult').innerHTML = `
                    <div class="success">
                        ✅ 市情概况API调用成功<br>
                        状态码: ${response.status}<br>
                        响应数据: <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('cityInfoResult').innerHTML = `
                    <div class="error">
                        ❌ 市情概况API调用失败<br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
