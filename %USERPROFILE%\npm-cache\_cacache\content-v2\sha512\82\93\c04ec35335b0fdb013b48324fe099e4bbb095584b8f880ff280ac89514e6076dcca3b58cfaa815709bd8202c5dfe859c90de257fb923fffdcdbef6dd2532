{"_id": "he", "_rev": "103-08018dcbe2acd2669d57503c9b56af5b", "name": "he", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "dist-tags": {"latest": "1.2.0"}, "versions": {"0.1.0": {"name": "he", "version": "0.1.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.2.2", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.1", "requirejs": "~2.1.6", "lodash": "~1.3.1", "string-escape": "~0.1.6", "grunt-curl": "~1.1.1"}, "_id": "he@0.1.0", "dist": {"shasum": "ff047a4c4a91ee6be2c1156f177a46e269a35908", "tarball": "https://registry.npmjs.org/he/-/he-0.1.0.tgz", "integrity": "sha512-QO9z3/LsiBvOxKkY04Ji2vkUxO+OnFN3PlHoGgdZ6cjmFQnUXA6Dwz0Gqvd6prVLqvWO0JTpRHSsUOeyRXbhVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoLRsQMqr8eO8HGhUACXnItMkizLyrL03wRCgV2cgC2wIhAMIAdMmWkxFRFT9nyb+O3mfCDn4hSGwKKk9NOI46FuiM"}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.1": {"name": "he", "version": "0.1.1", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.2.2", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.1", "requirejs": "~2.1.6", "lodash": "~1.3.1", "string-escape": "~0.1.6", "grunt-curl": "~1.1.1"}, "_id": "he@0.1.1", "dist": {"shasum": "0938123417e6ada2be2f2b1bb4e3c94313bd1b62", "tarball": "https://registry.npmjs.org/he/-/he-0.1.1.tgz", "integrity": "sha512-0tl5+djrbtBgw4YU/Zm3O1fq2XistRgrN0mHFUJwU8PIjht9tGgAndx/QQRY7ZDo7OHX9sqLz18vpvUWydnIkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZyCsWqJuJPkL9XLSVvnCGbHeIMxZUKKyrAJ705wL3VAIgby5iKmLBnFD5kW/Da/AVG0IPQK6/eYKAvEL1kZkHCB8="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.2": {"name": "he", "version": "0.1.2", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-curl": "~1.1.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.2", "dist": {"shasum": "031f56851e950bddb110dd958f8592b41acc5ba6", "tarball": "https://registry.npmjs.org/he/-/he-0.1.2.tgz", "integrity": "sha512-LCorvfowaDPjKXcuHyLhju7oxOSGWNnUUmVcx7l8HoWXvYHAC2SKlmSlFiUDlCxKuTF6iyCfhw178TFiL/jR8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIANmBGWYxxcqBUcbv8SD8qc0lxdbMfnpxpW4XUoE0ei4AiBMT/8zIFl/ffnyjLUV9TE5OpymAKP2+XCvM/RQILerWQ=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.3": {"name": "he", "version": "0.1.3", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-curl": "~1.1.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.3", "dist": {"shasum": "879aeb954f0846116e58d5694366d95dab6df495", "tarball": "https://registry.npmjs.org/he/-/he-0.1.3.tgz", "integrity": "sha512-x64go3uJA6sti7LV39GgIcluVrHBvWiwr8vorIwOxYpzNwqL2OhIidofJBKYPOZXoD6aNaXP1L9KMkwhB/XfyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqKjaaAYKDk7n7ew0Q3R4dDFqfnoKsaFRGzg/tcVMguAiEA/DyOrNc8M4JhRPgpD8dfIx4sHqBoTkpnD+ycmKwgbBA="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.4": {"name": "he", "version": "0.1.4", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-curl": "~1.1.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.4", "dist": {"shasum": "b772cb498842d6f6918b0e56079a6df27d966703", "tarball": "https://registry.npmjs.org/he/-/he-0.1.4.tgz", "integrity": "sha512-5ofbaxInK5DtOvG0R9FQTEuYDVBudiULdezzRE+1YtQpyttmfFwii0VFaeUGJDiKif6jPLAhB4oQUUPVxvDX8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+XEPDKy1OprO+gqjo3IpxsMLd/xNBKSlbiWYyX1hRtwIgJr/bbXcdC6RUoIZoQLg+MGMou4Si+bF8rAxFinhAAaY="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.5": {"name": "he", "version": "0.1.5", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-curl": "~1.1.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.5", "dist": {"shasum": "3b0e16129145e644589e62eec823ff6818fc4645", "tarball": "https://registry.npmjs.org/he/-/he-0.1.5.tgz", "integrity": "sha512-YuKl1fGB89Kz3LqBks+t5X4VlKvrASXMT8/sXIVU2Cze/tEu6RlBLSUQjgPXvwxS/X9hbqDpxuhv3dbBxk6qGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyL9UlBD9fIGF5MFjEiNEnrZ3+xYNIU0XYbT+cO/4ucgIgQuIftCkAJIJQsQEhATk4FWdpHKAKKX9jd0JdOs/YAZQ="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.6": {"name": "he", "version": "0.1.6", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-curl": "~1.1.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.6", "dist": {"shasum": "69b020bd6ab6196b42562b1c2192887ece2815f9", "tarball": "https://registry.npmjs.org/he/-/he-0.1.6.tgz", "integrity": "sha512-tt+1Lc26fxO+u1dQC/w6jHIwsuwviNxcwCCI74UUzgpGwaYy2pCx4ttQKjwOIPSB/+A70eqxyW4IrvrSGSt6mA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSTjuvgxgGjQmkoFof8WR46wd/xn1qC5n6S2X9UhRJWQIhAJKYuH021S81Y88RIkAN+nOWH8mKzxj/Ymk10ULVRzL1"}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.7": {"name": "he", "version": "0.1.7", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-curl": "~1.1.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.7", "dist": {"shasum": "c5e17c7c61a8a668b249279a9ffe30ca14f3bf64", "tarball": "https://registry.npmjs.org/he/-/he-0.1.7.tgz", "integrity": "sha512-gZDqPRGZp/MabWo832qO0kWZVCuIXs3GaU9fqbSFAlhilZtyPoMphUHWSHevdBxibK/iHcQAH5FHEEnu00kkBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFewE3OmMZg7v0ExqYImO4VvvB2qHwIOuru+FHxXoZvJAiBMExuLAA9tN6Ppl/Mz55XxRwGcQ8c2km0I+T39T0tPqw=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.8": {"name": "he", "version": "0.1.8", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-curl": "~1.1.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.8", "dist": {"shasum": "4350e7c9506269928f9d84c21a2d49f1ab49d797", "tarball": "https://registry.npmjs.org/he/-/he-0.1.8.tgz", "integrity": "sha512-Z0e9FEHN2cEULNXAqZCNX+cLIW/pAliisLmgvCOcxGLcBHfVzvTRh62HOQljjzd2GqCjS4BIuobtVgQvqKXM6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBGe9WofZgZpi42TIqWm5mrT29BYf8Zlh0GN0s/hGYe7AiBYTq2pmNJCPRkdyH8Y5XJ7qI/NV6nBMIx4Qkjx4ADIEQ=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.9": {"name": "he", "version": "0.1.9", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.9", "dist": {"shasum": "0354548643682c12752c63bc39e7bbbf9df096bf", "tarball": "https://registry.npmjs.org/he/-/he-0.1.9.tgz", "integrity": "sha512-SBMdmNO2vx9+gFPSBCDTt4H6lDdNEEeg6mKphRj6b8yc6g1E40v6UsIdyzs8o3iHbtv6N6NFFJfp5yZQfxi/eA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBokflLNqOaHmUGIuJH+uvF4k7lW1yc6CKTKEToE9SVJAiAdc/O7SRqdyB/24d0oQI74jcX7dLTNMIFL1WnI5R10jQ=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.1.10": {"name": "he", "version": "0.1.10", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.1.10", "dist": {"shasum": "183ecc8f277a2a577f5b2b0c2a08eb3fa908cb1f", "tarball": "https://registry.npmjs.org/he/-/he-0.1.10.tgz", "integrity": "sha512-Rxhekhh5mDW37xKiQXbXPMuUG8NreCn6u3BqCMfLpnXGDyCd50GIuU4fXIZWaQOCS4AkH5APkkzrsdRgnn/dvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICp3be82djKfUfHZjEgAdrR9rmn2bPdQyCzQxUqFWpidAiA2EEfXKQXhNEOydjmxAUDEQgLPT75MeGyK+XHgZRQvdQ=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.2.0": {"name": "he", "version": "0.2.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.1.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.1.6"}, "_id": "he@0.2.0", "dist": {"shasum": "fe1509700c35d4115b715ed80ebd2f5fbbf7d90b", "tarball": "https://registry.npmjs.org/he/-/he-0.2.0.tgz", "integrity": "sha512-2nWQIdQRU2dFzZaSRamQImIDk2NDt2W+fJy644SH1reK7WJl5GFKYoPmNX3AZOjId7B01wGRJYO2QQhCMN8K+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDG76rWJ7ZvCXvUvLkB/Cf7vFpsmFOBTD8q2wNthjUWwAIhAJSqvhBimvIfkJ0tb5IQkMgipagx/mAjtJ9sbn4i3ObI"}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.2.1": {"name": "he", "version": "0.2.1", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.2.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.2.2"}, "_id": "he@0.2.1", "dist": {"shasum": "6e97081ab21e8ba6df01d9070b648d6fe7a5da6f", "tarball": "https://registry.npmjs.org/he/-/he-0.2.1.tgz", "integrity": "sha512-8hKGt6oPq+Nv502kSmk6LqXXcGkRcr4DPNvVgOLdfYn4p6eEXj0dzmOhmlgcnHzc8WpJI229XTZ/igT42mFGwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6eKKn6vtRVWzD8DUteykg1Y2NmxubjSGvd+RDqjCHawIhAPTB7TvdSHfwmd3HivuU7+cvRQSRdXIshD8JxoaNq/jx"}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.0": {"name": "he", "version": "0.3.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.2.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.2.2"}, "_id": "he@0.3.0", "dist": {"shasum": "acd9b93774b6c85af4da8aa49f304ef462005aff", "tarball": "https://registry.npmjs.org/he/-/he-0.3.0.tgz", "integrity": "sha512-RdWJbHKHaDVm6fY/xGs4nLEM6MQ1SMsQsPBFxPwA+gfYPF6qPmR+GLhW6QDSoujbPYTf8/7nthIrstdcSubwQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBjpU+nxBhek1AYQF0YZ5E9SIw0IoClwQdVwVBLwLWxqAiEA6IUmNRaVSVJtPAiOiYyS9oLMCnNGwmTRTZaEIvBEQ6k="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.1": {"name": "he", "version": "0.3.1", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.0", "grunt-template": "~0.2.0", "istanbul": "~0.1.37", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.6", "string-escape": "~0.2.2"}, "_id": "he@0.3.1", "dist": {"shasum": "2a558a6dd2d06163351ba535868051a32749751e", "tarball": "https://registry.npmjs.org/he/-/he-0.3.1.tgz", "integrity": "sha512-9LzJaVahUSokivRoFkC8Bhy/z9ZFI/wQ8oSy6pXQNwllV86G7KgVDinYFoA1I/ZwWtd9jU3U2whlxGzl14oJSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAnRpwTsj71IRkTiB/+fg0+GNdls8yueTAEXVNo5gcEdAiAIZW6MMEFrP2MgsiZyHcfzgf/M7K8aQdLKCLz0tSrIcg=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.2": {"name": "he", "version": "0.3.2", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "man": ["man/he.1"], "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.1", "grunt-template": "~0.2.0", "istanbul": "~0.1.42", "jsesc": "~0.3.0", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.8"}, "_id": "he@0.3.2", "dist": {"shasum": "928e2df76f564a60d51b36825a11c47d5a9e74e2", "tarball": "https://registry.npmjs.org/he/-/he-0.3.2.tgz", "integrity": "sha512-PWoKTYZ8Xs9rPwmHhHJkvBWJ7sfF1xU6YHohzmmgR8VloqSNQ2Y/fQ/RKdFj/QT/JDxVcgd1mBT6tlBulFPlRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfLTxIIdaZnhy1BmWXxpWBjs46VFRW07U99ozrw/REQAiEAspuXZjJYdIUFETiiADWv63RS2XKYuqxPuf+IR6D450Y="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.3": {"name": "he", "version": "0.3.3", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "man": ["man/he.1"], "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.1", "grunt-template": "~0.2.0", "istanbul": "~0.1.42", "jsesc": "~0.3.0", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.8"}, "_id": "he@0.3.3", "dist": {"shasum": "0aef2ba87ec9ce434c0381f4767f1a3ebe154a45", "tarball": "https://registry.npmjs.org/he/-/he-0.3.3.tgz", "integrity": "sha512-J6t+t4MARj0mVJzJK5f9F2AR+mNbaEYJisFIrf8/oY2tNAjaClZoumEKJ4UpwzJtKmpGfYWnorTl2OWUrGucqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVoItTQ3lNhcpcg4HDkMVA4QTjUKQlaPjX82vnpN1rDAiEAv0KqT/JRlt49hA8FkI9BtvIvOvG/txnh+ZpPByPTnwU="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.4": {"name": "he", "version": "0.3.4", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "man": ["man/he.1"], "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.1", "grunt-template": "~0.2.0", "istanbul": "~0.1.42", "jsesc": "~0.3.0", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.8"}, "_id": "he@0.3.4", "dist": {"shasum": "b9d808667263d003fd00aac92ab57b869957c9d8", "tarball": "https://registry.npmjs.org/he/-/he-0.3.4.tgz", "integrity": "sha512-nLyOOoxxoYl4j3igIgrt7IuMnFXSUPTMaewDImozd2LjqtUvtc0yfJZ3Acl91MTCS+Ek06NrxC8bzGi+wjOVjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAYoV0FEzqwkJTWOd0gG/GJGUo9nOZzzbh5Ab3oKdb1VAiEAyCB4Pa91GQSjcuvrAIPh5Kp9xqPqLxBuKKE1lgsUnrg="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.5": {"name": "he", "version": "0.3.5", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "man": ["man/he.1"], "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.1", "grunt-template": "~0.2.0", "istanbul": "~0.1.42", "jsesc": "~0.3.0", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.8"}, "_id": "he@0.3.5", "dist": {"shasum": "1c2025f18a3bbfcc5f4e0e5db85658b34d2dd619", "tarball": "https://registry.npmjs.org/he/-/he-0.3.5.tgz", "integrity": "sha512-9YzM3kZvJLYmPaYaIrTo4SQ+67yqLeARfeRDscAaFapXYZkweTiBkNm7AzHJdezCDf49y4x9gNCyEl/yZMod4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvjYIuUhHRULlwAkcCbB4Cc1XZAGlM4m4tXQigY0cyCgIger+EOEMsMGbf/W5V4OVUbSRbEAyneg3Oxmh6hk71N5s="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.3.6": {"name": "he", "version": "0.3.6", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "man": ["man/he.1"], "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-shell": "~0.3.1", "grunt-template": "~0.2.0", "istanbul": "~0.1.43", "jsesc": "~0.4.1", "lodash": "~1.3.1", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "regenerate": "~0.5.2", "requirejs": "~2.1.8"}, "_id": "he@0.3.6", "dist": {"shasum": "9d7bc446e77963933301dd602d5731cb861135e0", "tarball": "https://registry.npmjs.org/he/-/he-0.3.6.tgz", "integrity": "sha512-C5Uq4khZqYh/Nq8YubQGYlsm85bkGeNfUn3BvnRCMuuZXXqM32jyEHBFkTE0NB70mteKI9PsYBXWfIacbluyxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrSb7FHcG0jJfx+nJabuh6Z3eeJtwht8V6dkMC8e8COQIgDab7J3b9CQsvkPrCmcSBm28vrdmfUPcylPzjedbMIJo="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.4.0": {"name": "he", "version": "0.4.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.5", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.10", "jsesc": "~0.4.3", "lodash": "~2.4.1", "lodash.difference": "~2.4.1", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "regenerate": "~0.6.1", "requirejs": "~2.1.11"}, "man": ["/Users/<USER>/.npm/he/0.4.0/package/man/he.1"], "_id": "he@0.4.0", "_shasum": "4e3746137f80e084e0b26ea5e14ebb453f937b1a", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "4e3746137f80e084e0b26ea5e14ebb453f937b1a", "tarball": "https://registry.npmjs.org/he/-/he-0.4.0.tgz", "integrity": "sha512-Zx3CUhsW5ALrXBIhXkfFe1b9K5QUBsy6mtB7Q8+sP0tjJhoYP8qnuAqtprVFdU0JNfYXrtUiRrpgqrQ0pgBp0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhc76K15vkcrq5Mbtg65DDrmhavGHJ70Drmqi4Nn6k0AiBkM8/iLASrsgzC3PgWYegE0MsltUTv2LW2K11OXrLWQA=="}]}}, "0.4.1": {"name": "he", "version": "0.4.1", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.5", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.10", "jsesc": "~0.4.3", "lodash": "~2.4.1", "lodash.difference": "~2.4.1", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "regenerate": "~0.6.2", "requirejs": "~2.1.11", "string.fromcodepoint": "~0.2.0"}, "man": ["/Users/<USER>/projects/he/man/he.1"], "gitHead": "004317cefeb8bf6c9859f0a465a62a6964ec491e", "_id": "he@0.4.1", "_shasum": "c86667614d2dd71bc737a197c760fb2eec8a1921", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "c86667614d2dd71bc737a197c760fb2eec8a1921", "tarball": "https://registry.npmjs.org/he/-/he-0.4.1.tgz", "integrity": "sha512-wFn1JlcsgbcWEawQ6A1GztgNRRl6UyEtaARIDpAeH3c35T/9id/QQKvap1NAz6Yk2oKrKwXQZZFERS0tl9E0Jg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlizGy5Fc+ozWUcaH9NSE15HwLjP1l7OlrwU4rqFfuiwIhAJVeSr9G+vaj3eyoT4LqNb5M+gkSEyIjfxBEuawgwenr"}]}}, "0.5.0": {"name": "he", "version": "0.5.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "http://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.1", "grunt": "^0.4.5", "grunt-shell": "^1.0.1", "grunt-template": "^0.2.3", "istanbul": "^0.3.0", "jsesc": "^0.5.0", "lodash": "^2.4.1", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "regenerate": "^0.6.2", "requirejs": "^2.1.14", "string.fromcodepoint": "^0.2.1"}, "man": ["/Users/<USER>/.npm/he/0.5.0/package/man/he.1"], "_id": "he@0.5.0", "_shasum": "2c05ffaef90b68e860f3fd2b54ef580989277ee2", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "2c05ffaef90b68e860f3fd2b54ef580989277ee2", "tarball": "https://registry.npmjs.org/he/-/he-0.5.0.tgz", "integrity": "sha512-DoufbNNOFzwRPy8uecq+j+VCPQ+JyDelHTmSgygrA5TsR8Cbw4Qcir5sGtWiusB4BdT89nmlaVDhSJOqC/33vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqJlkA8AZbl3fzB1fffmwyaCzhRr0nDZdn+osoEFLAiwIhAPVrZ+wJy8wy4SPHC2+9HSKTv672z20GjRc2FQn2A7P9"}]}}, "1.0.0": {"name": "he", "version": "1.0.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "https://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js", "build": "grunt build"}, "devDependencies": {"codecov.io": "^0.1.6", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "jsesc": "^1.0.0", "lodash": "^4.8.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "requirejs": "^2.1.22", "sort-object": "^3.0.2"}, "man": ["/Users/<USER>/projects/he/man/he.1"], "gitHead": "7630453730c820f31b8ee494582aa1004c7e9680", "_id": "he@1.0.0", "_shasum": "6da5b265d7f2c3b5e480749168e0e159d05728da", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "6da5b265d7f2c3b5e480749168e0e159d05728da", "tarball": "https://registry.npmjs.org/he/-/he-1.0.0.tgz", "integrity": "sha512-qIpX8c5fJnoHJWgowkrU/5mEUBSbYrFM5ctaAMYKI3pZRwOcchfmbVKe7XNAtyduZ2cmEPwJ9ibdQPmnNVFfDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAnY6N9kgTasqUXsvP7sb2yJi6pl0MjXqZQ0MnlN6E6fAiEAqg0rRn/So5MZ3waZaYFY0ew09ACqLu+uNAkldqKjKQA="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/he-1.0.0.tgz_1459885864215_0.46923283371143043"}}, "1.1.0": {"name": "he", "version": "1.1.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "https://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js", "build": "grunt build"}, "devDependencies": {"codecov.io": "^0.1.6", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "jsesc": "^1.0.0", "lodash": "^4.8.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "requirejs": "^2.1.22", "sort-object": "^3.0.2"}, "man": ["/Users/<USER>/projects/he/man/he.1"], "gitHead": "d2c219f6194f07de48543d4af8d5daf514f2ac0c", "_id": "he@1.1.0", "_shasum": "29319d49beec13a9b1f3c4f9b2a6dde4859bb2a7", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "29319d49beec13a9b1f3c4f9b2a6dde4859bb2a7", "tarball": "https://registry.npmjs.org/he/-/he-1.1.0.tgz", "integrity": "sha512-qbiXpa906Tr8hXFtwm6VPsTXFDdbtOEAOrVs/5uJTW5gCAoHeV89RwiQydSXjICso8E2Z7F/PltIjhw1MQMxRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAR8VYkgciqG9nQkwrio5vMnieYnFShrlzxePfikCb8/AiBWgq6o5V28ttuTQTH7lOPPmBSttJWVqVQuTe2chb7KbA=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/he-1.1.0.tgz_1462530942925_0.6209375066682696"}}, "1.1.1": {"name": "he", "version": "1.1.1", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "https://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js", "build": "grunt build"}, "devDependencies": {"codecov.io": "^0.1.6", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "jsesc": "^1.0.0", "lodash": "^4.8.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "requirejs": "^2.1.22", "sort-object": "^3.0.2"}, "man": ["/private/tmp/he/man/he.1"], "gitHead": "670991a4141d01dc015de5194d400d01c863208f", "_id": "he@1.1.1", "_shasum": "93410fd21b009735151f8868c2f271f3427e23fd", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "93410fd21b009735151f8868c2f271f3427e23fd", "tarball": "https://registry.npmjs.org/he/-/he-1.1.1.tgz", "integrity": "sha512-z/GDPjlRMNOa2XJiB4em8wJpuuBfrFOlYKTZxtpkdr1uPdibHI8rYA3MY0KDObpVyaes0e/aunid/t88ZI2EKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnDqHEBuN4fT+ZhUXhEBIJSj5Btdc6m1nF7Zn+jgJM3gIhAIflqIe0b/Mdqw1BAkUaelDDn6jKNmPKW6lc7cyJ9QIT"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/he-1.1.1.tgz_1485179741959_0.3859390316065401"}}, "1.2.0": {"name": "he", "version": "1.2.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "https://mths.be/he", "main": "he.js", "bin": {"he": "bin/he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js", "build": "grunt build"}, "devDependencies": {"codecov.io": "^0.1.6", "grunt": "^0.4.5", "grunt-cli": "^1.3.1", "grunt-shell": "^1.1.1", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "jsesc": "^1.0.0", "lodash": "^4.8.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "regexgen": "^1.3.0", "requirejs": "^2.1.22", "sort-object": "^3.0.2"}, "man": ["/Users/<USER>/projects/he/man/he.1"], "gitHead": "36afe179392226cf1b6ccdb16ebbb7a5a844d93a", "_id": "he@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "shasum": "84ae65fa7eafb165fddb61566ae14baf05664f0f", "tarball": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "fileCount": 6, "unpackedSize": 124138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbp8reCRA9TVsSAnZWagAAaKkQAJw9CB8UB3lCV2TspEiW\nE0WBWPPirKbc/PG2jXhd8DegomLJ1g2vm7fwibStKHG6fOKZqR+4cl3ZqwIV\nMrP7qeMqkrWgi2fHr3gCQmMqscCs7fzt30rVaNIygdJsQ1+j3tuahliNxBQj\nTGpUUyMKmGuPeGy8rn96brsLLS52wO2JTVlwjxHxPTLQvsveAMcq08R1fAgv\nQsTs9b/CkADxVLa9fXcbABzyUq9gdLNFo1mewptCYJYPxLwZ3cVekD01MeNx\nDEJ53zMI+hDZmU0U3Ouref7VRKq3C1x6aVHYz0lBz5BYNeSALe4PkylIvfGV\nhZfXCS9P18rvGrtYPhUC823SldmGTsC4h7xCcOASFtZgK3bs17K46VS/3vjY\naJoLV6s84UuTP2fSmyYWZrammmbRDdQDIgZ/oUbB6x7vawJKxspYHQi6k5cm\nCSzVdS1zJaPdOA2aa/ydXtjHMe1xLSb6p3QwG/qIjXNyaG6hUQFYAIO6Q2UX\n61WtyZOiwW6976HjWXGN9/AUGt6BVIpjTjTDvDyjiOuFn2RHRhZM+zunOh6w\n/tFnC+Xu9uc3/F1KH24NTwv5EThoQC6/Bjt0PjUDo+WHz3s2WoF8pCBdwgm+\nH73MhekvKYBgqQEoViGQpZxD4u+VxV9pI6AAThivOdpUBEaO0MqiP1aplYvQ\nVU8y\r\n=/3Zf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFw9hLeVA2/C0P4JKZ6ozNEh2BT3kcQ1yamXVy7QgWwwAiBJar9Qep/BCN5Kjj8TMJWHhi/mMiAkU0nWFow1KcrGNQ=="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/he_1.2.0_1537723102001_0.8366057239063311"}, "_hasShrinkwrap": false}}, "readme": "# he [![Build status](https://travis-ci.org/mathiasbynens/he.svg?branch=master)](https://travis-ci.org/mathiasbynens/he) [![Code coverage status](https://codecov.io/github/mathiasbynens/he/coverage.svg?branch=master)](https://codecov.io/github/mathiasbynens/he?branch=master) [![Dependency status](https://gemnasium.com/mathiasbynens/he.svg)](https://gemnasium.com/mathiasbynens/he)\n\n_he_ (for “HTML entities”) is a robust HTML entity encoder/decoder written in JavaScript. It supports [all standardized named character references as per HTML](https://html.spec.whatwg.org/multipage/syntax.html#named-character-references), handles [ambiguous ampersands](https://mathiasbynens.be/notes/ambiguous-ampersands) and other edge cases [just like a browser would](https://html.spec.whatwg.org/multipage/syntax.html#tokenizing-character-references), has an extensive test suite, and — contrary to many other JavaScript solutions — _he_ handles astral Unicode symbols just fine. [An online demo is available.](https://mothereff.in/html-entities)\n\n## Installation\n\nVia [npm](https://www.npmjs.com/):\n\n```bash\nnpm install he\n```\n\nVia [Bower](http://bower.io/):\n\n```bash\nbower install he\n```\n\nVia [Component](https://github.com/component/component):\n\n```bash\ncomponent install mathiasbynens/he\n```\n\nIn a browser:\n\n```html\n<script src=\"he.js\"></script>\n```\n\nIn [Node.js](https://nodejs.org/), [io.js](https://iojs.org/), [Narwhal](http://narwhaljs.org/), and [RingoJS](http://ringojs.org/):\n\n```js\nvar he = require('he');\n```\n\nIn [Rhino](http://www.mozilla.org/rhino/):\n\n```js\nload('he.js');\n```\n\nUsing an AMD loader like [RequireJS](http://requirejs.org/):\n\n```js\nrequire(\n  {\n    'paths': {\n      'he': 'path/to/he'\n    }\n  },\n  ['he'],\n  function(he) {\n    console.log(he);\n  }\n);\n```\n\n## API\n\n### `he.version`\n\nA string representing the semantic version number.\n\n### `he.encode(text, options)`\n\nThis function takes a string of text and encodes (by default) any symbols that aren’t printable ASCII symbols and `&`, `<`, `>`, `\"`, `'`, and `` ` ``, replacing them with character references.\n\n```js\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n```\n\nAs long as the input string contains [allowed code points](https://html.spec.whatwg.org/multipage/parsing.html#preprocessing-the-input-stream) only, the return value of this function is always valid HTML. Any [(invalid) code points that cannot be represented using a character reference](https://html.spec.whatwg.org/multipage/syntax.html#table-charref-overrides) in the input are not encoded:\n\n```js\nhe.encode('foo \\0 bar');\n// → 'foo \\0 bar'\n```\n\nHowever, enabling [the `strict` option](https://github.com/mathiasbynens/he#strict) causes invalid code points to throw an exception. With `strict` enabled, `he.encode` either throws (if the input contains invalid code points) or returns a string of valid HTML.\n\nThe `options` object is optional. It recognizes the following properties:\n\n#### `useNamedReferences`\n\nThe default value for the `useNamedReferences` option is `false`. This means that `encode()` will not use any named character references (e.g. `&copy;`) in the output — hexadecimal escapes (e.g. `&#xA9;`) will be used instead. Set it to `true` to enable the use of named references.\n\n**Note that if compatibility with older browsers is a concern, this option should remain disabled.**\n\n```js\n// Using the global default setting (defaults to `false`):\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly disallow named references:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'useNamedReferences': false\n});\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly allow named references:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'useNamedReferences': true\n});\n// → 'foo &copy; bar &ne; baz &#x1D306; qux'\n```\n\n#### `decimal`\n\nThe default value for the `decimal` option is `false`. If the option is enabled, `encode` will generally use decimal escapes (e.g. `&#169;`) rather than hexadecimal escapes (e.g. `&#xA9;`). Beside of this replacement, the basic behavior remains the same when combined with other options. For example: if both options `useNamedReferences` and `decimal` are enabled, named references (e.g. `&copy;`) are used over decimal escapes. HTML entities without a named reference are encoded using decimal escapes.\n\n```js\n// Using the global default setting (defaults to `false`):\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly disable decimal escapes:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'decimal': false\n});\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly enable decimal escapes:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'decimal': true\n});\n// → 'foo &#169; bar &#8800; baz &#119558; qux'\n\n// Passing an `options` object to `encode`, to explicitly allow named references and decimal escapes:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'useNamedReferences': true,\n  'decimal': true\n});\n// → 'foo &copy; bar &ne; baz &#119558; qux'\n```\n\n#### `encodeEverything`\n\nThe default value for the `encodeEverything` option is `false`. This means that `encode()` will not use any character references for printable ASCII symbols that don’t need escaping. Set it to `true` to encode every symbol in the input string. When set to `true`, this option takes precedence over `allowUnsafeSymbols` (i.e. setting the latter to `true` in such a case has no effect).\n\n```js\n// Using the global default setting (defaults to `false`):\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly encode all symbols:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'encodeEverything': true\n});\n// → '&#x66;&#x6F;&#x6F;&#x20;&#xA9;&#x20;&#x62;&#x61;&#x72;&#x20;&#x2260;&#x20;&#x62;&#x61;&#x7A;&#x20;&#x1D306;&#x20;&#x71;&#x75;&#x78;'\n\n// This setting can be combined with the `useNamedReferences` option:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'encodeEverything': true,\n  'useNamedReferences': true\n});\n// → '&#x66;&#x6F;&#x6F;&#x20;&copy;&#x20;&#x62;&#x61;&#x72;&#x20;&ne;&#x20;&#x62;&#x61;&#x7A;&#x20;&#x1D306;&#x20;&#x71;&#x75;&#x78;'\n```\n\n#### `strict`\n\nThe default value for the `strict` option is `false`. This means that `encode()` will encode any HTML text content you feed it, even if it contains any symbols that cause [parse errors](https://html.spec.whatwg.org/multipage/parsing.html#preprocessing-the-input-stream). To throw an error when such invalid HTML is encountered, set the `strict` option to `true`. This option makes it possible to use _he_ as part of HTML parsers and HTML validators.\n\n```js\n// Using the global default setting (defaults to `false`, i.e. error-tolerant mode):\nhe.encode('\\x01');\n// → '&#x1;'\n\n// Passing an `options` object to `encode`, to explicitly enable error-tolerant mode:\nhe.encode('\\x01', {\n  'strict': false\n});\n// → '&#x1;'\n\n// Passing an `options` object to `encode`, to explicitly enable strict mode:\nhe.encode('\\x01', {\n  'strict': true\n});\n// → Parse error\n```\n\n#### `allowUnsafeSymbols`\n\nThe default value for the `allowUnsafeSymbols` option is `false`. This means that characters that are unsafe for use in HTML content (`&`, `<`, `>`, `\"`, `'`, and `` ` ``) will be encoded. When set to `true`, only non-ASCII characters will be encoded. If the `encodeEverything` option is set to `true`, this option will be ignored.\n\n```js\nhe.encode('foo © and & ampersand', {\n  'allowUnsafeSymbols': true\n});\n// → 'foo &#xA9; and & ampersand'\n```\n\n#### Overriding default `encode` options globally\n\nThe global default setting can be overridden by modifying the `he.encode.options` object. This saves you from passing in an `options` object for every call to `encode` if you want to use the non-default setting.\n\n```js\n// Read the global default setting:\nhe.encode.options.useNamedReferences;\n// → `false` by default\n\n// Override the global default setting:\nhe.encode.options.useNamedReferences = true;\n\n// Using the global default setting, which is now `true`:\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &copy; bar &ne; baz &#x1D306; qux'\n```\n\n### `he.decode(html, options)`\n\nThis function takes a string of HTML and decodes any named and numerical character references in it using [the algorithm described in section ********* of the HTML spec](https://html.spec.whatwg.org/multipage/syntax.html#tokenizing-character-references).\n\n```js\nhe.decode('foo &copy; bar &ne; baz &#x1D306; qux');\n// → 'foo © bar ≠ baz 𝌆 qux'\n```\n\nThe `options` object is optional. It recognizes the following properties:\n\n#### `isAttributeValue`\n\nThe default value for the `isAttributeValue` option is `false`. This means that `decode()` will decode the string as if it were used in [a text context in an HTML document](https://html.spec.whatwg.org/multipage/syntax.html#data-state). HTML has different rules for [parsing character references in attribute values](https://html.spec.whatwg.org/multipage/syntax.html#character-reference-in-attribute-value-state) — set this option to `true` to treat the input string as if it were used as an attribute value.\n\n```js\n// Using the global default setting (defaults to `false`, i.e. HTML text context):\nhe.decode('foo&ampbar');\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly assume an HTML text context:\nhe.decode('foo&ampbar', {\n  'isAttributeValue': false\n});\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly assume an HTML attribute value context:\nhe.decode('foo&ampbar', {\n  'isAttributeValue': true\n});\n// → 'foo&ampbar'\n```\n\n#### `strict`\n\nThe default value for the `strict` option is `false`. This means that `decode()` will decode any HTML text content you feed it, even if it contains any entities that cause [parse errors](https://html.spec.whatwg.org/multipage/syntax.html#tokenizing-character-references). To throw an error when such invalid HTML is encountered, set the `strict` option to `true`. This option makes it possible to use _he_ as part of HTML parsers and HTML validators.\n\n```js\n// Using the global default setting (defaults to `false`, i.e. error-tolerant mode):\nhe.decode('foo&ampbar');\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly enable error-tolerant mode:\nhe.decode('foo&ampbar', {\n  'strict': false\n});\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly enable strict mode:\nhe.decode('foo&ampbar', {\n  'strict': true\n});\n// → Parse error\n```\n\n#### Overriding default `decode` options globally\n\nThe global default settings for the `decode` function can be overridden by modifying the `he.decode.options` object. This saves you from passing in an `options` object for every call to `decode` if you want to use a non-default setting.\n\n```js\n// Read the global default setting:\nhe.decode.options.isAttributeValue;\n// → `false` by default\n\n// Override the global default setting:\nhe.decode.options.isAttributeValue = true;\n\n// Using the global default setting, which is now `true`:\nhe.decode('foo&ampbar');\n// → 'foo&ampbar'\n```\n\n### `he.escape(text)`\n\nThis function takes a string of text and escapes it for use in text contexts in XML or HTML documents. Only the following characters are escaped: `&`, `<`, `>`, `\"`, `'`, and `` ` ``.\n\n```js\nhe.escape('<img src=\\'x\\' onerror=\"prompt(1)\">');\n// → '&lt;img src=&#x27;x&#x27; onerror=&quot;prompt(1)&quot;&gt;'\n```\n\n### `he.unescape(html, options)`\n\n`he.unescape` is an alias for `he.decode`. It takes a string of HTML and decodes any named and numerical character references in it.\n\n### Using the `he` binary\n\nTo use the `he` binary in your shell, simply install _he_ globally using npm:\n\n```bash\nnpm install -g he\n```\n\nAfter that you will be able to encode/decode HTML entities from the command line:\n\n```bash\n$ he --encode 'föo ♥ bår 𝌆 baz'\nf&#xF6;o &#x2665; b&#xE5;r &#x1D306; baz\n\n$ he --encode --use-named-refs 'föo ♥ bår 𝌆 baz'\nf&ouml;o &hearts; b&aring;r &#x1D306; baz\n\n$ he --decode 'f&ouml;o &hearts; b&aring;r &#x1D306; baz'\nföo ♥ bår 𝌆 baz\n```\n\nRead a local text file, encode it for use in an HTML text context, and save the result to a new file:\n\n```bash\n$ he --encode < foo.txt > foo-escaped.html\n```\n\nOr do the same with an online text file:\n\n```bash\n$ curl -sL \"http://git.io/HnfEaw\" | he --encode > escaped.html\n```\n\nOr, the opposite — read a local file containing a snippet of HTML in a text context, decode it back to plain text, and save the result to a new file:\n\n```bash\n$ he --decode < foo-escaped.html > foo.txt\n```\n\nOr do the same with an online HTML snippet:\n\n```bash\n$ curl -sL \"http://git.io/HnfEaw\" | he --decode > decoded.txt\n```\n\nSee `he --help` for the full list of options.\n\n## Support\n\n_he_ has been tested in at least:\n\n* Chrome 27-50\n* Firefox 3-45\n* Safari 4-9\n* Opera 10-12, 15–37\n* IE 6–11\n* Edge\n* Narwhal 0.3.2\n* Node.js v0.10, v0.12, v4, v5\n* PhantomJS 1.9.0\n* Rhino 1.7RC4\n* RingoJS 0.8-0.11\n\n## Unit tests & code coverage\n\nAfter cloning this repository, run `npm install` to install the dependencies needed for he development and testing. You may want to install Istanbul _globally_ using `npm install istanbul -g`.\n\nOnce that’s done, you can run the unit tests in Node using `npm test` or `node tests/tests.js`. To run the tests in Rhino, Ringo, Narwhal, and web browsers as well, use `grunt test`.\n\nTo generate the code coverage report, use `grunt cover`.\n\n## Acknowledgements\n\nThanks to [Simon Pieters](https://simon.html5.org/) ([@zcorpan](https://twitter.com/zcorpan)) for the many suggestions.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_he_ is available under the [MIT](https://mths.be/mit) license.\n", "maintainers": [{"email": "<EMAIL>", "name": "<PERSON>ias"}, {"email": "<EMAIL>", "name": "google-wombot"}], "time": {"modified": "2022-07-06T14:43:40.376Z", "created": "2013-06-25T07:04:30.073Z", "0.1.0": "2013-06-25T07:04:33.268Z", "0.1.1": "2013-06-25T07:42:07.589Z", "0.1.2": "2013-06-25T10:21:38.795Z", "0.1.3": "2013-06-26T04:49:05.788Z", "0.1.4": "2013-06-26T08:00:02.097Z", "0.1.5": "2013-06-26T09:43:53.310Z", "0.1.6": "2013-06-27T05:20:41.342Z", "0.1.7": "2013-06-27T07:35:03.487Z", "0.1.8": "2013-06-27T07:56:06.990Z", "0.1.9": "2013-06-27T11:36:05.943Z", "0.1.10": "2013-06-27T13:38:44.229Z", "0.2.0": "2013-06-27T15:47:21.538Z", "0.2.1": "2013-06-28T19:58:15.009Z", "0.3.0": "2013-07-01T16:48:42.300Z", "0.3.1": "2013-07-01T16:58:41.074Z", "0.3.2": "2013-07-27T11:26:46.217Z", "0.3.3": "2013-07-30T10:27:19.524Z", "0.3.4": "2013-08-02T22:03:05.837Z", "0.3.5": "2013-08-04T07:47:39.912Z", "0.3.6": "2013-08-07T10:47:20.953Z", "0.4.0": "2014-05-23T08:23:36.693Z", "0.4.1": "2014-05-24T18:58:13.050Z", "0.5.0": "2014-08-24T07:04:48.183Z", "1.0.0": "2016-04-05T19:51:06.638Z", "1.1.0": "2016-05-06T10:35:44.511Z", "1.1.1": "2017-01-23T13:55:43.985Z", "1.2.0": "2018-09-23T17:18:22.163Z"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/he.git"}, "users": {"brugnara": true, "jkimbo": true, "medikoo": true, "julien-f": true, "nguru": true, "chalassa": true, "kmck": true, "joakin": true, "mbouclas": true, "tzsiga": true, "aslezak": true, "h0ward": true, "edin-m": true, "markthethomas": true, "koulmomo": true, "cheapsteak": true, "moimikey": true, "stany": true, "bkonkle": true, "neoklosch": true, "monkeymonk": true, "vtocco": true, "princetoad": true, "abuelwafa": true, "anoubis": true, "stone_breaker": true, "vitorluizc": true, "lagden": true, "d-band": true, "wangnan0610": true, "yatsu": true, "shakakira": true, "oleg_tsyba": true, "restuta": true, "gugadev": true}, "homepage": "https://mths.be/he", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "readmeFilename": "README.md", "license": "MIT"}