# 智能政务平台 - AI政务问答系统

这是一个完整的智能政务问答系统，集成了本地知识库、云端AI服务和联网搜索功能，为政府部门提供高效的在线问答服务。

## 📁 项目结构

```
zhengwudemobase0729/
├── index.html                      # 主页面文件
├── styles.css                      # 样式文件
├── script.js                       # 前端交互逻辑
├── requirements.txt                # Python依赖包列表（已更新）
├── test-markdown.html              # Markdown测试页面
├── venv_new/                      # Python虚拟环境目录（推荐使用）
├── Backend/                       # 后端服务模块
│   ├── app.py                     # Flask主应用服务（7个API端点）
│   ├── qa_database.py             # 问答数据库服务（Excel数据处理）
│   ├── sensitive_filter.py        # 敏感词过滤模块（40+敏感词）
│   ├── ali_knowledge_service.py   # 阿里云知识库服务（双模式支持）

│   ├── smart_policy_service.py    # 智能问策服务（政策分析）
│   └── __pycache__/              # Python缓存目录
├── DB/
│   └── QAlist.xlsx               # 政务问答数据库（8列Excel格式）
├── pic/                          # 图片资源目录
│   ├── robot1.gif                # AI机器人动画1
│   ├── robot2.gif                # AI机器人动画2
│   ├── robot3.gif                # AI机器人动画3
│   └── shukelogo.png             # 联通数科Logo
└── README.md                     # 项目说明文档（本文件）
```

## 🚀 核心功能

### 智能问答系统
- 🤖 **四种问答模式**：智能导办、知识库检索、联网搜索、智能问策
- 📚 **本地知识库**：基于Excel数据的政务问答数据库，支持精确匹配和模糊匹配
- ☁️ **云端AI服务**：集成阿里云百炼平台和硅基流动AI模型
- 🔍 **联网搜索**：实时获取最新政策信息和办事指南
- 📄 **智能问策**：专业的政策链接解析和政策问答服务
- 🛡️ **安全防护**：内置40+敏感词过滤和内容安全校验

### 用户界面特色
- 🎨 **政务风格设计**：红蓝白配色方案，符合政府网站规范
- 💬 **实时聊天界面**：流畅的对话体验，支持打字机效果
- 🤖 **AI助手形象**：可爱的机器人动画，增强用户亲和力
- 📱 **响应式布局**：适配桌面端、平板和移动设备
- ⚡ **快捷功能**：高频咨询、智能导办等一键操作

### 数据管理
- 📊 **Excel数据源**：支持8列格式的问答数据管理
- 🔄 **热更新**：支持运行时重新加载数据，无需重启服务
- � **统计分析**：提供问答数据统计和分类管理
- 🏷️ **智能分类**：按业务类型自动分类问题和答案

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 现代浏览器（Chrome、Firefox、Edge、Safari）
- 网络连接（用于云端AI服务）

### 安装依赖
```bash
# 1. 激活虚拟环境（推荐使用venv_new）
.\venv_new\Scripts\activate  # Windows
# 或
source venv_new/bin/activate  # Linux/Mac

# 2. 安装依赖包
pip install -r requirements.txt
```

**依赖包说明：**
- `Flask 2.3.3` - Web框架，提供7个API端点
- `Flask-CORS 4.0.0` - 跨域支持
- `openpyxl 3.1.2` - Excel文件处理
- `dashscope >=1.14.0` - 阿里云百炼平台SDK

### 启动服务
```bash
# 启动后端Flask服务
python Backend/app.py
```
服务启动后会在 `http://127.0.0.1:5000` 运行

### 访问前端
1. 在浏览器中打开 `index.html` 文件
2. 或者直接双击 `index.html` 文件
3. 开始使用智能政务问答功能

### 配置说明
项目使用了以下第三方服务，需要相应的API密钥：
- **阿里云百炼平台**：用于知识库检索、联网搜索和智能问策
  - 知识库检索应用ID: `fd00ab6eaf7c47a6a9ab1d651b3be0b1`
  - 联网搜索应用ID: `ff034050df7a4dc39e491c6307d3e5b3`
  - 智能问策应用ID: `c0a9b3cbf9134a85959eb83860a5c7d5`
- **硅基流动平台**：AI模型调用服务（当前已停用）

> ⚠️ **安全提醒**：当前代码中包含了测试用的API密钥，生产环境请替换为您自己的密钥并使用环境变量管理

## 💡 使用指南

### 四种问答模式

#### 1. 智能导办（默认模式）
- 基于本地Excel数据库进行问答
- 支持精确匹配、关键词匹配和模糊匹配
- 适合标准化的政务办事咨询
- 可显示办理链接和咨询电话
- 数据源：`DB/QAlist.xlsx`（8列格式）

#### 2. 知识库检索
- 调用阿里云百炼平台知识库
- 支持复杂问题的智能理解
- 提供文档引用和来源信息
- 适合政策解读和深度咨询
- API端点：`/ask_knowledge`

#### 3. 联网搜索
- 实时联网获取最新信息
- 支持时效性强的问题
- 需要用户同意使用协议
- 适合最新政策和动态查询
- API端点：`/ask_ai`（online_search=true）

#### 4. 智能问策（新增）
- 专业的政策链接解析和政策问答
- 支持政策文档深度分析
- 智能识别政策链接内容
- 适合复杂政策咨询场景
- API端点：`/ask_policy`

### 操作说明
1. **提问**：在输入框中输入问题，点击发送或按回车
2. **切换模式**：点击相应按钮切换问答模式
3. **查看步骤**：系统会显示处理步骤和来源信息
4. **快捷操作**：使用高频咨询功能快速提问

## 🔧 技术架构

### 前端技术
- **HTML5**：语义化标签，无障碍友好设计
- **CSS3**：Grid布局、Flexbox、渐变、动画效果
- **JavaScript ES6+**：模块化编程，异步处理，DOM操作
- **Font Awesome**：图标库，提供丰富的UI图标

### 后端技术
- **Python Flask**：轻量级Web框架，提供RESTful API
- **Flask-CORS**：跨域资源共享支持
- **openpyxl**：Excel文件读写处理


### 第三方服务
- **阿里云百炼平台**：知识库检索、联网搜索和智能问策服务

- **dashscope SDK**：阿里云AI服务开发工具包

### 数据存储
- **Excel文件**：本地问答数据库，支持热更新
- **内存缓存**：问答数据缓存，提升查询性能

## 📱 浏览器兼容性

- ✅ Chrome 70+
- ✅ Firefox 65+
- ✅ Safari 13+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 🛠️ 开发指南

### API接口说明

#### 1. 智能导办接口
```
POST /ask
Content-Type: application/json

{
    "question": "用户问题"
}
```

#### 2. 知识库检索接口
```
POST /ask_knowledge
Content-Type: application/json

{
    "question": "用户问题"
}
```

#### 3. 联网搜索接口
```
POST /ask_ai
Content-Type: application/json

{
    "question": "用户问题",
    "online_search": true
}
```

#### 4. 智能问策接口（新增）
```
POST /ask_policy
Content-Type: application/json

{
    "question": "用户问题（可包含政策链接）"
}
```

#### 5. 数据管理接口
```
GET /qa_stats              # 获取问答数据统计
POST /reload_data          # 重新加载Excel数据
GET /get_all_types         # 获取所有数据类型
GET /get_questions_by_type/<type_name>  # 按类型获取问题
```

### 数据格式说明

Excel数据库（QAlist.xlsx）采用8列格式：
- A列：问题ID
- B列：业务类型
- C列：问题内容
- D列：答案内容
- E列：关键词（用分号分隔）
- F列：是否支持在线办理（是/否）
- G列：办理链接
- H列：咨询电话

### 自定义配置

1. **修改API密钥**：
   - 编辑 `Backend/ali_knowledge_service.py` 中的阿里云API密钥
   - 编辑 `Backend/smart_policy_service.py` 中的智能问策API密钥

2. **更新问答数据**：
   - 直接编辑 `DB/QAlist.xlsx` 文件
   - 调用 `/reload_data` 接口热更新数据

3. **自定义敏感词**：
   - 编辑 `Backend/sensitive_filter.py` 中的敏感词列表

4. **界面定制**：
   - 修改 `styles.css` 调整界面样式
   - 修改 `script.js` 调整交互逻辑
   - 修改 `index.html` 调整页面结构

## 📄 许可证

本项目采用 MIT 许可证，允许自由使用、修改和分发。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目：
1. Fork 本项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📊 项目统计信息

### 代码统计
- **后端模块**: 5个Python文件
- **API端点**: 7个RESTful接口
- **AI服务**: 3种不同的AI调用模式
- **数据处理**: 支持8列Excel格式
- **安全过滤**: 40+敏感词检测

### 文件详情
| 文件名 | 功能描述 | 主要特性 |
|--------|----------|----------|
| `app.py` | Flask主应用 | 7个API端点，完整的错误处理 |
| `qa_database.py` | 数据库服务 | Excel数据加载，多种匹配算法 |
| `ali_knowledge_service.py` | 阿里云服务 | 双模式切换，文档引用提取 |

| `smart_policy_service.py` | 智能问策 | 政策链接识别，专业分析 |
| `sensitive_filter.py` | 安全过滤 | 40+敏感词，多层校验 |

### 虚拟环境说明
- **推荐使用**: `venv_new/` 目录
- **已删除**: 旧的 `venv/` 目录（存在问题）
- **激活命令**: `.\venv_new\Scripts\activate` (Windows)

## 🔧 启动步骤总结

```bash
# 1. 进入项目目录
cd e:\Demo\zhengwudemobase0729

# 2. 激活虚拟环境（使用venv_new）
.\venv_new\Scripts\activate

# 3. 安装依赖（首次运行）
pip install -r requirements.txt

# 4. 启动后端服务
python Backend/app.py

# 5. 打开前端页面
# 双击 index.html 或在浏览器中打开该文件
```

---

**⚠️ 重要提醒**：
1. 本项目集成了真实的AI服务，请妥善保管API密钥，避免泄露
2. 生产环境部署时请使用环境变量管理敏感信息
3. 推荐使用 `venv_new` 虚拟环境，旧的 `venv` 已删除
4. 项目包含完整的错误处理和日志记录功能