{"_id": "html-encoding-sniffer", "_rev": "14-ebd4a94aefe3272a22c0bbd72d1ed8ca", "name": "html-encoding-sniffer", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "html-encoding-sniffer", "version": "1.0.0", "keywords": ["encoding", "html"], "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "WTFPL", "_id": "html-encoding-sniffer@1.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "dist": {"shasum": "19f93ff0d070bd3c238f1ff16ea2f68cc0d42c96", "tarball": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-1.0.0.tgz", "integrity": "sha512-5GPXiuqEy2x2yiMCkUKKI+VX7djbWBsORMhsM9fk6kQUYOXFWaEidMDPqpksQSlfCSfryGei2fwHSp9E6m5Qiw==", "signatures": [{"sig": "MEQCIGIcO7v2uNQ9osC1cnOFwUzSy5s0aC4ll419+xjKJ+spAiASzR2t08IR2rrEX7zOHF9Gnde9dlxvcHl5osedFoRlsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/html-encoding-sniffer.js", "_from": ".", "files": ["lib/"], "_shasum": "19f93ff0d070bd3c238f1ff16ea2f68cc0d42c96", "gitHead": "8cd325d2e567e33dec4f0fa489c556e0e290db5d", "scripts": {"lint": "eslint lib test", "test": "mocha"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "Sniff the encoding from a HTML byte stream", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"whatwg-encoding": "^1.0.1"}, "devDependencies": {"mocha": "^3.1.2", "eslint": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/html-encoding-sniffer-1.0.0.tgz_1476589995029_0.5327760553918779", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "html-encoding-sniffer", "version": "1.0.1", "keywords": ["encoding", "html"], "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "WTFPL", "_id": "html-encoding-sniffer@1.0.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "dist": {"shasum": "79bf7a785ea495fe66165e734153f363ff5437da", "tarball": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-1.0.1.tgz", "integrity": "sha512-mZeyJClkfuKBpYabLFSeHsbcOU8eZQcSqSxfuaazMkyMIbJXZPu546YZPXdDeSdkwfEKm7E363XNlU4QYBEh8Q==", "signatures": [{"sig": "MEUCIDCE+kPm9HBTL0kmLzBqAKYxYrtHUb8WB36jniNT+FSOAiEA3RrquBnezr89HVsPNeFdx1rmr1pGZi1p/YpLpegh6kU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/html-encoding-sniffer.js", "_from": ".", "files": ["lib/"], "_shasum": "79bf7a785ea495fe66165e734153f363ff5437da", "gitHead": "9b4a785caf193f8852770b7dfc7194431fbf8bed", "scripts": {"lint": "eslint lib test", "test": "mocha"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "Sniff the encoding from a HTML byte stream", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"whatwg-encoding": "^1.0.1"}, "devDependencies": {"mocha": "^3.1.2", "eslint": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/html-encoding-sniffer-1.0.1.tgz_1476591787938_0.5426098443567753", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.2": {"name": "html-encoding-sniffer", "version": "1.0.2", "keywords": ["encoding", "html"], "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "html-encoding-sniffer@1.0.2", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "dist": {"shasum": "e70d84b94da53aa375e11fe3a351be6642ca46f8", "tarball": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-1.0.2.tgz", "integrity": "sha512-71l<PERSON>ziiDnsuabfdYiUeWdCVyKuqwWi23L8YeIgV9jSSZHCtb6wB1BKWooH7L3tn4/FuZJMVWyNaIDr4RGmaSYw==", "signatures": [{"sig": "MEYCIQCmVY1pliKPW15YZp55q01Ie8C6HBCwGtRa2s4p9/EfSgIhANojKrDPnWn8biYdzrf5KmEZKcgmXso3Ey0qyDrLA8m2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/html-encoding-sniffer.js", "files": ["lib/"], "gitHead": "8d69308c38af4b19170d2caeac6e16c6f52f5e01", "scripts": {"lint": "eslint lib test", "test": "mocha"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Sniff the encoding from a HTML byte stream", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"whatwg-encoding": "^1.0.1"}, "devDependencies": {"mocha": "^3.1.2", "eslint": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/html-encoding-sniffer-1.0.2.tgz_1508717756193_0.17921806732192636", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "html-encoding-sniffer", "version": "2.0.0", "keywords": ["encoding", "html"], "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "html-encoding-sniffer@2.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "dist": {"shasum": "70b3b69bb5999f35d0d4495d79079f35630e71ae", "tarball": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-Y9prnPKkM7FXxQevZ5UH8Z6aVTY0ede1tHquck5UxGmKWDshxXh95gSa2xXYjS8AsGO5iOvrCI5+GttRKnLdNA==", "signatures": [{"sig": "MEUCIGc7V5pPGIk3cL5WGoGnTl/t4V1y3r3SwGcniGzQEKsHAiEA86Gz5a85UAWSXOVTV+VnMFBX6SjaNWQEjT7xNgEbudI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEhkaCRA9TVsSAnZWagAAY3gP/A2kdlKGHR8WGwgG6fU/\nUvrYvRfaNQddDNedUwRuZG8Va63QMeeU8tTe8UyphPyqrbhOJLN88oTR7sOm\n//ZZGL8PIHIr+KCqPUWbld8Jf7DcJBQ/8VgyyH1vlQhQ33M5TEEFsjNesoSz\nWY8fvZGBgd4oAbt3rd9gh2f2DTlcwD28t6Oy6V5U1K+BfHm3hi/hnsYuGaa4\nH/6EyoE5Zbjg0rlMZo6TX7LRk90/K4SEkf0MoDsGN1zjg3JJE1QtFI+JEJsc\nkzlEWhRoCAMwGjzc8JjhsaYmRd42BG8EAmhc67sFX4UUOwGQIbQCXrVdxVCY\nHbVbw+qGkwiHO4KExBDVrdUVM9HpZdSp+N8mOufSNUXKOSiAomZlt9xYoFFG\nDvna8ZAELVeXKdsEVKRqtlmEjy2NCCJvannsHMtjq83iAKmrNCKNsBQbxDqY\nwT52Y6PDelZ2/DWJY8b5Qexw/vpdPq84bbBoAvDHCsxRpX7GegyBcOMOdczC\nFgB+eEquEx/0I6M6sHpRLWW2ksSfci9q9MTPZFVGNo6/IrXRmxebAixwoqYY\nqWq9F4es5uig7PR2BAgfmk28bv9ITrUk1bFGpOVu3mO+MyT32bNQpkabnnkR\nPFXXTFqPSrU6Iu78brd4owMlYuAMiOIceQQqWgsFd31urNXIGoqymCM7hgL3\nozTt\r\n=L2Z3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/html-encoding-sniffer.js", "engines": {"node": ">=10"}, "gitHead": "c5e8fe3539a6ad4be2ffe5e5d61060885e3f6d64", "scripts": {"lint": "eslint .", "test": "mocha"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Sniff the encoding from a HTML byte stream", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"whatwg-encoding": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^7.0.0", "eslint": "^6.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/html-encoding-sniffer_2.0.0_1578244378061_0.49863360811709123", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "html-encoding-sniffer", "version": "2.0.1", "keywords": ["encoding", "html"], "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "html-encoding-sniffer@2.0.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "dist": {"shasum": "42a6dc4fd33f00281176e8b23759ca4e4fa185f3", "tarball": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ==", "signatures": [{"sig": "MEQCID+P2NFwTcmawj2OoLNz53A2ODABPKKPYfV8CmO+c5t/AiAyp0yYV9udoQqj5EuxcBja3KCamIW8bDn4CQWG0VXD3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUvxeCRA9TVsSAnZWagAAv58P/1SkBqtI50/P0EkZosfD\ns1kSdWcWYhpD/ENunOEAmq+sD6uNIppAP8X+h24ExIpdIIxxh+ouA/LxRh0J\nK8TT/XSrXk0AqGIgScFYD+guPXe08+xUxuNzf5DEmIa1TPeTTosoNiGdFIsK\n90rw01PVqvIVgu08hTBQjEWeiT+C3LfiC37Sm1kRkW+7+Lo+56Z0HjNiPyEr\nBFl9wPBWOETYuTb9Ff+d0KVseqF5FXEcsJhDJIerFJGZRUuaIZ8Z+ir9INPk\nnMYoxj4ZB6sNCSOXBdrtBrSVe2xM4mJNUOuWMgaxTyRNbGo7TFIVvWg6uqkq\nbcWfyeYkwujj2SgNtudxao4ZOloOO5n0P1qNFRNowfbMwMNSOQsHSasY788K\nxvbtuwD6lEtox5gJmWf05s+l+S1NoLB9RDMna+UCth2ShxQE6//LQt3763JT\n/j6MzEqTGyCAYOIVPOrkPqvXdsjNnRFiN1e9f7hbLb+t6+1AXgX6b1WnH/m9\n/dgbIuszO2FGh226jM5MxPcCTWNKuAksfuJn7vU0DsqYqLs3DqphA1V8R5f0\n1fNqAojPfIUlGv5N/bpgDdHm+IA4Cbz20bfovyUUguFUjJHwa+/STYLCy2uM\nX/5qhUZxRmLooaCeEeISQjD+aF3Cqc9VERvlL4DIx7gkGRodc5dGikGngDE8\nsQ3g\r\n=7eGa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/html-encoding-sniffer.js", "engines": {"node": ">=10"}, "gitHead": "24a3f567dcffa23ef6d2c3f238b1299f7a71569f", "scripts": {"lint": "eslint .", "test": "mocha"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Sniff the encoding from a HTML byte stream", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"whatwg-encoding": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^7.0.0", "eslint": "^6.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/html-encoding-sniffer_2.0.1_1582496861635_0.25573667939106226", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "html-encoding-sniffer", "version": "3.0.0", "keywords": ["encoding", "html"], "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "html-encoding-sniffer@3.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "dist": {"shasum": "2cb1a8cf0db52414776e5b2a7a04d5dd98158de9", "tarball": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==", "signatures": [{"sig": "MEYCIQDD2Auim96lEI5XZM090xuCmLpyzetKPrqqKJv5zZkF7gIhAPQisGTiiGJXXzvkxPKB6nx3DRtuY6MoXKncmNDh8LRR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2tqcCRA9TVsSAnZWagAAEAwP/jj89iMOgV3PssvMtzfk\nVKg21813w27zJEqNya/zc7xXVobfaVILk5XuT6UIr6H4/LEeYcAVWJRVHQuH\nJqSFVMjryVcMXVrAldneVDPTGn/NvpJQrKAOkzABvGifEBw1FAwhcZCr+W/D\nirWkpieFsEhM00gFj76N3SNgjnUR9ufx2w8DhKobSsmSpCeAlG2mBhqLordN\ns8AT7LJKpyUS6qFDfeyLpQVzTDsbnl6WmBV1cOYSE8/0VEqMUv1AHY/HmU0A\n38PzBC4eM8t2o85HQ7psEixuUvU5hJr+gwjC6vmGg0j2rCaG0xM1cc3Q8SO1\npYqKFZoZRDZOj/JZL4oTRBBoZojxjlmPydVPz6Ablmp8VxaIEQ3G5xYU+SMb\nRS3+yMcgaBLLULp890Cc1ASXYfmTUsuRHvlHbyCB9y+j4Bl+TisLfFea4TxL\nMqTAxU+37E1os13iMXWrtr/j3J1tVRgqbysZSGwE5FoewqrxIXb89QXzom2Q\nlKtA8yhE2sBZ/TPSHsCzfLPPbq93Mg1VaOcfQlmJzlCVkS+3uqQFLOGxQIf2\nLgCgsBKCXFZlPXFnXhu+TtBFew/oOl7RlYPDD8Dgnn/NfslPy63czWUent7w\ntm6wAGtrQv1VV4zpiCtos06uoUdOcFG1Dhxl4zIEaqVouMlUT7CWo2ADx5pC\nkC0m\r\n=X8Aj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/html-encoding-sniffer.js", "engines": {"node": ">=12"}, "gitHead": "7de54310087887b3fbb2c89b05d5fbc36da82078", "scripts": {"lint": "eslint .", "test": "mocha"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Sniff the encoding from a HTML byte stream", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {"whatwg-encoding": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.1.1", "eslint": "^7.32.0", "@domenic/eslint-config": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/html-encoding-sniffer_3.0.0_1632004810793_0.020944850433149753", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "html-encoding-sniffer", "version": "4.0.0", "keywords": ["encoding", "html"], "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "html-encoding-sniffer@4.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "dist": {"shasum": "696df529a7cfd82446369dc5193e590a3735b448", "tarball": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ==", "signatures": [{"sig": "MEQCIA3R8wVVN8vxa/J2Th/35LgD9fOwH/68s6hOF4ymJ839AiAbXAQJDq7NjdARWIawmRpjKHRJdilzUHTy2zSxApNybw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11662}, "main": "lib/html-encoding-sniffer.js", "engines": {"node": ">=18"}, "gitHead": "d655e5adef00678d24d228c1cd8e34f1299c2e1f", "scripts": {"lint": "eslint .", "test": "node --test"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Sniff the encoding from a HTML byte stream", "directories": {}, "_nodeVersion": "21.1.0", "dependencies": {"whatwg-encoding": "^3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.53.0", "@domenic/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/html-encoding-sniffer_4.0.0_1699774695445_0.18692303422369672", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2016-10-16T03:53:16.596Z", "modified": "2024-11-12T07:14:10.358Z", "1.0.0": "2016-10-16T03:53:16.596Z", "1.0.1": "2016-10-16T04:23:09.751Z", "1.0.2": "2017-10-23T00:15:57.146Z", "2.0.0": "2020-01-05T17:12:58.185Z", "2.0.1": "2020-02-23T22:27:41.834Z", "3.0.0": "2021-09-18T22:40:10.894Z", "4.0.0": "2023-11-12T07:38:15.637Z"}, "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "author": {"url": "https://domenic.me/", "name": "Domenic <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "keywords": ["encoding", "html"], "repository": {"url": "git+https://github.com/jsdom/html-encoding-sniffer.git", "type": "git"}, "description": "Sniff the encoding from a HTML byte stream", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "readme": "# Determine the Encoding of a HTML Byte Stream\n\nThis package implements the HTML Standard's [encoding sniffing algorithm](https://html.spec.whatwg.org/multipage/syntax.html#encoding-sniffing-algorithm) in all its glory. The most interesting part of this is how it pre-scans the first 1024 bytes in order to search for certain `<meta charset>`-related patterns.\n\n```js\nconst htmlEncodingSniffer = require(\"html-encoding-sniffer\");\nconst fs = require(\"fs\");\n\nconst htmlBytes = fs.readFileSync(\"./html-page.html\");\nconst sniffedEncoding = htmlEncodingSniffer(htmlBytes);\n```\n\nThe passed bytes are given as a `Uint8Array`; the Node.js `Buffer` subclass of `Uint8Array` will also work, as shown above.\n\nThe returned value will be a canonical [encoding name](https://encoding.spec.whatwg.org/#names-and-labels) (not a label). You might then combine this with the [whatwg-encoding](https://github.com/jsdom/whatwg-encoding) package to decode the result:\n\n```js\nconst whatwgEncoding = require(\"whatwg-encoding\");\nconst htmlString = whatwgEncoding.decode(htmlBytes, sniffedEncoding);\n```\n\n## Options\n\nYou can pass two potential options to `htmlEncodingSniffer`:\n\n```js\nconst sniffedEncoding = htmlEncodingSniffer(htmlBytes, {\n  transportLayerEncodingLabel,\n  defaultEncoding\n});\n```\n\nThese represent two possible inputs into the [encoding sniffing algorithm](https://html.spec.whatwg.org/multipage/syntax.html#encoding-sniffing-algorithm):\n\n- `transportLayerEncodingLabel` is an encoding label that is obtained from the \"transport layer\" (probably a HTTP `Content-Type` header), which overrides everything but a BOM.\n- `defaultEncoding` is the ultimate fallback encoding used if no valid encoding is supplied by the transport layer, and no encoding is sniffed from the bytes. It defaults to `\"windows-1252\"`, as recommended by the algorithm's table of suggested defaults for \"All other locales\" (including the `en` locale).\n\n## Credits\n\nThis package was originally based on the excellent work of [@nicolashenry](https://github.com/nicolashenry), [in jsdom](https://github.com/tmpvar/jsdom/blob/16fd85618f2705d181232f6552125872a37164bc/lib/jsdom/living/helpers/encoding.js). It has since been pulled out into this separate package.\n", "readmeFilename": "README.md", "users": {"essamonline": true}}