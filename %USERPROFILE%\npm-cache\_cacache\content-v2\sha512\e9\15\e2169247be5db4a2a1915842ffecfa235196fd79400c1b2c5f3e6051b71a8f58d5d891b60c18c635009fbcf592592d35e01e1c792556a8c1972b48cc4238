{"_id": "union", "_rev": "76-f676e37d2efd2568d0229c96aef85951", "name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "dist-tags": {"latest": "0.6.0"}, "versions": {"0.1.0": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/flatiron-http.git"}, "devDependencies": {"vows": "0.5.x"}, "scripts": {"test": "vows test/**/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.1.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "38a9c186828b4f23238e359070bf51ccb6cb9e7f", "tarball": "https://registry.npmjs.org/union/-/union-0.1.0.tgz", "integrity": "sha512-u/fZQQVzHgQqXbzbULpCL33bTI4UUV41Vx1iI+4I36huKvfFJaoPu01m9eDZpFsSHNUsMfE5kjl3rvpEAEOKdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDoch001XRbKmyLwQeQF6qk/Slcyr6nP2bX8Htqi80eUAiBPl5VQbk0EMryecYGuT8N9umnbOhSNhWwV+nykwHwewQ=="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/flatiron-http.git"}, "dependencies": {"qs": "0.3.2"}, "devDependencies": {"director": "1.x.x", "request": "2.x.x", "vows": "0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.1.2", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "24ebaae1631f7a280a76d74ee2df19b616ab2396", "tarball": "https://registry.npmjs.org/union/-/union-0.1.2.tgz", "integrity": "sha512-u41U72n3CzjNnc6XvfNd25F0ZM3x+NLMsRYL7SX0gd4tJ3HkIGnf5j5gXPBV+f0V/sVJZN86y9v+6Ppb3B8XXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGgNu4Q2HYuH36AzSx2lFSyF+5Z49QxFZyvdoPQ6bYMrAiEA9fo4PwH/Lu218j/TtVEUHsZsezpvKYaWjsZQkVtzicY="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/flatiron-http.git"}, "dependencies": {"qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.1.3", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "db0863af0e674d6d51a75eee10b83cda7508b309", "tarball": "https://registry.npmjs.org/union/-/union-0.1.3.tgz", "integrity": "sha512-Xd37LYXytDxVZFRpUmxYMKhbglcURzLLDPJLnicPXCiOeSptkaTWDrOuQcO8Z92ucZueTZkHBj2Dd/ydf82hcA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6iYtmQ091WZZK4jXiO2N27MMoxbmX1f/lJ/ll0HO+LQIhAIjeZCwKelHU7wqOZ0qY+kkbJ4rgIgJAPzc8mKpEPToT"}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.4", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/flatiron-http.git"}, "dependencies": {"qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.1.4", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "668a227031e8bb466f00b422f8196975b5e3355c", "tarball": "https://registry.npmjs.org/union/-/union-0.1.4.tgz", "integrity": "sha512-pY8OgTHe48BSniGKbdE0cB8SxKf2eLTp/xiGdR5fAUmzWHW5V5m2QBtZRU5sjok4+vU9aaSKtrpiCdUGnfYZBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBgFGEmW2FaUkZQtnxSR2zukxc6S77/HfVLtTYCYbTwgAiAF9MCCfRpGRLmoHzEsbY1Awi8puzkZQKtnn2MCZ+uqvw=="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.5": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.5", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/flatiron-http.git"}, "dependencies": {"qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.1.5", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "8d4213fcdc1dd0b73d561228c4934acb48f47417", "tarball": "https://registry.npmjs.org/union/-/union-0.1.5.tgz", "integrity": "sha512-knwNu7Pbv+1qOqlRaWuJPG70PoZe7oa+kKGDivOgvVsTetjjZADbij2bMi1AruWhsAyyFoLbeqEXgLzDJbaM3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG5oEt1xLZdgWs2HKDCcLYX6NqoUsRTd3ccljaYcTg1HAiASWbqRJPGh8R5HkNlz8PMU+nvNVIPr83xkNviMkFK0CA=="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.6": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.6", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/flatiron-http.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.1.6", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "86594b7b088bf7349c6397b70a7ea2f5f772c783", "tarball": "https://registry.npmjs.org/union/-/union-0.1.6.tgz", "integrity": "sha512-kTkJenYrnCeWm6WnQwbpE10tjpSi/umINolhBpbNWH9cDN4nTWxhFL/PM1J0ch8PU0SPgSALbJQ9x4ueKGks4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClMNzjadOvKtwaOqHWctJ33lXeCPr0eIeojLgndvHRxQIhAICxXIXMAEswAJxq9W68uOa5ybAbrT5gU8iCcpxJ6MVW"}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.7": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.7", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/flatiron-http.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.1.7", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "576eb28f6704207e84567d701c8ffbcaa30f4f26", "tarball": "https://registry.npmjs.org/union/-/union-0.1.7.tgz", "integrity": "sha512-kiGFcWxykWHhFi2gEuTmFjLKXopbbKvveqQ07m4nINw5YQ3scyF/xwFSnh6BvTJnCbsoDvzeIX/Ysb6qUiK1iQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFb6r+47Wq02GEY3ONtOTMiVsTT27NkTqCYPI68do9JjAiEA6uKchd/F/Hhx8DLyPoYTO/7fY1F5lvek1IJ5uCubM+s="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.8": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.1.8", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "_id": "union@0.1.8", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"shasum": "98ea72738d93bc8e7d4c4d4977d4cbd95976056b", "tarball": "https://registry.npmjs.org/union/-/union-0.1.8.tgz", "integrity": "sha512-b360VOqeMBYk7BZ3tADS/Mxq/hctOJRxvLfWJXOmhVNhMB2ICmJIEGXZTVqp2Et6yDVgNTLbNYSY8348vMDRvA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHH2nzUx9lP6Vq9mhWWTZ6c0iVZAZgSX97CW3SYREHZRAiEA7TqzFDmGjfeibU1rnaIgOsiYwYjNw9Te3nqE8DRWMus="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "_id": "union@0.2.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"shasum": "f7f33f97836c218a2b87cb5fb95427de9b991707", "tarball": "https://registry.npmjs.org/union/-/union-0.2.0.tgz", "integrity": "sha512-cxcqRSqJzC6Z8+vaBotIrWlNozl/iTwyJMyGudtRRLqAJXqAKhy6VrjfcrfRXwYO/vz5uiaDZ7e3djH3Dd+mjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDesf4kw7x5G6NqB4MkybCepTSDfW7URDECDWouND0djAIhAJESTzi07rrGRwSYvJOiZ/TN1oz9hBnafDFM41yhoGsq"}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.2.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.3.2"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "_id": "union@0.2.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"shasum": "dd80c47580f58ca108f02a0835bc616088ac7667", "tarball": "https://registry.npmjs.org/union/-/union-0.2.1.tgz", "integrity": "sha512-iwIpRf6dUH9nNyLMKbSKSZddP2aTV2reINU+UlewOrWs9yFnFDHPv43J0XaAV16zlboE+H2FYPqIMmctAU0Hlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHoBSe/Ad4xVancEfCDILyADILcVLokPJoteeYwG/J10AiAi4RxXAuLSP34XhC8D90lbKlwj1KTmxdZCLNhbMdiPIA=="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.3.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.4.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "_id": "union@0.3.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"shasum": "7250ac29041a34e789d11755a4f1309f94e771f5", "tarball": "https://registry.npmjs.org/union/-/union-0.3.0.tgz", "integrity": "sha512-+xTo2gmurlqsTzWlsX4ZESbkElSSjwFth3J/Vy3FSMyHOnlVoKG3BG1ooKPa+/TniI+PqkWQCVmgn3Xt5iDooQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH3qtbS68UnAdg71u/7zK57lccWQLMeRa8CQg43KeYW3AiEApL/aOvz2nSawqSxExZ5i2+YIQdGC9TPXV+VySsHitHw="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}], "directories": {}}, "0.3.2": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.3.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.4.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.3.2", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "6e85116ca77702f3aa78cc3fa0a8c1b02b197635", "tarball": "https://registry.npmjs.org/union/-/union-0.3.2.tgz", "integrity": "sha512-Hxld6VJrNPiL8tBwQEXKhbbvKph0rRO5bxAIwNsR/44NCIe8fnJ73jKHKOw85LPqbKRxarHb3DwYvPteHCp4og==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPY0ykdoLkfOzVFEHHcUjgVyyNYjjSSx6us4Bbop1AegIhAILKn9heeAiOXQ8wxCYuFaB9iIB4fGLReB1YxgwUC65z"}]}, "directories": {}}, "0.3.3": {"name": "union", "description": "<img src=\"https://github.com/flatiron/union/raw/master/union.png\" />", "version": "0.3.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.4.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x", "connect": "2.3.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_id": "union@0.3.3", "dist": {"shasum": "095e973096180a8c5368cbb4be274e2b21462507", "tarball": "https://registry.npmjs.org/union/-/union-0.3.3.tgz", "integrity": "sha512-DbmDm10JJSNYd3QjW7V9XS9tLp/IaOt6KhAqtuKQptq0WBN1bgjVhA24tIe/fF/tFU6pqqTjsX8r/hnEljsIzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqCGZMzrscTIlTP9YpaxTiizAqWQjXx/iVEPMsPCAnugIhAI+i9ybn70yjhOKS7NGD3oLL44Pn3yJc8/ue2qp7rKgt"}]}, "directories": {}}, "0.3.4": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.3.4", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.5.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x", "connect": "2.3.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "union@0.3.4", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "ff9d40939cda4f8f2556d194d01fc7ac2cdc94b7", "tarball": "https://registry.npmjs.org/union/-/union-0.3.4.tgz", "integrity": "sha512-IPFzr9VPwY4PdY63whSVNIfHnGKfmG4aj/a9BKbzKdAg0Nq6qNyRTnsWcWkydZ+pHm+4Eu2CfADf1Cd9T2oigw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF+36c7cblgD3UUdLYq+huxz/pR1Djgu77rnrj7dhKGtAiBYUq9+7bmHoFXoDomYj+mbRP/uGB0eJiMlaBvbUPm0Vg=="}]}, "directories": {}}, "0.3.5": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.3.5", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.5.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x", "connect": "2.3.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_id": "union@0.3.5", "dist": {"shasum": "e46c7fa99ed1ada90436fdc0247f3aca573e4f5b", "tarball": "https://registry.npmjs.org/union/-/union-0.3.5.tgz", "integrity": "sha512-8/liH/vpbVl3h9R4L5pGhVwqKh4ekURnFFagb4kqjbpg8uRmr47o2b+zDpMN6cJy5I57SAAyZrBjMQfWZUzfwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn0n4HcwHF0ievHpbV5buRxkL+pb7coxbc8w7TnVWPmAIhAJycgikRSAJlHHfuXwHHvGcA769qN8Uy91Lz6UKQHkzh"}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}}, "0.3.6": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.3.6", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.5.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x", "connect": "2.3.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_id": "union@0.3.6", "dist": {"shasum": "e054a5a0de9ca0238268e785811ee3b4b2452d09", "tarball": "https://registry.npmjs.org/union/-/union-0.3.6.tgz", "integrity": "sha512-zwuATth+apg/jxyAcQNnz+WMdUBoavucxAGuT4xgADvmRFL73Ji2T8jHLrMm/TJGMKpUfN54a3dsL7wWfIlVGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBgwzHL+A1BGR3+k9fXjncIIJ/g8X2y0lOigTlVXXBh0AiEAz0AeZnKQ7rLgmnpqoOMUj+OsypFXDJBSr2t+Uvyo5dY="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}}, "0.3.7": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.3.7", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.5.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.6.x", "connect": "2.3.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "_id": "union@0.3.7", "dist": {"shasum": "201a00523ef47932213f8fbe43e3570bc07c45ed", "tarball": "https://registry.npmjs.org/union/-/union-0.3.7.tgz", "integrity": "sha512-H3f6qXvqJjrsxLJsPTUtSObtSQeFD06Q61KI/gxxCqHQ82t9AfX6BbhOJYxY2cWRokuu8ByWtf2aZR8YN+gwkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7J6X7IZpZpVoOdFyvKT08oiv/nuvgXR/gk3DL/wmBwAiEA1DYSikdyjUxYqd7mkTtOWg4o9lyFMspyC+ooev1eL6o="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}}, "0.3.8": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.3.8", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.5.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.7.x", "connect": "2.3.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union", "_id": "union@0.3.8", "dist": {"shasum": "26a702a4d3528b4358c9711c8abff6cc91d24257", "tarball": "https://registry.npmjs.org/union/-/union-0.3.8.tgz", "integrity": "sha512-0rE7P7EEOPZabwOGltjAFItQKHFx8pYfkamn3Se7KVdufcuwq6HwvVyVRqAVT/bfhwvJdb8Fs8HoXaL+IHCiWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHBwn+geqcvZAHKAzXrpHrXNAoU5WXv0Uq7YeS3pw2ADAiEAghSYEa6c6zL9JtjPq3+laxxGBGfeJwkxI3JcOqR40ts="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}}, "0.4.0": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.4.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.5.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.7.x", "connect": "2.13.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union", "_id": "union@0.4.0", "dist": {"shasum": "1ed141a7a3f9b3e35385cfe112b0636d89d4a412", "tarball": "https://registry.npmjs.org/union/-/union-0.4.0.tgz", "integrity": "sha512-LKkFEjFZHuf3ntJogJjJoJshV8p4EFIVQLD1tmD2LSAiovTQmN9g2nKDq8pTLfu9u8MZa8qYXw9/Vqmowdk0mA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFkIMh8OIYdE4SfqRtkU1Fs9j15jYESbIDXLOb2Eeln3AiEAwlgERi14nQHPahkv4RuO7yHJ1UyMuqcyvWiQLOIkRjE="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "swa<PERSON>e", "email": "<EMAIL>"}, "directories": {}}, "0.4.1": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.4.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.2.x", "qs": "0.5.x"}, "devDependencies": {"ecstatic": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-ecstatic.git", "director": "1.x.x", "request": "2.x.x", "vows": "0.7.x", "connect": "2.13.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "gitHead": "6fcf78269b02d15a8bb8bd26ec9ca51ec7b21fd1", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union", "_id": "union@0.4.1", "_shasum": "cb59a21d9342db71bf257a82ea82cf516daa6b5d", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "cb59a21d9342db71bf257a82ea82cf516daa6b5d", "tarball": "https://registry.npmjs.org/union/-/union-0.4.1.tgz", "integrity": "sha512-IM01fI0N5puEfLY4km7sbJKaUHsBnLWmDmJUy35vKTNF5hAqMRoq4/Dfo8dbyVmTR6sDAbLozx6vCNblKai2kQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDx1l2YA0ha1UpZ1DFmpObLWaRsrKXEiCY8EEvBJBPnVgIgMfPt+D/cPMgwUVLNEWm7tZuNoryKjQxiVUNqAXKZ9Kw="}]}, "directories": {}}, "0.4.2": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.4.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.3.x", "qs": "0.6.x"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.x.x", "vows": "0.7.x", "connect": "2.22.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union", "_id": "union@0.4.2", "_shasum": "26aa9def97e962cb209c7e27897595c3162c47ae", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "swa<PERSON>e", "email": "<EMAIL>"}, "dist": {"shasum": "26aa9def97e962cb209c7e27897595c3162c47ae", "tarball": "https://registry.npmjs.org/union/-/union-0.4.2.tgz", "integrity": "sha512-xEs+oBaQIydKa/QkYjRQisquBbHqoVpk8MGLKAH4+SwpzpPdIL2ZkUHcUByJ2Fc8Pecu8PjuAxmQjIY+fmPr1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDt90HF+PSN9VzABdrF3Pyp4q84LByUJxydRsi78BWzAIhAMjhaRWiSo1NFuXSi4fxeD4gyYhuqToFj6QjBja27foG"}]}, "directories": {}}, "0.4.3": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.4.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"pkginfo": "0.3.x", "qs": "1.2.x"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.x.x", "vows": "0.7.x", "connect": "2.22.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.4.0"}, "gitHead": "6cef9077861e3c4175fbf18469664c611eba2911", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union", "_id": "union@0.4.3", "_shasum": "bc625b65dfbb6a8ce53b0d6a7f8b999eb62a6067", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "bc625b65dfbb6a8ce53b0d6a7f8b999eb62a6067", "tarball": "https://registry.npmjs.org/union/-/union-0.4.3.tgz", "integrity": "sha512-ouU+VtmNhANBFsUDPg9Y+xeeDAKIzeOdd0xe9WohChuhcpWFXm4w/f9QMfxiGx1EFj4br8ocIkossAowcJiypg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBq4CwZEWnMr32EXyMz9qx4rjUVtMqYX3h+MqcflpIlJAiEArSE3bbe9PVhH/Wko5fq3icWciJ647FE7X4gC1XJ+H4o="}]}, "directories": {}}, "0.4.4": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"qs": "~2.3.3"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.29.x", "vows": "0.8.x", "connect": "2.22.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.8.0"}, "gitHead": "091b39a84f2f9a972ea1e15cf5922c99450c325d", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union", "_id": "union@0.4.4", "_shasum": "d73f49321d6ffb721a4c69ff5b0a6c31a98ff3e0", "_from": ".", "_npmVersion": "2.1.9", "_nodeVersion": "0.10.33", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "dist": {"shasum": "d73f49321d6ffb721a4c69ff5b0a6c31a98ff3e0", "tarball": "https://registry.npmjs.org/union/-/union-0.4.4.tgz", "integrity": "sha512-KlBBP98O5ymgbvCVZLUAwoLFKDHGTmePr7hrq/IuG9Ms0psspqL+xWg9cechOde8+akhKCgyNZD3KDKgpee8Qg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEOxIHUGlis79FRSmscpBXNTonvT2rQUekuulY1xHZLpAiEAlzQgzlds6QNIXvxw0I/zf5axl36dSh3hY9yrbMIjB1E="}]}, "directories": {}}, "0.4.5": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.4.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/union.git"}, "dependencies": {"qs": "~2.3.3"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.29.x", "vows": "0.8.0", "connect": "2.22.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.8.0"}, "gitHead": "905d2972a8848decb454df093a562b285f1611eb", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union#readme", "_id": "union@0.4.5", "_shasum": "29e01c98bf59f71e27b61150b7de9b9e3a00e938", "_from": ".", "_npmVersion": "3.9.6", "_nodeVersion": "4.4.3", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "29e01c98bf59f71e27b61150b7de9b9e3a00e938", "tarball": "https://registry.npmjs.org/union/-/union-0.4.5.tgz", "integrity": "sha512-sg2Y7G1jM8LRAI1mNP21Ap7XQG5DxGT2MOkBzHiUZY5ssXB7cDqtEgVR/rfcFCeC6nyr4SH+se7lxGNziuydLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDw4KkY/RRLMGvgKKWurPNYTB40CbhoHqiADzItjX5tcgIgLcOIDknNyK90RmCYHMOozaPRCZ2g90fsqW6K04JBnE0="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/union-0.4.5.tgz_1477161641194_0.785191223025322"}, "directories": {}}, "0.4.6": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.4.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dscape", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/union.git"}, "dependencies": {"qs": "~2.3.3"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.29.x", "vows": "0.8.0", "connect": "2.22.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.8.0"}, "gitHead": "0dfda9acdec355997b09c4b85e2906cb4b46014f", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union#readme", "_id": "union@0.4.6", "_shasum": "198fbdaeba254e788b0efcb630bc11f24a2959e0", "_from": ".", "_npmVersion": "3.9.6", "_nodeVersion": "4.4.3", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "198fbdaeba254e788b0efcb630bc11f24a2959e0", "tarball": "https://registry.npmjs.org/union/-/union-0.4.6.tgz", "integrity": "sha512-2qtrvSgD0GKotLRCNYkIMUOzoaHjXKCtbAP0kc5Po6D+RWTBb+BxlcHlHCYcf+Y+YM7eQicPgAg9mnWQvtoFVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEOuufcQlXLACbGiHfhjl6eGoR+JaDXZ5ENILzLE7a3uAiB8IW59+2RPKvzanKcpl/j/33UJ81NFbUtHKyie3SDhsw=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/union-0.4.6.tgz_1477161955979_0.7554563113953918"}, "directories": {}}, "0.5.0": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "dscape"}, {"email": "<EMAIL>", "name": "indexzero"}, {"email": "<EMAIL>", "name": "jcrugzz"}, {"email": "<EMAIL>", "name": "swa<PERSON>e"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/union.git"}, "dependencies": {"qs": "^6.4.0"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.29.x", "vows": "0.8.0", "connect": "2.22.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.8.0"}, "gitHead": "b69c2c0eb95d48b49e72182e53c1788efa195cc0", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union#readme", "_id": "union@0.5.0", "_npmVersion": "6.2.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-N6uOhuW6zO95P3Mel2I2zMsbsanvvtgn6jVqJv4vbVcz/JN0OkL9suomjQGmWtxJQXOCqUJvquc1sMeNz/IwlA==", "shasum": "b2c11be84f60538537b846edb9ba266ba0090075", "tarball": "https://registry.npmjs.org/union/-/union-0.5.0.tgz", "fileCount": 37, "unpackedSize": 60040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBZAhCRA9TVsSAnZWagAAqWYP/2iNfbjpBMZpCmwoun/Y\nhdtSdy+f+aVawGK6DUgOLqs7UVTKvFJpLU0g0ze4A13xjWi/pAWm6049kQ8c\nXiJg56WbOR7vTgPezxy9/kFliGnoWzWMF3csENo5i0UiIteqqgTefS6g/fMj\nFHiZLREgCUFVF6ybK6w/4kk7dptvOrxSk394R2QD2kCDHgzGIMoYreG32FCo\neMibk0VfYYTDrd58WUBE+VJewo/pgZt6foXAtfyr2NrPf3/x2vq5JmyPJiTH\nWK55PyqvMhamL6LPJET7qtj+nOZmv73hpPu/GptGSxiocukq3uiK69OVfUyd\nYjJgGZS+nlsbCIaVUfL/Q2SVVXjSNYgHoWNmcCwnt73UtX249BiVvCXFa1vk\nfehlgx5gokrAkLGNoMycbaZaESli0wOiCNIJs6LU7emyb2k8318hjpjBU+zk\nl8vQXWeIsP2JPGGQt59cOJh5Hijv/Ww2O8Ozoj3b8BtJmR5XKdR/jms6kQYt\nXY0k6eeoVCAeVbynQ5gLKqbpSNv2a+WLvHLHPa5sNgxr65iLTc0tHlTPVT1m\nKP+hOP1O+40mTHj2X+V7fDOGt0eDOHnyXPfp69stUmHI2nsa/mk4tq6Bkwgx\nJ+12WDPfm6UEYD3ob1FvBfXW3kXVOMgUJBUUXOlx2S15viKUiFtiLvEzbwBp\nrCu5\r\n=AX6t\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBQhwJkT701vabteOuldoqMfFo5RIy1iLHlbMFca6U6gAiEA8uOLDaTbZqftgt4iJPnpWh6mVkPbUDXC0Dgr8LfxQhQ="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/union_0.5.0_1543868448466_0.6769752911678664"}, "_hasShrinkwrap": false}, "0.6.0": {"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dscape", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/union.git"}, "dependencies": {"qs": "^6.4.0"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.29.x", "vows": "0.8.0", "connect": "2.22.x"}, "scripts": {"preinstall": "npx npm-force-resolutions", "test": "vows test/*-test.js --spec -i"}, "resolutions": {"graceful-fs": "^4.2.11"}, "main": "./lib", "engines": {"node": ">= 0.8.0"}, "_id": "union@0.6.0", "gitHead": "c93f7854517f6b81f3cfdec16264b1f7883f7493", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "homepage": "https://github.com/flatiron/union#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-nRwufTjDubTeGyGYYewzwcZrX1RM/FYPYB10wGGJOs6axh4XHmMb2WIWNVD1W4h5y2dO5Mo64WYqyIsUPdHHdA==", "shasum": "dadd0580272403d4271be4404eee2f9542047ee0", "tarball": "https://registry.npmjs.org/union/-/union-0.6.0.tgz", "fileCount": 37, "unpackedSize": 60564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGXck2POFPlr2/giEIKrrtTMT0BPnsM+8PY6qVgVtDQwIgSkF8BOhCjhOi46yRQ+667GGYr8B6YQMpOSliqn80MqA="}]}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/union_0.6.0_1705555064193_0.5371241974393712"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "dscape", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "time": {"modified": "2024-01-18T05:17:44.604Z", "created": "2011-11-09T15:50:49.252Z", "0.1.0": "2011-11-09T15:50:50.759Z", "0.1.2": "2011-11-18T05:24:11.477Z", "0.1.3": "2011-11-23T02:10:32.053Z", "0.1.4": "2011-12-02T09:47:52.736Z", "0.1.5": "2011-12-06T08:43:04.185Z", "0.1.6": "2011-12-09T10:51:04.063Z", "0.1.7": "2011-12-20T10:49:02.227Z", "0.1.8": "2012-03-15T19:06:56.992Z", "0.2.0": "2012-03-20T11:15:38.681Z", "0.2.1": "2012-03-20T17:35:29.973Z", "0.3.0": "2012-04-03T09:18:31.872Z", "0.3.2": "2012-05-27T21:59:46.525Z", "0.3.3": "2012-07-02T09:35:37.658Z", "0.3.4": "2012-07-24T20:15:22.322Z", "0.3.5": "2012-11-12T17:53:45.863Z", "0.3.6": "2012-11-29T11:43:46.996Z", "0.3.7": "2013-03-04T10:37:09.887Z", "0.3.8": "2013-11-20T11:09:55.386Z", "0.4.0": "2014-02-28T18:31:03.482Z", "0.4.1": "2014-07-02T15:50:49.631Z", "0.4.2": "2014-07-09T08:09:01.625Z", "0.4.3": "2014-09-15T19:58:41.886Z", "0.4.4": "2014-12-07T22:59:09.255Z", "0.4.5": "2016-10-22T18:40:42.557Z", "0.4.6": "2016-10-22T18:45:58.221Z", "0.5.0": "2018-12-03T20:20:48.632Z", "0.6.0": "2024-01-18T05:17:44.397Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/flatiron/union.git"}, "users": {"wojohowitz": true, "blakmatrix": true, "fgribreau": true, "eins78": true, "tcrowe": true, "dgarlitt": true, "namgil.ko": true, "daizch": true}, "homepage": "https://github.com/flatiron/union#readme", "bugs": {"url": "https://github.com/flatiron/union/issues"}, "readme": "\n<img src=\"https://github.com/flatiron/union/raw/master/union.png\" />\n\n# Synopsis\nA hybrid streaming middleware kernel backwards compatible with connect.\n\n# Motivation\nThe advantage to streaming middlewares is that they do not require buffering the entire stream in order to execute their function.\n\n# Status\n\n[![Build Status](https://secure.travis-ci.org/flatiron/union.png)](http://travis-ci.org/flatiron/union)\n\n# Installation\nThere are a few ways to use `union`. Install the library using npm. You can add it to your `package.json` file as a dependancy\n\n```bash\n  $ [sudo] npm install union\n```\n\n## Usage\nUnion's request handling is [connect](https://github.com/senchalabs/connect)-compatible, meaning that all existing connect middlewares should work out-of-the-box with union.\n\n**(Union 0.3.x is compatible with connect >= 2.1.0)**\n\nIn addition, the response object passed to middlewares listens for a \"next\" event, which is equivalent to calling `next()`. Flatiron middlewares are written in this manner, meaning they are not reverse-compatible with connect.\n\n### A simple case\n\n``` js\nvar fs = require('fs'),\n    union = require('../lib'),\n    director = require('director');\n\nvar router = new director.http.Router();\n\nvar server = union.createServer({\n  before: [\n    function (req, res) {\n      var found = router.dispatch(req, res);\n      if (!found) {\n        res.emit('next');\n      }\n    }\n  ]\n});\n\nrouter.get(/foo/, function () {\n  this.res.writeHead(200, { 'Content-Type': 'text/plain' })\n  this.res.end('hello world\\n');\n});\n\nrouter.post(/foo/, { stream: true }, function () {\n  var req = this.req,\n      res = this.res,\n      writeStream;\n\n  writeStream = fs.createWriteStream(Date.now() + '-foo.txt');\n  req.pipe(writeStream);\n\n  writeStream.on('close', function () {\n    res.writeHead(200, { 'Content-Type': 'text/plain' });\n    res.end('wrote to a stream!');\n  });\n});\n\nserver.listen(9090);\nconsole.log('union with director running on 9090');\n```\n\nTo demonstrate the code, we use [director](https://github.com/flatiron/director). A light-weight, Client AND Server side URL-Router for Node.js and Single Page Apps!\n\n### A case with connect\n\nCode based on connect\n\n```js\nvar connect = require('connect')\n  , http = require('http');\n\nvar app = connect()\n  .use(connect.favicon())\n  .use(connect.logger('dev'))\n  .use(connect.static('public'))\n  .use(connect.directory('public'))\n  .use(connect.cookieParser('my secret here'))\n  .use(connect.session())\n  .use(function (req, res) {\n    res.end('Hello from Connect!\\n');\n  });\n\nhttp.createServer(app).listen(3000);\n```\n\nCode based on union\n\n```js\nvar connect = require('connect')\n  , union = require('union');\n\nvar server = union.createServer({\n  buffer: false,\n  before: [\n    connect.favicon(),\n    connect.logger('dev'),\n    connect.static('public'),\n    connect.directory('public'),\n    connect.cookieParser('my secret here'),\n    connect.session(),\n    function (req, res) {\n      res.end('Hello from Connect!\\n');\n    },\n  ]\n}).listen(3000);\n```\n\n### SPDY enabled server example\n\n# API\n\n## union Static Members\n\n### createServer(options)\nThe `options` object is required. Options include:\n\nSpecification\n\n```\n  function createServer(options)\n\n  @param options {Object}\n  An object literal that represents the configuration for the server.\n\n    @option before {Array}\n    The `before` value is an array of middlewares, which are used to route and serve incoming\n    requests. For instance, in the example, `favicon` is a middleware which handles requests\n    for `/favicon.ico`.\n\n    @option after {Array}\n    The `after` value is an array of functions that return stream filters,\n    which are applied after the request handlers in `options.before`.\n    Stream filters inherit from `union.ResponseStream`, which implements the\n    Node.js core streams api with a bunch of other goodies.\n\n    @option limit {Object}\n    (optional) A value, passed to internal instantiations of `union.BufferedStream`.\n\n    @option https {Object}\n    (optional) A value that specifies the certificate and key necessary to create an instance of\n    `https.Server`.\n\n    @option spdy {Object}\n    (optional) A value that specifies the certificate and key necessary to create an instance of\n    `spdy.Server`.\n\n    @option headers {Object}\n    (optional) An object representing a set of headers to set in every outgoing response\n```\n\nExample\n\n```js\nvar server = union.createServer({\n  before: [\n    favicon('./favicon.png'),\n    function (req, res) {\n      var found = router.dispatch(req, res);\n      if (!found) {\n        res.emit('next');\n      }\n    }\n  ]\n});\n```\n\nAn example of the `https` or `spdy` option.\n\n``` js\n{\n  cert: 'path/to/cert.pem',\n  key: 'path/to/key.pem',\n  ca: 'path/to/ca.pem'\n}\n```\n\nAn example of the `headers` option.\n\n``` js\n{\n  'x-powered-by': 'your-sweet-application v10.9.8'\n}\n```\n\n## Error Handling\nError handler is similiar to middlware but takes an extra argument for error at the beginning.\n\n```js\nvar handle = function (err, req, res) {\n  res.statusCode = err.status;\n  res.end(req.headers);\n};\n\nvar server = union.createServer({\n  onError: handle,\n  before: [\n    favicon('./favicon.png'),\n    function (req, res) {\n      var found = router.dispatch(req, res);\n      if (!found) {\n        res.emit('next');\n      }\n    }\n  ]\n});\n```\n\n## BufferedStream Constructor\nThis constructor inherits from `Stream` and can buffer data up to `limit` bytes. It also implements `pause` and `resume` methods.\n\nSpecification\n\n```\n  function BufferedStream(limit)\n\n  @param limit {Number}\n  the limit for which the stream can be buffered\n```\n\nExample\n\n```js\nvar bs = union.BufferedStream(n);\n```\n\n## HttpStream Constructor\nThis constructor inherits from `union.BufferedStream` and returns a stream with these extra properties:\n\nSpecification\n\n```\n  function HttpStream()\n```\n\nExample\n\n```js\nvar hs = union.HttpStream();\n```\n\n## HttpStream Instance Members\n\n### url\nThe url from the request.\n\nExample\n\n```js\nhttpStream.url = '';\n```\n\n### headers\nThe HTTP headers associated with the stream.\n\nExample\n\n```js\nhttpStream.headers = '';\n```\n\n### method\nThe HTTP method (\"GET\", \"POST\", etc).\n\nExample\n\n```js\nhttpStream.method = 'POST';\n```\n\n### query\nThe querystring associated with the stream (if applicable).\n\nExample\n\n```js\nhttpStream.query = '';\n```\n\n## ResponseStream Constructor\nThis constructor inherits from `union.HttpStream`, and is additionally writeable. Union supplies this constructor as a basic response stream middleware from which to inherit.\n\nSpecification\n\n```\n  function ResponseStream()\n```\n\nExample\n\n```js\nvar rs = union.ResponseStream();\n```\n\n# Tests\n\nAll tests are written with [vows][0] and should be run with [npm][1]:\n\n``` bash\n  $ npm test\n```\n\n# Licence\n\n(The MIT License)\n\nCopyright (c) 2010-2012 Charlie Robbins & the Contributors\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n[0]: http://vowsjs.org\n[1]: http://npmjs.org\n", "readmeFilename": "README.md"}