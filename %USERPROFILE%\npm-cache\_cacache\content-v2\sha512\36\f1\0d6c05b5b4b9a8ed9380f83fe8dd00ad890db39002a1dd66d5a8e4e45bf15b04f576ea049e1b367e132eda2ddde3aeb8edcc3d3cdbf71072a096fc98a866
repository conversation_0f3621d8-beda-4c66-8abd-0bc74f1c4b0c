{"_id": "get-intrinsic", "_rev": "16-f4cfde6a2878e1e6fdb52993dea2b0f1", "name": "get-intrinsic", "dist-tags": {"latest": "1.3.0"}, "versions": {"1.0.0": {"name": "get-intrinsic", "version": "1.0.0", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "035ccf14a00ae2eb3d110a00fcd10e74706a8fe7", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.0.0.tgz", "fileCount": 11, "integrity": "sha512-EMuu0ud8uAP4Zs6tQqMeHiY1PbIBDcZ92QVxqeLfqTMbyvqcDbrtHjfu0RWh8QaUNJ3lP1DSX3J2okgj9JE47g==", "signatures": [{"sig": "MEQCIEehDACke//ohQCAy5pJo/R/9J5UGrufkNBiQJqe3y2DAiBC7txNPrBmQB4PjK/Ydow1627eRDEIl0wz1IkhWkBAkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnCtMCRA9TVsSAnZWagAAwm0P/0h8E1pcOVKKP6XQ6No4\n9tdWqfwRUlG8RTYs8sXW8g2qL3PxQdM1ql5GztOTUSstrtEE2sux290V6w1B\n829I8YHJbw667RuqIOuUBnXjaFm3Eb6S1Tvhvlbff0MtEoP9dZwgvqHn6yLx\niIBIRDCEJhuqrfVmjbpy6hLDEsxhaWsSxPj81gm+aHY6xVb4f/dZvrDp8R9j\nlaEwsE7EK+cEn3ifTQYYHlv8an9QkPFTHDLjeZ+wdWBnut+tepMeFM+ZjG+d\ngdTg2IeNfXFw/QSU5eDQtjqHZ2Fv2T4fFn2blhkrIbEMmwxczzM6QuQiOGc8\n1suIs9vDdt8qq6h8ESs9hr5I2hgE3M4Xxt5ziZ95TifSDRNyyQGbMy5vj3CY\n0z2e5M6zr5b2mkiWm0A5tZI4Mdy/2XrpJxTE6/opYgvA5mQ0GIYzO7r1Zt+G\nmHD/MDeTe2WxBWizo3nv0IGRvZeHZ/JjcRHdHeRAq+rqJ6o4hvYanxfoGlGA\njCUXYsZzR2XLfxBiTeSUO9VQ5YSBtsfU+egeRNwOw5PwxpGwfW4VUVOPHwHJ\n5dHlRGuWHDOn+4uF+09o5B70By6rcGZsHV62jX5ci5JclHswBdrvcftucfyG\nyR2qyuEnxq7O+S2D/uMylQLqTdCdJ6Bf58TKGSzpsp45oWrSmIsSTdiVWIsG\nz7pB\r\n=DQ3f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "516f403fe75287a2a80a8d48c2061f6b3238ec0c", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "nyc npm run tests-only", "posttest": "aud --production", "tests-only": "tape 'test/*'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.2", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.12.1", "foreach": "^2.0.5", "es-abstract": "^1.18.0-next.1", "has-bigints": "^1.0.0", "auto-changelog": "^2.2.1", "object-inspect": "^1.8.0", "make-async-function": "^1.0.0", "@ljharb/eslint-config": "^17.2.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.0.0_1604070219549_0.3039159077605891", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "get-intrinsic", "version": "1.0.1", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "94a9768fcbdd0595a1c9273aacf4c89d075631be", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.0.1.tgz", "fileCount": 12, "integrity": "sha512-ZnWP+AmS1VUaLgTRy47+zKtjTxz+0xMpx3I52i+aalBK1QP19ggLF3Db89KJX7kjfOfP2eoa01qc++GwPgufPg==", "signatures": [{"sig": "MEUCIFzb1R9CMnRu3GNwT893R3yms0wnrxROjmNn7s1aWEdLAiEAgP3VkUFew7/H+j05N3mW3XntPRU+Smw2z+q8kZl3CC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnPbUCRA9TVsSAnZWagAAO2UP/37xSp1p1f50Pt6yyL4k\n1BKcgA+OfPCEhFnJA1AKqYeL8rVBLr7VoSvMzTQ9JonFIIXLlnlVe8P91KWE\n1AXoYJr/dW8ZG7vHs37jK7aiEweyYlgLebWPOM2T2bU0WFoaaIws1fa+TwTS\neCqY8Q7XysXV3syWXX1El/2TIXzSVa8g8gOVJy/j8j+fthSAPD0H6ZTCvYQ6\nPSWIFAYhRIWXLGel3T/TE1p61AWZuEtf8B+e6K8hPiMuzhNjODCBqJQV246D\nPznhAbJV81wNIdM0ohuT19+t7GqjjKbKKMpU0LZzSCjZF3Q+zLI4H+qMY0Bl\nHFiqspAfS0r/wHWoBkzODoHWMduJ/JPtE/uee8ae92iC9fR9Y8fSOWXTt07W\nFWSGyLyJ6CQS7d+dJwFb+2cQNckV/9VKu+y58z+i6x6/FExmHNBdYt4ps3ju\nH89DQEmfq5wyLcceng9K0a7A6vfLM6MvEk8FugXVhGORioFOkscE3f8gS7Sc\nIzbl739iiG3oGvNzRgF229t2xwUZXVNqGJ4Sg3AQM/RX75+Mu1Jlx52z0ECY\nLZGX16A+J3N955DxJktRA1l7RA+zihIs1fZKHm+fErP547biV5p+TNocKrrn\nwghypBweNbcNkzNds6qczoB/3Vsc2OxHaVUBWNleqmlrQ6Qk1AA4ZRIdhQZ8\njWu7\r\n=BhVO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1802957d1ff6a04965505f54c3d354ad7fa31034", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "nyc tape 'test/*'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"has": "^1.0.3", "has-symbols": "^1.0.1", "function-bind": "^1.1.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.2", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.12.1", "foreach": "^2.0.5", "es-abstract": "^1.18.0-next.1", "has-bigints": "^1.0.0", "auto-changelog": "^2.2.1", "object-inspect": "^1.8.0", "es-value-fixtures": "^1.0.0", "make-async-function": "^1.0.0", "@ljharb/eslint-config": "^17.2.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.0.1_1604122323843_0.022947285149020447", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "get-intrinsic", "version": "1.0.2", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "6820da226e50b24894e08859469dc68361545d49", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.0.2.tgz", "fileCount": 13, "integrity": "sha512-aeX0vrFm21ILl3+JpFFRNe9aUvp6VFZb2/CTbgLb8j75kOhvoNYjt9d8KA/tJG4gSo8nzEDedRl0h7vDmBYRVg==", "signatures": [{"sig": "MEYCIQDLO/WsMu1yogrwHBInw7hC3MUpX9G+E/a0CiaubHBZGAIhAOSWmWgEOV39qmGn7YkvbHgzc+IxhjCPiTch0LAig1NA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3FiZCRA9TVsSAnZWagAAhqwP/ApVcuwN67ClrOqU4sXI\nq1LZjlVXwkM54mRbfqChOsZUZsxW1V8xCpeaaZE0h2JKH+PnzS/GUvAcd0iA\nXbyjsIfvc66lu0bBIMKrP/zLMQ7LIm3q9Vr7iLIWi7LXXCGNqhtNx0rGgPVi\npk6c0o6MUK6Tr1RGtdpQZVnJqF9veFC6RVApu+xFYt8QWXuYDTGKrS093aRU\noI3SmbrjLSlskjXSVREGFja/L5JsiHbds7meSHPWdF57AhatrEb9X8h93fdy\n4Pz1yUKjd1QFXoAg4Pw+TLRPO0VN4JYeHWwaQ+mmOl5RViz+Yiq6joR+Fo1r\nsdSWHdijgx3XzGH4nbiP9mjR/TcypqZQeEP1H5TDZfDSSRSg9Eus0BQuHwOa\n9kNLDQywTsBBsB8S5tlJ4QSrTSn6Y8q5RsQIl9IIHwAUW/0GyiAUfCJMYCiE\n9A13GnS6ZPJEdJu960P7ZlbvnfpPbiQaMOMyC6kXOfACBkcxhhc4SofQkMZw\n5v7Xjg3Nz6inEnpbXuuU3Tj3WmDMMWoyX06sDbv50X/gzciNSy6ptcJgultt\n8aGrP+i/QWfHzGdguIVlz+2wf5kYG1jRyvbKsVZKJ2wvnnxUC8Ji0yjFO6j/\nKuwcsva60yDyaAjpy4Sbw7WSE1etufVa0rXf96788xqPhAhvFcGzzVKiOigr\nUP2R\r\n=SifI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "eec980691af2fafb4e0d9207e473c9e1eb7995e6", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "nyc tape 'test/*'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"has": "^1.0.3", "has-symbols": "^1.0.1", "function-bind": "^1.1.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.15.0", "foreach": "^2.0.5", "es-abstract": "^1.18.0-next.1", "has-bigints": "^1.0.1", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "es-value-fixtures": "^1.0.0", "make-async-function": "^1.0.0", "@ljharb/eslint-config": "^17.3.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.0.2_1608276120877_0.84071357918607", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "get-intrinsic", "version": "1.1.0", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "892e62931e6938c8a23ea5aaebcfb67bd97da97e", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-M11rgtQp5GZMZzDL7jLTNxbDfurpzuau5uqRWDPvlHjfvg3TdScAZo96GLvhMjImrmR8uAt0FS2RLoMrfWGKlg==", "signatures": [{"sig": "MEQCIA+e/mSh+QgkqBLYqQTWcVvq5FQ05WbdMfaLjOMgM3N5AiB29JZnuXxh8SsBV17yu9nizQr5iwkWwK5HPPpe9Dx8Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgD0SqCRA9TVsSAnZWagAAVjQP/276R/hJLC3r7gUvPhUj\n8mwJCVpdCjIzaf4fBVvu0mB4cXe4G+t+N1w3JG9wkBqTpoHjNRzUUxhcFYeX\nnos9b4CeNftDFVgwixFHcRS0Nk0A6SUSj7jdmLiyrM3Lc0KVrMfe7G7ECeSV\nKGWViXtP8oEZJ6FZURMS9yMraQzeh5ChjcGKXsX0Jf0IpUXlDaib0ElChkLr\nN6iXsGveM9tYf15JjBW/gyJXhMPQLGE37jdCBkoW6WeOT7twWr0KDcmn6QHg\n775CZxPl1VJpEiXIoSk0PnAxRN95MIRZvdQ9k1ctSuE5kpErrTZk7j7i4i2T\n5bbOOcLvxX+StCvNtOh7M52RyDxPaagFSoKaNHxmW4e2muDDuvWRPA3n/FI4\nuXw1J1Lb1lvbhx/L9wLNN9SdPFcFOA2+t23SJE/F8abLHNsdhoBlCyoCmULL\nIKdrVXWxFbopnQF3n18ajCIDJ9E4J1vr6XU7+xYc7Pl1Nuel9AfQU5PuLAFy\nj0ziiUntUOuYWC0xHuhnYVHDWmU+1UB5IoxlQi9uAYp0/RBWg4mmAcQ2dK9B\nJDxOaa/Rmkp1F/5htSqD6hvfAH8Pv/SpEglGRUPH4mmHF183iLEwls2GfOAJ\nh2Baw9u2yX7COfPYqDG2MVbwB6wafDPgUKNNCZ+FjMRgLCt9VrzwVjPu9QF/\n5V7l\r\n=QaDr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "aaaaa0d5cd17d4b0b274cdaa1f7f3e6007fc9e59", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"has": "^1.0.3", "has-symbols": "^1.0.1", "function-bind": "^1.1.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.1.1", "eslint": "^7.18.0", "foreach": "^2.0.5", "call-bind": "^1.0.2", "es-abstract": "^1.18.0-next.2", "has-bigints": "^1.0.1", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "es-value-fixtures": "^1.0.0", "make-async-function": "^1.0.0", "@ljharb/eslint-config": "^17.5.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.1.0_1611613354056_0.8648044903277086", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "get-intrinsic", "version": "1.1.1", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.1.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "15f59f376f855c446963948f0d24cd3637b4abc6", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q==", "signatures": [{"sig": "MEUCIQCNo13JEcIXzNTEsjtVSMSsBL9CAqU56ZzTh56ilFAwmgIgCFy2IWS7fXyDYWF1/aSqiRCTW9wVIONaN0YUk7J0diM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGroRCRA9TVsSAnZWagAAAaMP/1kYGifz/BvcSYhnlVk+\nSnCwbyuOaTefaIpH15yyWb9sjo+1fgUw4Ej3GmVdpmyW45Tj0WePwRWhbpok\n1aKIx3P8/q8m95HymXcR50VRByFyxpNFxtWuo674yTzvYxN1+QqXVSO7xeLI\nL+bRYOScvb+f5DI8t5LqhZlvQgfiqyWXZI4L+gbwfIIrE7EUg5DZJZrzIBOY\n5SExvgueChcIptQgu8ppE5kADlGqmTHUBt3P68EU5HRc5Z/LN5csgTu63VkJ\nxx3pTXa/Q672C9qj1CqedmughzgkfBjSuKOhbQWgILCbNy0A6TKKVirpc2fB\nuI0f4vWTf1ImGrspsfIH2IR4SQqMmVy8qpgwG/YtU3q9Si9pOcXQ1q+JnyD6\nDoLaiTEVPC8ks/bKGjtNBDUmlnEuyluaaFuK3cfJQMGp2n+FNLXI5LBz9uoR\nkpqUHNJBFJ9HbbMfBUmTS3K3duAkgOR+izFQgAJJWzYbuAvM7GGAoy1eQUrY\nuD1tAQglMbB0YwsjnDxvGcV32iFoMttrcXb5xKUOlVaFMD2D9PDryeO/gu0N\nm3wDWCKhmMjGNWV6WA9q0mD6YRCPHZUwmb4xSFdz/i1MP4iVjVKc1tz6RAiT\nLqxKnm4uPjTsPPGrXWYdRs5EEF2/QHcmCex2kwk5Ul4fsVayaNOAzB3F+iSp\nbWf1\r\n=5aSN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "efa0daa5166f1a06658001e34f49b5f1185786eb", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"has": "^1.0.3", "has-symbols": "^1.0.1", "function-bind": "^1.1.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.1.1", "eslint": "^7.19.0", "evalmd": "^0.0.19", "foreach": "^2.0.5", "call-bind": "^1.0.2", "es-abstract": "^1.18.0-next.2", "has-bigints": "^1.0.1", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "es-value-fixtures": "^1.0.0", "make-async-function": "^1.0.0", "@ljharb/eslint-config": "^17.5.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.1.1_1612364304893_0.18784978138621788", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "get-intrinsic", "version": "1.1.2", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.1.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "336975123e05ad0b7ba41f152ee4aadbea6cf598", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "fileCount": 9, "integrity": "sha512-Jfm3OyCxHh9DJyc28qGk+JmfkpO41A4XkneDSujN9MDXrm4oDKdHvndhZ2dN94+ERNfkYJWDclW6k2L/ZGHjXA==", "signatures": [{"sig": "MEQCIGk690kzjXZ7zcAng4wWyvMEdoQ4xPaEtBm2SQIm48nMAiBm4P1A9nW2MVt9ngQfwiaKLc6wAZZBcdlpzlq8Br1v2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioLxOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs3A//RY5KjVotePADUXDnTk/obYp33AmFt7aXnVRafoQNIhvDC7Ya\r\nMj9g+8NGAG2D1xBgD+Q/dhvvfZQlpuLRzkfQg4V92liFmgpoEB0ue6BP0TD8\r\n37S9yioBWG6LTJkqbvjc68V3gi3t5jWTqHJeYPW4mxJF6MMCx7m9EYWGtqUR\r\n0AnVNqH9j4SJ/X3qHach0vgsI8hnb8iXeTg8X7465MmQke+tygQbT3rYLN/L\r\nSni+uwm6EMybJ8Lh5GKq5U6aKr+inAYm/h47js4D7/A+tvfzYfWvLjr1l4J5\r\n+cMKLskFEP6g/Xz9jaYCCRxe7YGaiTmH/sUgT+kTzo2oJaYh6xd/6bgvGCut\r\nPFBBxh0lknSR1wbiQz3hcdHu42D0a9jiOmtc3DlkiRzrez6pEJMDnKu+Pbck\r\nkqhrBMLYyLYkLHJzeB07aN+KuspIZgjMJ/rSsgqla8JHv6TqWx0BbaoZ53VA\r\nPzf3fs73zh7IhNLznCQVNHR9iM0w+dUMI2n6c0QlOaimFkJ+61cHA13zU20x\r\nllmejv9s5XEkvuVSU/ibuYEkbnqDg62sWcm0HgGuL6k+RKe7Mj/gOds/Zn9n\r\ngvMIl1y7zeaIrEmQHfj6ndXAB1Mv9eIySBA4//nd+oVZLibt4pkAJIIy8xY1\r\nnHBGLFN4jrtJBI6I36xrNfrC0DVumPnTh8A=\r\n=ZCcH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1692762305146cdee0bd0a31cb0a57ffd9240c8c", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "18.3.0", "dependencies": {"has": "^1.0.3", "has-symbols": "^1.0.3", "function-bind": "^1.1.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "call-bind": "^1.0.2", "npmignore": "^0.3.0", "es-abstract": "^1.20.1", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.2", "es-value-fixtures": "^1.4.1", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.1.2_1654701133878_0.7209001100988714", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "get-intrinsic", "version": "1.1.3", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.1.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "063c84329ad93e83893c7f4f243ef63ffa351385", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.3.tgz", "fileCount": 9, "integrity": "sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A==", "signatures": [{"sig": "MEUCIFx7EpcX7UchnW1MjTW4LY/IDpL1jl3H+M29ezR+WSHQAiEA7sXR/8EoSjeBOAK0Z3he//k1OtgvYgkt6hGAGtrHojM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIACSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAuA//URHhCHAEO247jjMd3BcpIpNWgbXTtnIEpHqbGulxZuzwd8hD\r\n7a9fqywLrEQq10reRxRNeS6Zk7BSv9QqwtZx7dTmi95ZxPETZvF2khJ6ggyj\r\nhAMonDjaP79Ki5Dwz/JH2WxsefDcAAPRRftEmm73oSJgt9EEdssmmAXgG5JS\r\n5OU3tCLGb4ricSaPNv2g2QDDLuLh/j6axKGn5bsQZFCvK87PV1vR/9Q6EVUz\r\nNDgWOxcgQTXgpVJYPsd6j8FiB3PiuFmd7/aLiqUMncStQDzklRHd8zUcxay3\r\n+0NplukrzPQPRDjMLuLeIX6WX+145sPZcThc7s9nrfmk2ODpDmLUYPZdki6U\r\nUBBa9aK3kDBIocvwVrleIzyY53SKvmmZ6jqmP5wS9pEWPa1gdD+VugZGazEK\r\noYK1MH77WG9fJb/2n27AWhJ/Tm9m177G+9rYQKIA+Q9JmZom+qNQviXkSkHL\r\n9MhOdjGzH0hnhX25ml81l6I2a/spKuN6RsHKNruUEUUxAyQYxIm6ZJs6D2Hy\r\nDjd+LklfZnCUsJUIJarqkB8XnRYsrKR+zrcTjxuRS0vQMBs+t/DYyXaS1k73\r\n4SR/biyt43/SOVtwZ25ThMxfGBZ+gwIqsoih3Rovs18QsrZNDyeU3fzfcTCM\r\nRioeF4ejfq26VnL5JSIEvGDWBNihLZhCw5U=\r\n=Yum9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "65cac0bca7cf7db4d1594bd1f7c68e921adedb5b", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"has": "^1.0.3", "has-symbols": "^1.0.3", "function-bind": "^1.1.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.6.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "call-bind": "^1.0.2", "npmignore": "^0.3.0", "es-abstract": "^1.20.2", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.2", "es-value-fixtures": "^1.4.2", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.1.3_1663041682205_0.23362607287463288", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "get-intrinsic", "version": "1.2.0", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "7ad1dc0535f3a2904bba075772763e5051f6d05f", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.0.tgz", "fileCount": 9, "integrity": "sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q==", "signatures": [{"sig": "MEYCIQDb29OYVbJKfex+ljyYg1fRxZiHvAcbeMgBRIcq6cP6MgIhAMPAotqdPrJxkwnAeSq+RDK//aoFWESiSJuvWBmlhUAH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjykKtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUEQ/+PfWdGnewUZa86B0H4haSxBRBlwuFGg7GpdoEhJ3Ll1A9p3Jb\r\nvU2+9RyeNp1p2LNrktOAenAcs7I3dLl0dAspHjjL3uLNhPkrlpeVqOktXs+H\r\n7l0VaAOrLZVi1f+akY117IO0OO6FwRLV42VdM7QIH2BcfXuCyDDke41rq5oS\r\nR9I+8C2SCW2/OxXcMG9nYOpW494hmHRRYh9mpovJUOpAerMUgy334rK72ArR\r\nNsgnAu4luu/7RmC5BNPS26Q7NVCVf7THdx2v3OSkgFvTrdS+wu0NhqkakppS\r\nfGTYkR1m+7vX9YLHIokoIDjHtHaNPMUb7e51OxegjtPEh7FBacfRs0bxfx7Z\r\nJLhYAbjSanGci/gfC2gT1YIPUgydWbx1Ejmol9j7QmA9BQuHSxHu+SiaRA46\r\n+F/Fzbkp1sC0gqo4qGN04Lw8+2g2DHGfBygd6vcUtnaHMz2coCF4rlvcW2fN\r\nz6tT4pcE/AWtC6l9yCWzAWDjEZjF2kBycuiY36IlhhPjtj3qiGQqnTPLL10d\r\nUWA9ZTqFH2k+o4tKhz8g1kQeBApgpRgr9FfukaNq/TZi2tguQ2MlHQ+0R0ZC\r\nZZRtnin4nEpjZ+GkAcfnm9QCrripiWwDtSgXsKvgSICOdp9urrSgfcEAuEvM\r\nrTjOosJAUVohG06+klaUIe6mIssavg3AgjU=\r\n=CxS4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "0b60d7ac9d93e8824a36ddd52635be1fc13758d1", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"has": "^1.0.3", "has-symbols": "^1.0.3", "function-bind": "^1.1.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "gopd": "^1.0.1", "tape": "^5.6.3", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "call-bind": "^1.0.2", "npmignore": "^0.3.0", "es-abstract": "^1.21.1", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.3", "es-value-fixtures": "^1.4.2", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.0_1674199725115_0.9427568240984563", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "get-intrinsic", "version": "1.2.1", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "d295644fed4505fc9cde952c37ee12b477a83d82", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz", "fileCount": 9, "integrity": "sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==", "signatures": [{"sig": "MEQCIH2PMMBY2iFUOUqP3dZ9AzuAm4akebv8JtnQ/8ytUwHcAiA3J9wBIHECkgkMjb0tz+evqI1jlknL37xpqbmFel24CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39285}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "156c781fcdbfe23d6f248cc2c9c71e01c43db198", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"has": "^1.0.3", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "function-bind": "^1.1.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "gopd": "^1.0.1", "tape": "^5.6.3", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "call-bind": "^1.0.2", "npmignore": "^0.3.0", "es-abstract": "^1.21.2", "mock-property": "^1.0.0", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.3", "es-value-fixtures": "^1.4.2", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.1_1684044853845_0.08879521215063968", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "get-intrinsic", "version": "1.2.2", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "281b7622971123e1ef4b3c90fd7539306da93f3b", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.2.tgz", "fileCount": 9, "integrity": "sha512-0gSo4ml/0j98Y3lngkFEot/zhiCeWsbYIlZ+uZOVgzLyLaUw7wxUL+nCTP0XJvJg1AXulJRI3UJi8GsbDuxdGA==", "signatures": [{"sig": "MEUCIQCE4i94ty4EPmnJBOdQY/dlaJHPTLNYDlAnwhnAdxUaxQIgCD6rj5ICp5d6HT3FPyiwbzqhuV1I/EOFyuXL/jVTTbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39896}, "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f973d4fa439027671b626ce53795541774d9af0b", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"hasown": "^2.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "function-bind": "^1.1.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "gopd": "^1.0.1", "tape": "^5.7.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "call-bind": "^1.0.5", "npmignore": "^0.3.0", "es-abstract": "^1.22.2", "mock-property": "^1.0.2", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "es-value-fixtures": "^1.4.2", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.2_1697865532155_0.5048495193780591", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "get-intrinsic", "version": "1.2.3", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "9d2d284a238e62672f556361e7d4e1a4686ae50e", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.3.tgz", "fileCount": 9, "integrity": "sha512-J<PERSON>cZczvcMVE7AUOP+X72bh8HqHBRxFdz5PDHYtNG/lE3yk9b3KZBJlwFcTyPYjg3L4RLLmZJzvjxhaZVapxFrQ==", "signatures": [{"sig": "MEUCICnWf2Lq2QF62/nFmeSSayPT1BrVwI9j/GQR1csxhW96AiEAiMJWHn0HxmQSB/w08t6sc82VjSsfsz09sutUnZmr1us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41188}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "6c71c310ec5e23da37cffd245e5b6c41de182376", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"hasown": "^2.0.0", "es-errors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "function-bind": "^1.1.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "gopd": "^1.0.1", "tape": "^5.7.4", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "call-bind": "^1.0.5", "npmignore": "^0.3.1", "es-abstract": "^1.22.3", "mock-property": "^1.0.3", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "es-value-fixtures": "^1.4.2", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.3_1706982791589_0.****************", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "get-intrinsic", "version": "1.2.4", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "e385f5a4b5227d449c3eabbad05494ef0abbeadd", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz", "fileCount": 9, "integrity": "sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==", "signatures": [{"sig": "MEUCIQDQ14DLtlzSO4A3E7LtutAY2/qA5riZBsmzWGKv6A4lWQIgMl7+Y6W7AyTLiI9Ip7qZX36ZEtREjtPlAI3GIJgT8bU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41624}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3ce1c8e1d677ae3b828f84add6e0e399d3c94a66", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"hasown": "^2.0.0", "es-errors": "^1.3.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "function-bind": "^1.1.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "gopd": "^1.0.1", "tape": "^5.7.4", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "call-bind": "^1.0.5", "npmignore": "^0.3.1", "es-abstract": "^1.22.3", "mock-property": "^1.0.3", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "es-value-fixtures": "^1.4.2", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.4_1707197526285_0.00048803680446463105", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "get-intrinsic", "version": "1.2.5", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "dfe7dd1b30761b464fe51bf4bb00ac7c37b681e7", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.5.tgz", "fileCount": 9, "integrity": "sha512-Y4+pKa7XeRUPWFNvOOYHkRYrfzW07oraURSvjDmRVOJ748OrVmeXtpE4+GCEHncjCjkTxPNRt8kEbxDhsn6VTg==", "signatures": [{"sig": "MEUCIEY/v70GaEVmYjohhysyjaTWNGrHnRFdiON0ObVRpY+2AiEAp/eQxixV9fDK7wCO3awM184aJ+VSti+sIIjHNIkaCXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44007}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "a3b2d7c605887536cbd67efa3e3c005a06704eb2", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx npm@'>= 10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "dependencies": {"gopd": "^1.2.0", "hasown": "^2.0.2", "es-errors": "^1.3.0", "has-symbols": "^1.1.0", "dunder-proto": "^1.0.0", "function-bind": "^1.1.2", "es-define-property": "^1.0.1", "call-bind-apply-helpers": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "call-bound": "^1.0.1", "es-abstract": "^1.23.5", "mock-property": "^1.1.0", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "es-value-fixtures": "^1.5.0", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.5_1733516484751_0.5002024032938608", "host": "s3://npm-registry-packages"}}, "1.2.6": {"name": "get-intrinsic", "version": "1.2.6", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "43dd3dd0e7b49b82b2dfcad10dc824bf7fc265d5", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.6.tgz", "fileCount": 9, "integrity": "sha512-qxsEs+9A+u85HhllWJJFicJfPDhRmjzoYdl64aMWW9yRIJmSyxdn8IEkuIM530/7T+lv0TIHd8L6Q/ra0tEoeA==", "signatures": [{"sig": "MEYCIQCLeGLMmVW5+xr6gypd3a0TLIbRfIGZk/PcaDxBf6U2NQIhAKZFLeVOT37F9QWqQ6fTjVrCQ4fTfQva/gC8R8Iq7Icr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45087}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "4acb3f0114a3fdb3180bccfc0769af0f931c4b23", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx npm@'>= 10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "sideEffects": false, "_nodeVersion": "23.4.0", "dependencies": {"gopd": "^1.2.0", "hasown": "^2.0.2", "es-errors": "^1.3.0", "has-symbols": "^1.1.0", "dunder-proto": "^1.0.0", "function-bind": "^1.1.2", "es-object-atoms": "^1.0.0", "math-intrinsics": "^1.0.0", "es-define-property": "^1.0.1", "call-bind-apply-helpers": "^1.0.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "call-bound": "^1.0.2", "es-abstract": "^1.23.5", "mock-property": "^1.1.0", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "es-value-fixtures": "^1.5.0", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.6_1733954479550_0.5013000635905569", "host": "s3://npm-registry-packages-npm-production"}}, "1.2.7": {"name": "get-intrinsic", "version": "1.2.7", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-intrinsic@1.2.7", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-intrinsic#readme", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "dist": {"shasum": "dcfcb33d3272e15f445d15124bc0a216189b9044", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.7.tgz", "fileCount": 9, "integrity": "sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==", "signatures": [{"sig": "MEUCIGcu79jd4MNjRYCtMef97IpSGAZW6Ajz6XxbTBzkxzPgAiEA63UOsYkJg7qDPNgoVyeT9OAWi35OQbnRbLe1g2ly0xQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45821}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "70f0c19787dd59a46a99091ec706fada15ddc28a", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx npm@'>= 10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/GetIntrinsic.js"}, "repository": {"url": "git+https://github.com/ljharb/get-intrinsic.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "directories": {}, "sideEffects": false, "_nodeVersion": "23.5.0", "dependencies": {"gopd": "^1.2.0", "hasown": "^2.0.2", "es-errors": "^1.3.0", "get-proto": "^1.0.0", "has-symbols": "^1.1.0", "function-bind": "^1.1.2", "es-object-atoms": "^1.0.0", "math-intrinsics": "^1.1.0", "es-define-property": "^1.0.1", "call-bind-apply-helpers": "^1.0.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "call-bound": "^1.0.3", "es-abstract": "^1.23.8", "mock-property": "^1.1.0", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "es-value-fixtures": "^1.5.0", "make-async-function": "^1.0.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1", "make-generator-function": "^2.0.0", "make-async-generator-function": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-intrinsic_1.2.7_1735840488016_0.8380664257897461", "host": "s3://npm-registry-packages-npm-production"}}, "1.3.0": {"name": "get-intrinsic", "version": "1.3.0", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=.js,.mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-intrinsic.git"}, "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "homepage": "https://github.com/ljharb/get-intrinsic#readme", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "call-bound": "^1.0.3", "encoding": "^0.1.13", "es-abstract": "^1.23.9", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "testling": {"files": "test/GetIntrinsic.js"}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "get-intrinsic@1.3.0", "gitHead": "9d747e0540e5e03421e7411b7946c23f4b010fd5", "_nodeVersion": "23.8.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "shasum": "743f0e3b6964a93a5491ed1bffaae054d7f98d01", "tarball": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "fileCount": 9, "unpackedSize": 46542, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEnVjmY1m1/1LGZ5OEP1oFPgly9QoANJ8qnqZZmeRqsRAiAQ12HOGi0q5aBtjTTAfhP1HpDOFjfv5KPEd2FTrrZlcA=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/get-intrinsic_1.3.0_1740257660418_0.1214735397958775"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-10-30T15:03:39.549Z", "modified": "2025-02-22T20:54:20.766Z", "1.0.0": "2020-10-30T15:03:39.692Z", "1.0.1": "2020-10-31T05:32:03.992Z", "1.0.2": "2020-12-18T07:22:01.056Z", "1.1.0": "2021-01-25T22:22:34.211Z", "1.1.1": "2021-02-03T14:58:25.007Z", "1.1.2": "2022-06-08T15:12:14.076Z", "1.1.3": "2022-09-13T04:01:22.362Z", "1.2.0": "2023-01-20T07:28:45.291Z", "1.2.1": "2023-05-14T06:14:14.066Z", "1.2.2": "2023-10-21T05:18:52.454Z", "1.2.3": "2024-02-03T17:53:11.747Z", "1.2.4": "2024-02-06T05:32:06.445Z", "1.2.5": "2024-12-06T20:21:24.984Z", "1.2.6": "2024-12-11T22:01:19.760Z", "1.2.7": "2025-01-02T17:54:48.205Z", "1.3.0": "2025-02-22T20:54:20.619Z"}, "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/get-intrinsic#readme", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-intrinsic.git"}, "description": "Get and robustly cache all JS language-level intrinsics at first require time", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# get-intrinsic <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nGet and robustly cache all JS language-level intrinsics at first require time.\n\nSee the syntax described [in the JS spec](https://tc39.es/ecma262/#sec-well-known-intrinsic-objects) for reference.\n\n## Example\n\n```js\nvar GetIntrinsic = require('get-intrinsic');\nvar assert = require('assert');\n\n// static methods\nassert.equal(GetIntrinsic('%Math.pow%'), Math.pow);\nassert.equal(Math.pow(2, 3), 8);\nassert.equal(GetIntrinsic('%Math.pow%')(2, 3), 8);\ndelete Math.pow;\nassert.equal(GetIntrinsic('%Math.pow%')(2, 3), 8);\n\n// instance methods\nvar arr = [1];\nassert.equal(GetIntrinsic('%Array.prototype.push%'), Array.prototype.push);\nassert.deepEqual(arr, [1]);\n\narr.push(2);\nassert.deepEqual(arr, [1, 2]);\n\nGetIntrinsic('%Array.prototype.push%').call(arr, 3);\nassert.deepEqual(arr, [1, 2, 3]);\n\ndelete Array.prototype.push;\nGetIntrinsic('%Array.prototype.push%').call(arr, 4);\nassert.deepEqual(arr, [1, 2, 3, 4]);\n\n// missing features\ndelete JSON.parse; // to simulate a real intrinsic that is missing in the environment\nassert.throws(() => GetIntrinsic('%JSON.parse%'));\nassert.equal(undefined, GetIntrinsic('%JSON.parse%', true));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n[package-url]: https://npmjs.org/package/get-intrinsic\n[npm-version-svg]: https://versionbadg.es/ljharb/get-intrinsic.svg\n[deps-svg]: https://david-dm.org/ljharb/get-intrinsic.svg\n[deps-url]: https://david-dm.org/ljharb/get-intrinsic\n[dev-deps-svg]: https://david-dm.org/ljharb/get-intrinsic/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/get-intrinsic#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/get-intrinsic.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/get-intrinsic.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/get-intrinsic.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=get-intrinsic\n[codecov-image]: https://codecov.io/gh/ljharb/get-intrinsic/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/get-intrinsic/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/get-intrinsic\n[actions-url]: https://github.com/ljharb/get-intrinsic/actions\n", "readmeFilename": "README.md"}