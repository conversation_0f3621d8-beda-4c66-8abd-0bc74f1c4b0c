#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的中文敏感词检测和过滤模块
"""

class ChineseSensitiveFilter:
    def __init__(self):
        """初始化敏感词过滤器"""
        # 简单的敏感词库（不分类）
        self.sensitive_words = {
            # 暴力相关
            '暴力', '杀人', '杀死', '谋杀', '自杀', '死去', '炸弹', '爆炸', '恐怖',
            
            # 色情相关
            '色情', '黄色', '裸体', '性交', '淫秽',
            
            # 赌博相关
            '赌博', '赌场', '博彩', '赌钱', '六合彩',
            
            # 毒品相关
            '毒品', '大麻', '海洛因', '冰毒', '吸毒', '贩毒',
            
            # 政治敏感
            '推翻政府', '颠覆', '政变', '造反', '叛国', '分裂国家',
            
            # 违法犯罪
            '诈骗', '传销', '洗钱', '偷税', '走私', '贿赂',
            
            # 仇恨言论
            '仇恨', '歧视', '种族主义', '血腥', '报复社会',
            
            # 虚假信息
            '假证', '假币', '伪造', '代办证件', '买卖证件',
            
            # 网络攻击
            '黑客', '病毒', '木马', '入侵', '攻击',
            
            # 粗俗词汇
            '傻逼', '草泥马', '操你', '去死', '滚蛋', '妈的', '狗屎'
        }
    
    def check_sensitive_content(self, text):
        """
        检查文本是否包含敏感词
        
        Args:
            text (str): 要检查的文本
            
        Returns:
            dict: 检查结果
                - is_safe (bool): 是否通过校验
                - message (str): 提示信息
        """
        if not text or not text.strip():
            return {
                'is_safe': False,
                'message': '问题内容为空，请输入您的问题'
            }
        
        text_clean = text.strip().lower()
        
        # 检查文本长度
        if len(text_clean) > 500:
            return {
                'is_safe': False,
                'message': '问题内容过长，请简化您的问题'
            }
        
        # 检查是否包含敏感词
        for word in self.sensitive_words:
            if word.lower() in text_clean:
                return {
                    'is_safe': False,
                    'message': '⚠️ 您的问题包含不当内容，请修改后重新提交！'
                }
        
        # 没有检测到敏感词，通过校验
        return {
            'is_safe': True,
            'message': '校验通过'
        }


# 全局过滤器实例
sensitive_filter = ChineseSensitiveFilter() 