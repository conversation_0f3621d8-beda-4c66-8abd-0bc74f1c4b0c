#!/usr/bin/env python3
"""
智能问数日志功能测试脚本
测试智能问数模块的日志记录功能
"""

import os
import sys
import json
from datetime import datetime

# 添加Backend目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from smart_data_service import SmartDataService

def test_logging_functionality():
    """测试日志功能"""
    print("🧪 开始测试智能问数日志功能...")
    print("=" * 60)
    
    # 初始化服务
    try:
        service = SmartDataService()
        print("✅ 智能问数服务初始化成功")
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        return False
    
    # 测试用例
    test_cases = [
        {
            "name": "蔬菜价格趋势查询",
            "question": "最近白菜的价格趋势如何？",
            "expected_keywords": ["趋势", "白菜"]
        },
        {
            "name": "价格对比查询", 
            "question": "哪种蔬菜最贵？",
            "expected_keywords": ["排名", "最贵"]
        },
        {
            "name": "价格分布查询",
            "question": "蔬菜价格分布情况怎么样？",
            "expected_keywords": ["分布", "价格"]
        },
        {
            "name": "无效查询测试",
            "question": "",
            "expected_keywords": ["错误", "空"]
        },
        {
            "name": "复杂查询测试",
            "question": "分析一下土豆和萝卜的价格变化趋势，并对比它们的价格差异",
            "expected_keywords": ["土豆", "萝卜", "趋势", "对比"]
        }
    ]
    
    print(f"📋 准备执行 {len(test_cases)} 个测试用例...")
    print()
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"🧪 测试用例 {i}/{total_count}: {test_case['name']}")
        print(f"❓ 问题: {test_case['question']}")
        
        try:
            # 记录开始时间
            start_time = datetime.now()
            
            # 调用分析方法
            result = service.analyze_question(test_case['question'])
            
            # 计算耗时
            elapsed_time = (datetime.now() - start_time).total_seconds()
            
            # 检查结果
            if result and isinstance(result, dict):
                if result.get('success'):
                    print(f"✅ 测试成功 - 耗时: {elapsed_time:.2f}秒")
                    print(f"📊 生成图表: {len(result.get('charts', []))} 个")
                    print(f"📝 分析报告长度: {len(result.get('analysis_report', ''))} 字符")
                    success_count += 1
                else:
                    print(f"⚠️ 分析失败: {result.get('error', '未知错误')}")
            else:
                print("❌ 返回结果格式异常")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        print("-" * 40)
        print()
    
    # 输出测试总结
    print("📊 测试总结:")
    print(f"  总测试数: {total_count}")
    print(f"  成功数: {success_count}")
    print(f"  失败数: {total_count - success_count}")
    print(f"  成功率: {(success_count/total_count)*100:.1f}%")
    
    return success_count == total_count

def check_log_files():
    """检查日志文件是否生成"""
    print("\n🔍 检查日志文件...")
    
    log_dir = os.path.join(current_dir, 'logs')
    if not os.path.exists(log_dir):
        print("❌ 日志目录不存在")
        return False
    
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
    
    if not log_files:
        print("❌ 没有找到日志文件")
        return False
    
    print(f"✅ 找到 {len(log_files)} 个日志文件:")
    
    for log_file in log_files:
        file_path = os.path.join(log_dir, log_file)
        file_size = os.path.getsize(file_path)
        modified_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        
        print(f"  📄 {log_file}")
        print(f"     大小: {file_size} 字节")
        print(f"     修改时间: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查最新的日志内容
        if 'smart_data' in log_file:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"     最新日志: {lines[-1].strip()}")
            except Exception as e:
                print(f"     读取失败: {e}")
        print()
    
    return True

def show_recent_logs():
    """显示最近的日志内容"""
    print("📋 最近的日志内容:")
    print("-" * 60)
    
    log_dir = os.path.join(current_dir, 'logs')
    smart_data_log = os.path.join(log_dir, 'smart_data.log')
    
    if os.path.exists(smart_data_log):
        try:
            with open(smart_data_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 显示最后20行
                recent_lines = lines[-20:] if len(lines) > 20 else lines
                
                for line in recent_lines:
                    print(line.rstrip())
        except Exception as e:
            print(f"❌ 读取日志失败: {e}")
    else:
        print("❌ 智能问数日志文件不存在")

def main():
    """主函数"""
    print("🚀 智能问数日志功能测试")
    print("=" * 60)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行测试
    test_success = test_logging_functionality()
    
    # 检查日志文件
    log_files_exist = check_log_files()
    
    # 显示最近日志
    if log_files_exist:
        show_recent_logs()
    
    # 总结
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    
    if test_success and log_files_exist:
        print("✅ 所有测试通过，日志功能正常工作")
        print("\n💡 提示:")
        print("  - 可以使用 'python view_logs.py list' 查看所有日志文件")
        print("  - 可以使用 'python view_logs.py tail 50' 查看最新50行日志")
        print("  - 可以使用 'python view_logs.py analyze' 分析日志统计信息")
    else:
        print("❌ 测试失败或日志功能异常")
        if not test_success:
            print("  - 智能问数功能测试失败")
        if not log_files_exist:
            print("  - 日志文件生成失败")
    
    print(f"⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
