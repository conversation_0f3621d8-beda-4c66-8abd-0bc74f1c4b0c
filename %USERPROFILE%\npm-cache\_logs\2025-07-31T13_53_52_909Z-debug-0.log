0 verbose cli D:\New Folder\node.exe D:\New Folder\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:D:\New Folder\node_modules\npm\npmrc
4 silly config load:file:E:\Demo\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:E:\Demo\zhengwudemobase0729\%USERPROFILE%\npm-global\etc\npmrc
7 verbose title npm exec http-server -p 8080
8 verbose argv "exec" "--" "http-server" "-p" "8080"
9 verbose logfile logs-max:10 dir:E:\Demo\zhengwudemobase0729\%USERPROFILE%\npm-cache\_logs\2025-07-31T13_53_52_909Z-
10 verbose logfile E:\Demo\zhengwudemobase0729\%USERPROFILE%\npm-cache\_logs\2025-07-31T13_53_52_909Z-debug-0.log
11 silly logfile done cleaning log files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 http fetch GET 200 https://registry.npmjs.org/http-server 233ms (cache revalidated)
14 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
15 verbose Could not read global path E:\Demo\zhengwudemobase0729\%USERPROFILE%\npm-global, ignoring
16 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
17 verbose shrinkwrap failed to load node_modules/.package-lock.json out of date, updated: node_modules
18 http fetch GET 200 https://registry.npmjs.org/npm 460ms
19 silly idealTree buildDeps
20 silly fetch manifest http-server@14.1.1
21 silly packumentCache full:https://registry.npmjs.org/http-server cache-miss
22 http fetch GET 200 https://registry.npmjs.org/http-server 66ms (cache revalidated)
23 silly packumentCache full:https://registry.npmjs.org/http-server set size:133770 disposed:false
24 silly placeDep ROOT http-server@14.1.1 REPLACE for:  want: 14.1.1
25 silly reify moves {}
26 silly audit bulk request {
26 silly audit   'ansi-styles': [ '4.3.0' ],
26 silly audit   async: [ '3.2.6' ],
26 silly audit   'basic-auth': [ '2.0.1' ],
26 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
26 silly audit   'call-bound': [ '1.0.4' ],
26 silly audit   chalk: [ '4.1.2' ],
26 silly audit   'color-convert': [ '2.0.1' ],
26 silly audit   'color-name': [ '1.1.4' ],
26 silly audit   corser: [ '2.0.1' ],
26 silly audit   debug: [ '4.4.1' ],
26 silly audit   'dunder-proto': [ '1.0.1' ],
26 silly audit   'es-define-property': [ '1.0.1' ],
26 silly audit   'es-errors': [ '1.3.0' ],
26 silly audit   'es-object-atoms': [ '1.1.1' ],
26 silly audit   eventemitter3: [ '4.0.7' ],
26 silly audit   'follow-redirects': [ '1.15.9' ],
26 silly audit   'function-bind': [ '1.1.2' ],
26 silly audit   'get-intrinsic': [ '1.3.0' ],
26 silly audit   'get-proto': [ '1.0.1' ],
26 silly audit   gopd: [ '1.2.0' ],
26 silly audit   'has-flag': [ '4.0.0' ],
26 silly audit   'has-symbols': [ '1.1.0' ],
26 silly audit   hasown: [ '2.0.2' ],
26 silly audit   he: [ '1.2.0' ],
26 silly audit   'html-encoding-sniffer': [ '3.0.0' ],
26 silly audit   'http-proxy': [ '1.18.1' ],
26 silly audit   'iconv-lite': [ '0.6.3' ],
26 silly audit   'math-intrinsics': [ '1.1.0' ],
26 silly audit   mime: [ '1.6.0' ],
26 silly audit   minimist: [ '1.2.8' ],
26 silly audit   ms: [ '2.1.3' ],
26 silly audit   'object-inspect': [ '1.13.4' ],
26 silly audit   opener: [ '1.5.2' ],
26 silly audit   portfinder: [ '1.0.37' ],
26 silly audit   qs: [ '6.14.0' ],
26 silly audit   'requires-port': [ '1.0.0' ],
26 silly audit   'safe-buffer': [ '5.1.2' ],
26 silly audit   'safer-buffer': [ '2.1.2' ],
26 silly audit   'secure-compare': [ '3.0.1' ],
26 silly audit   'side-channel': [ '1.1.0' ],
26 silly audit   'side-channel-list': [ '1.0.0' ],
26 silly audit   'side-channel-map': [ '1.0.1' ],
26 silly audit   'side-channel-weakmap': [ '1.0.2' ],
26 silly audit   'supports-color': [ '7.2.0' ],
26 silly audit   union: [ '0.5.0' ],
26 silly audit   'url-join': [ '4.0.1' ],
26 silly audit   'whatwg-encoding': [ '2.0.0' ],
26 silly audit   'http-server': [ '14.1.1' ]
26 silly audit }
27 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 266ms
28 silly audit report {}
