{"_id": "requires-port", "_rev": "15-681ddf92f0af0aee927b07502b28a4ab", "name": "requires-port", "description": "Check if a protocol requires a certain port number to be added to an URL.", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.0.0": {"name": "requires-port", "version": "0.0.0", "description": "Check if a protocol requires a certain port number to be added to an URL.", "main": "index.js", "scripts": {"test": "mocha --reporter spec --ui bdd test.js", "watch": "mocha --watch --reporter spec --ui bdd test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- --reporter spec --ui bdd test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter spec --ui bdd test.js"}, "repository": {"type": "git", "url": "https://github.com/unshiftio/requries-port"}, "keywords": ["port", "require", "http", "https", "ws", "wss", "gopher", "file", "ftp", "requires", "requried", "portnumber", "url", "parsing", "validation", "cows"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/unshiftio/requries-port/issues"}, "homepage": "https://github.com/unshiftio/requries-port", "devDependencies": {"assume": "0.0.x", "istanbul": "0.3.x", "mocha": "1.21.x", "pre-commit": "0.0.x"}, "gitHead": "cdd88d620507e79c4b5d57c3b353d5da8ac66988", "_id": "requires-port@0.0.0", "_shasum": "d9914dce124d3d5e75ceda38ca5434069f7132b3", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "dist": {"shasum": "d9914dce124d3d5e75ceda38ca5434069f7132b3", "tarball": "https://registry.npmjs.org/requires-port/-/requires-port-0.0.0.tgz", "integrity": "sha512-m/J1HWP0nf6c1wekYXTaUT3LGPjWLlwfVySp2EQr8MoPQIRQdIA9NOKgaWThzvu6CaoxB5bkfTEWD6nfo+9rEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDL+97HvQhKUGDA8J6GP2LzdUu8GdH7TrFWOVd+z0ewzAIgNeko6gcpRaG4nSXaffb6O6+ow9JYRMrpQZYmBANzb8k="}]}, "directories": {}}, "0.0.1": {"name": "requires-port", "version": "0.0.1", "description": "Check if a protocol requires a certain port number to be added to an URL.", "main": "index.js", "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "test": "mocha test.js", "watch": "mocha --watch test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js"}, "repository": {"type": "git", "url": "git+https://github.com/unshiftio/requires-port.git"}, "keywords": ["port", "require", "http", "https", "ws", "wss", "gopher", "file", "ftp", "requires", "requried", "portnumber", "url", "parsing", "validation", "cows"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/unshiftio/requires-port/issues"}, "homepage": "https://github.com/unshiftio/requires-port", "devDependencies": {"assume": "1.1.x", "istanbul": "0.3.x", "mocha": "2.1.x", "pre-commit": "1.0.x"}, "gitHead": "d6235df7aa7e8d08e9ac72c842e1e2c6c366376f", "_id": "requires-port@0.0.1", "_shasum": "4b4414411d9df7c855995dd899a8c78a2951c16d", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4b4414411d9df7c855995dd899a8c78a2951c16d", "tarball": "https://registry.npmjs.org/requires-port/-/requires-port-0.0.1.tgz", "integrity": "sha512-AzPDCliPoWDSvEVYRQmpzuPhGGEnPrQz9YiOEvn+UdB9ixBpw+4IOZWtwctmpzySLZTy7ynpn47V14H4yaowtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNkpsTuBdYwiD2fslcubhqpi+IJgUxX+Eeotomb7dJ7wIgLw16a0QoTS0HO2gthlekQnJJRPG4uCVYRbwrJ/FqVwM="}]}, "directories": {}}, "1.0.0": {"name": "requires-port", "version": "1.0.0", "description": "Check if a protocol requires a certain port number to be added to an URL.", "main": "index.js", "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "test-travis": "istanbul cover _mocha --report lcovonly -- test.js", "coverage": "istanbul cover _mocha -- test.js", "watch": "mocha --watch test.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "git+https://github.com/unshiftio/requires-port.git"}, "keywords": ["port", "require", "http", "https", "ws", "wss", "gopher", "file", "ftp", "requires", "requried", "portnumber", "url", "parsing", "validation", "cows"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/unshiftio/requires-port/issues"}, "homepage": "https://github.com/unshiftio/requires-port", "devDependencies": {"assume": "1.3.x", "istanbul": "0.4.x", "mocha": "2.3.x", "pre-commit": "1.1.x"}, "gitHead": "3a552b935dd2ddba8f2ddf9096932f0f2024edfd", "_id": "requires-port@1.0.0", "_shasum": "925d2601d39ac485e091cf0da5c6e694dc3dcaff", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "925d2601d39ac485e091cf0da5c6e694dc3dcaff", "tarball": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7ULUr4cjvXv4RF2Yoqn8J+vpz1P2Gpeyg629/rXTbhQIhAKXAdGSkgtHxKGz7ZEl38G1UylcB17D92BrEzag+JYq2"}]}, "directories": {}}}, "readme": "# requires-port\n\n[![Made by unshift](https://img.shields.io/badge/made%20by-unshift-00ffcc.svg?style=flat-square)](http://unshift.io)[![Version npm](http://img.shields.io/npm/v/requires-port.svg?style=flat-square)](http://browsenpm.org/package/requires-port)[![Build Status](http://img.shields.io/travis/unshiftio/requires-port/master.svg?style=flat-square)](https://travis-ci.org/unshiftio/requires-port)[![Dependencies](https://img.shields.io/david/unshiftio/requires-port.svg?style=flat-square)](https://david-dm.org/unshiftio/requires-port)[![Coverage Status](http://img.shields.io/coveralls/unshiftio/requires-port/master.svg?style=flat-square)](https://coveralls.io/r/unshiftio/requires-port?branch=master)[![IRC channel](http://img.shields.io/badge/IRC-irc.freenode.net%23unshift-00a8ff.svg?style=flat-square)](http://webchat.freenode.net/?channels=unshift)\n\nThe module name says it all, check if a protocol requires a given port.\n\n## Installation\n\nThis module is intended to be used with browserify or Node.js and is distributed\nin the public npm registry. To install it simply run the following command from\nyour CLI:\n\n```j\nnpm install --save requires-port\n```\n\n## Usage\n\nThe module exports it self as function and requires 2 arguments:\n\n1. The port number, can be a string or number.\n2. Protocol, can be `http`, `http:` or even `https://yomoma.com`. We just split\n   it at `:` and use the first result. We currently accept the following\n   protocols:\n   - `http`\n   - `https`\n   - `ws`\n   - `wss`\n   - `ftp`\n   - `gopher`\n   - `file`\n\nIt returns a boolean that indicates if protocol requires this port to be added\nto your URL.\n\n```js\n'use strict';\n\nvar required = require('requires-port');\n\nconsole.log(required('8080', 'http')) // true\nconsole.log(required('80', 'http'))   // false\n```\n\n# License\n\nMIT\n", "maintainers": [{"email": "<EMAIL>", "name": "v1"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "time": {"modified": "2022-06-26T11:43:56.491Z", "created": "2014-11-07T11:03:17.791Z", "0.0.0": "2014-11-07T11:03:17.791Z", "0.0.1": "2015-05-26T09:31:22.078Z", "1.0.0": "2015-10-30T14:42:33.794Z"}, "homepage": "https://github.com/unshiftio/requires-port", "keywords": ["port", "require", "http", "https", "ws", "wss", "gopher", "file", "ftp", "requires", "requried", "portnumber", "url", "parsing", "validation", "cows"], "repository": {"type": "git", "url": "git+https://github.com/unshiftio/requires-port.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/unshiftio/requires-port/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"mojaray2k": true, "staydan": true, "papasavva": true}}