# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

## [v1.18.1](https://github.com/http-party/node-http-proxy/compare/1.18.0...v1.18.1) - 2020-05-17

### Merged

- Skip sending the proxyReq event when the expect header is present [`#1447`](https://github.com/http-party/node-http-proxy/pull/1447)
- Remove node6 support, add node12 to build [`#1397`](https://github.com/http-party/node-http-proxy/pull/1397)

## [1.18.0](https://github.com/http-party/node-http-proxy/compare/1.17.0...1.18.0) - 2019-09-18

### Merged

- Added in auto-changelog module set to keepachangelog format [`#1373`](https://github.com/http-party/node-http-proxy/pull/1373)
- fix 'Modify Response' readme section to avoid unnecessary array copying [`#1300`](https://github.com/http-party/node-http-proxy/pull/1300)
- Fix incorrect target name for reverse proxy example [`#1135`](https://github.com/http-party/node-http-proxy/pull/1135)
- Fix modify response middleware example [`#1139`](https://github.com/http-party/node-http-proxy/pull/1139)
- [dist] Update dependency async to v3 [`#1359`](https://github.com/http-party/node-http-proxy/pull/1359)
- Fix path to local http-proxy in examples. [`#1072`](https://github.com/http-party/node-http-proxy/pull/1072)
- fix reverse-proxy example require path [`#1067`](https://github.com/http-party/node-http-proxy/pull/1067)
- Update README.md [`#970`](https://github.com/http-party/node-http-proxy/pull/970)
- [dist] Update dependency request to ~2.88.0 [SECURITY] [`#1357`](https://github.com/http-party/node-http-proxy/pull/1357)
- [dist] Update dependency eventemitter3 to v4 [`#1365`](https://github.com/http-party/node-http-proxy/pull/1365)
- [dist] Update dependency colors to v1 [`#1360`](https://github.com/http-party/node-http-proxy/pull/1360)
- [dist] Update all non-major dependencies [`#1356`](https://github.com/http-party/node-http-proxy/pull/1356)
- [dist] Update dependency agentkeepalive to v4 [`#1358`](https://github.com/http-party/node-http-proxy/pull/1358)
- [dist] Update dependency nyc to v14 [`#1367`](https://github.com/http-party/node-http-proxy/pull/1367)
- [dist] Update dependency concat-stream to v2 [`#1363`](https://github.com/http-party/node-http-proxy/pull/1363)
- x-forwarded-host overwrite for mutli level proxies [`#1267`](https://github.com/http-party/node-http-proxy/pull/1267)
- [refactor doc] Complete rename to http-party org. [`#1362`](https://github.com/http-party/node-http-proxy/pull/1362)
- Highlight correct lines for createProxyServer [`#1117`](https://github.com/http-party/node-http-proxy/pull/1117)
- Fix docs for rewrite options - 201 also handled [`#1147`](https://github.com/http-party/node-http-proxy/pull/1147)
- Update .nyc_output [`#1339`](https://github.com/http-party/node-http-proxy/pull/1339)
- Configure Renovate [`#1355`](https://github.com/http-party/node-http-proxy/pull/1355)
- [examples] Restream body before proxying, support for Content-Type of application/x-www-form-urlencoded [`#1264`](https://github.com/http-party/node-http-proxy/pull/1264)

### Commits

- [dist] New test fixtures. [`7e4a0e5`](https://github.com/http-party/node-http-proxy/commit/7e4a0e511bc30c059216860153301de2cdd1e97f)
- [dist] End of an era. [`a9b09cc`](https://github.com/http-party/node-http-proxy/commit/a9b09cce43f072db99fb5170030a05536177ccb7)
- [dist] Version bump. 1.18.0 [`9bbe486`](https://github.com/http-party/node-http-proxy/commit/9bbe486c5efcc356fb4d189ef38eee275bbde345)
- [fix] Latest versions. [`59c4403`](https://github.com/http-party/node-http-proxy/commit/59c4403e9dc15ab9b19ee2a3f4aecbfc6c3d94c4)
- [fix test] Update tests. [`dd1d08b`](https://github.com/http-party/node-http-proxy/commit/dd1d08b6319d1def729554446a5b0176978a8dad)
- [dist] Update dependency ws to v3 [SECURITY] [`b00911c`](https://github.com/http-party/node-http-proxy/commit/b00911c93740a00c5cfbacbb91565cb6912ed255)
- [dist] .gitattributes all the things. [`fc93520`](https://github.com/http-party/node-http-proxy/commit/fc93520d741ec80be8ae31ca005f3e9c199e330e)
- [dist] Regenerate package-lock.json. [`16d4f8a`](https://github.com/http-party/node-http-proxy/commit/16d4f8a95162b2e2e4ee6657c500f1208c044b2d)

## [1.17.0](https://github.com/http-party/node-http-proxy/compare/1.16.2...1.17.0) - 2018-04-20

### Merged

- Fix overwriting of global options [`#1074`](https://github.com/http-party/node-http-proxy/pull/1074)
- Update README.md [`#1131`](https://github.com/http-party/node-http-proxy/pull/1131)
- Update README.md with CoC link [`#1120`](https://github.com/http-party/node-http-proxy/pull/1120)
- Add Code Of Conduct [`#1119`](https://github.com/http-party/node-http-proxy/pull/1119)
- [deps] Update eventemitter3 to version 2.0.x [`#1109`](https://github.com/http-party/node-http-proxy/pull/1109)

### Fixed

- Fix "Can't set headers after they are sent" errors [`#930`](https://github.com/http-party/node-http-proxy/issues/930)
- Include websocket non-upgrade response [`#890`](https://github.com/http-party/node-http-proxy/issues/890)

### Commits

- Add followRedirects option [`c9a556c`](https://github.com/http-party/node-http-proxy/commit/c9a556cfa57c7ce0b877e16f2c2e1448d8cc278d)
- [test] add test for selfHandleRequest and remove modifyResponse as selfHandleRequest is the only way that functionality works [`4a37175`](https://github.com/http-party/node-http-proxy/commit/4a37175a5296d2ea2da0fc15a3f8fe08599bb592)
- Adding ability to set cookie path [`2c98416`](https://github.com/http-party/node-http-proxy/commit/2c98416ac2bf17bb5f515b9e10ee4485f5913846)
- Updating docs and adding more tests. [`f5c2381`](https://github.com/http-party/node-http-proxy/commit/f5c2381395e01bf8d6655cc70e14032c8f0aaa67)
- [dist] make tests work reliably, add package-lock.json [`09dcb98`](https://github.com/http-party/node-http-proxy/commit/09dcb984565dabb159a01a75a188b974f8c176ad)
- add support for modify response [`e5c02b8`](https://github.com/http-party/node-http-proxy/commit/e5c02b8a8a902e204eee886acafbbfe46c4a3aef)
- [wip] proper tests and reporting [`f4ff100`](https://github.com/http-party/node-http-proxy/commit/f4ff1006b9e71eb4185a3edf03333dbe514a84c9)
- Add detail about "buffer" option [`6f88caf`](https://github.com/http-party/node-http-proxy/commit/6f88caf6e46d84a809910c591e138250b333b39f)
- Add use case for proxy to HTTPS using a PKCS12 client certificate [`d2f9db8`](https://github.com/http-party/node-http-proxy/commit/d2f9db824136358a06dc3dd566644f3a016f24e2)
- [test] for override method feature [`81d58c5`](https://github.com/http-party/node-http-proxy/commit/81d58c531be3f61efb56d2489a66c73a7b2325fe)
- [dist] doc updates [`e94d529`](https://github.com/http-party/node-http-proxy/commit/e94d52973a26cf817a9de12d97e5ae603093f70d)
- feat: 添加response自处理参数 [`89f9ef8`](https://github.com/http-party/node-http-proxy/commit/89f9ef87e0532d54d086719c5ace1a968a42e51b)
- [dist][test] codecov config [`a4bccc3`](https://github.com/http-party/node-http-proxy/commit/a4bccc332d36d7db93db984674cd7e51b43a1b99)
- Removing unnecessary check since this is a private API [`bc6a237`](https://github.com/http-party/node-http-proxy/commit/bc6a23709c37c65b5b16cc802d05cb57f099b0ce)
- issue #953: stop using writeHead [`2c44039`](https://github.com/http-party/node-http-proxy/commit/2c44039a7c30b190043da654ee7e5aed0304e979)
- [fix] move badges [`543636d`](https://github.com/http-party/node-http-proxy/commit/543636d0f662308ec8c9afdbf641f4036f002bfd)
- fix small typos in README [`8231984`](https://github.com/http-party/node-http-proxy/commit/8231984fb02dca331b4ef77e089db50855eea4f5)
- Added timeout option to docs [`107c187`](https://github.com/http-party/node-http-proxy/commit/107c18720c3906f9049cc14d075b31910c0ccf55)
- [dist] document the feature [`d533a1b`](https://github.com/http-party/node-http-proxy/commit/d533a1be437b37fed5bd25f5e58298eea819f974)
- [fix] slightly more tolerant [`de1b808`](https://github.com/http-party/node-http-proxy/commit/de1b80851ab1b1251b5eaeaf0beab164024f09b6)
- Forgot 'i' flag when changing from regex shorthand to string. [`50f58b4`](https://github.com/http-party/node-http-proxy/commit/50f58b4cd9b4422a11512a6a065432159b5bc806)
- Update common.js [`c5d8466`](https://github.com/http-party/node-http-proxy/commit/c5d846648304f2e36a172b25d9fb8300d8131f8c)
- [fix] rm newline [`e6f24ba`](https://github.com/http-party/node-http-proxy/commit/e6f24ba6173c4fdd26089b3c729de5dbdd71ad74)
- [dist] update package-lock.json [`abf882e`](https://github.com/http-party/node-http-proxy/commit/abf882e03c92cf1665d5b7d4dbdaf87feb50a677)

## [1.16.2](https://github.com/http-party/node-http-proxy/compare/1.16.1...1.16.2) - 2016-12-06

### Merged

- [WIP] Revert default behavior of writeHeaders method [`#1104`](https://github.com/http-party/node-http-proxy/pull/1104)

## [1.16.1](https://github.com/http-party/node-http-proxy/compare/1.16.0...1.16.1) - 2016-12-04

### Commits

- Enable proxy response to have multiple Set-Cookie raw headers #1101 [`8cb451f`](https://github.com/http-party/node-http-proxy/commit/8cb451f20cff0a19fc9576fc2558307fb17a5710)
- [dist] Version bump. 1.16.1 [`ac1a01b`](https://github.com/http-party/node-http-proxy/commit/ac1a01b1f3caa3a2a9433341bf5e7a95072d6612)

## [1.16.0](https://github.com/http-party/node-http-proxy/compare/1.15.2...1.16.0) - 2016-12-02

### Merged

- Fix newly introduced error in error handler for ECONNREFUSED in forward proxy [`#1100`](https://github.com/http-party/node-http-proxy/pull/1100)
- Keep original letter case of response header keys [`#1098`](https://github.com/http-party/node-http-proxy/pull/1098)
- Handle errors for forward request, add test case [`#1099`](https://github.com/http-party/node-http-proxy/pull/1099)

### Commits

- add node 6 to travis [`2f7f037`](https://github.com/http-party/node-http-proxy/commit/2f7f03778cfb94396acf0d778061ea197212fbb5)

## [1.15.2](https://github.com/http-party/node-http-proxy/compare/1.15.1...1.15.2) - 2016-10-22

### Merged

- Add proxy-timeout option to documentation [`#1075`](https://github.com/http-party/node-http-proxy/pull/1075)

### Commits

- Do not rely on func.name (no scope) [`61c2889`](https://github.com/http-party/node-http-proxy/commit/61c28891093b256bbc0dae78e45e2c5f0acf2101)
- Do not rely on func.name (no scope) [`d48f67e`](https://github.com/http-party/node-http-proxy/commit/d48f67eb90d8af66211093e91efdd6638859e0bf)
- Expose full callback names [`220f5fb`](https://github.com/http-party/node-http-proxy/commit/220f5fb795d2977c5a68ae59d7db65089efed50c)
- test case added [`f5217d6`](https://github.com/http-party/node-http-proxy/commit/f5217d6c20c164ed412a3b20f660786b6f88b35b)
- [fix] style nits [`d0f1dfe`](https://github.com/http-party/node-http-proxy/commit/d0f1dfeb8277d46a057017cd888b50e85f6725d6)
- With a comment [`fbc2668`](https://github.com/http-party/node-http-proxy/commit/fbc266809c289fbdb59d7944345816a858303c96)
- Fix browserification [`8eddf45`](https://github.com/http-party/node-http-proxy/commit/8eddf45f2a043e4e1b3f6e33c304e68fe7e1c406)
- not setting connection header in case of http2 as it is deprecated [`2d01edc`](https://github.com/http-party/node-http-proxy/commit/2d01edc5a5ace591784022b85860a3bbc48c5e12)

## [1.15.1](https://github.com/http-party/node-http-proxy/compare/1.15.0...1.15.1) - 2016-09-14

### Merged

- Properly write response header optionally including statusMessage [`#1061`](https://github.com/http-party/node-http-proxy/pull/1061)

### Commits

- [dist] Version bump. 1.15.1 [`912cd3a`](https://github.com/http-party/node-http-proxy/commit/912cd3acaef484f7ea08affc9339250082e04058)

## [1.15.0](https://github.com/http-party/node-http-proxy/compare/1.14.0...1.15.0) - 2016-09-14

### Merged

- Made it not to crash with omited Host http header [`#1050`](https://github.com/http-party/node-http-proxy/pull/1050)
- README.md: fix typo: 'ingoing' should be 'incoming' [`#1060`](https://github.com/http-party/node-http-proxy/pull/1060)
- Fix for Reason-Phrase being overwritten on proxy response. [`#1051`](https://github.com/http-party/node-http-proxy/pull/1051)
- cookieDomainRewrite option [`#1009`](https://github.com/http-party/node-http-proxy/pull/1009)
- Update ntlm-authentication.js [`#1025`](https://github.com/http-party/node-http-proxy/pull/1025)
- Restream body before proxying [`#1027`](https://github.com/http-party/node-http-proxy/pull/1027)
- Location rewriting for responses with status 201 [`#1024`](https://github.com/http-party/node-http-proxy/pull/1024)
- #866 Copy CA from options into outbound proxy [`#1042`](https://github.com/http-party/node-http-proxy/pull/1042)

### Fixed

- Restream body before proxying (#1027) [`#955`](https://github.com/http-party/node-http-proxy/issues/955)

### Commits

- [dist] Version bump. 1.15.0 [`b98c75b`](https://github.com/http-party/node-http-proxy/commit/b98c75b1ff3ebdf7f78224eb0d9aa857af2db1d9)

## [1.14.0](https://github.com/http-party/node-http-proxy/compare/1.13.3...1.14.0) - 2016-06-15

### Merged

- Emit disconnected event instead of error when ECONNRESET [`#966`](https://github.com/http-party/node-http-proxy/pull/966)
- fix test for node 0.10 + socket.io-client@1.4.6 (engine.io-client@1.6.9) [`#1010`](https://github.com/http-party/node-http-proxy/pull/1010)

### Commits

- [dist] Version bump. 1.14.0 [`fcfb0b3`](https://github.com/http-party/node-http-proxy/commit/fcfb0b37f6ac61369565507446377f91d955cf29)

## [1.13.3](https://github.com/http-party/node-http-proxy/compare/1.13.2...1.13.3) - 2016-05-16

### Merged

- fix browserify compatibility [`#975`](https://github.com/http-party/node-http-proxy/pull/975)
- alter message error [`#998`](https://github.com/http-party/node-http-proxy/pull/998)
- Sanitize header keys before setting them [`#997`](https://github.com/http-party/node-http-proxy/pull/997)
- Update ntlm-authentication.js [`#989`](https://github.com/http-party/node-http-proxy/pull/989)
- Add expected datatype to readme [`#983`](https://github.com/http-party/node-http-proxy/pull/983)
- Update README [`#982`](https://github.com/http-party/node-http-proxy/pull/982)
- Fix formatting of the `headers` option [`#974`](https://github.com/http-party/node-http-proxy/pull/974)
- Set the x-forwarded-host flag when xfwd is enabled [`#967`](https://github.com/http-party/node-http-proxy/pull/967)

### Fixed

- Sanitize header keys before setting them (#997) [`#996`](https://github.com/http-party/node-http-proxy/issues/996)

### Commits

- [dist] Update LICENSE to reflect 2015 changes. [`f345a1a`](https://github.com/http-party/node-http-proxy/commit/f345a1ac2dde1884e72b952a685a0a1796059f14)
- [dist] Version bump. 1.13.3 [`5082acc`](https://github.com/http-party/node-http-proxy/commit/5082acc067bbf287f503bbd5b776f798ab169db1)

## [1.13.2](https://github.com/http-party/node-http-proxy/compare/1.13.1...1.13.2) - 2016-02-17

### Merged

- Fixed missing documentation: #options.headers [`#806`](https://github.com/http-party/node-http-proxy/pull/806)
- #949 Proxy example using req instead res on README [`#950`](https://github.com/http-party/node-http-proxy/pull/950)
- mocha: Use default reporter [`#962`](https://github.com/http-party/node-http-proxy/pull/962)
- Remove "transfer-encoding" header if "content-length" is set to zero [`#961`](https://github.com/http-party/node-http-proxy/pull/961)

### Commits

- [dist] Version bump. 1.13.2 [`e1b2f4c`](https://github.com/http-party/node-http-proxy/commit/e1b2f4c31b34464431db251b3b6169689dadf518)

## [1.13.1](https://github.com/http-party/node-http-proxy/compare/1.13.0...1.13.1) - 2016-02-02

### Merged

- README.md: summary to specify reverse proxy [`#932`](https://github.com/http-party/node-http-proxy/pull/932)
- fix(common) urlJoin replace: ":/" -&gt; "http?s:/" [`#947`](https://github.com/http-party/node-http-proxy/pull/947)
- Update README.md [`#948`](https://github.com/http-party/node-http-proxy/pull/948)

### Commits

- [dist] Version bump. 1.13.1 [`9d9fa94`](https://github.com/http-party/node-http-proxy/commit/9d9fa940cff3aa6134c60732c23aea8171fc7296)

## [1.13.0](https://github.com/http-party/node-http-proxy/compare/1.12.1...1.13.0) - 2016-01-26

### Merged

- Fix for #839 (Ignore path and the trailing slash) [`#934`](https://github.com/http-party/node-http-proxy/pull/934)
- Update license year range to 2016 [`#943`](https://github.com/http-party/node-http-proxy/pull/943)

### Commits

- [dist] Version bump. 1.13.0 [`268994e`](https://github.com/http-party/node-http-proxy/commit/268994ea45d9f8737343001ab9542e03023a5c96)

## [1.12.1](https://github.com/http-party/node-http-proxy/compare/1.12.0...1.12.1) - 2016-01-24

### Merged

- Bump version for npm publish [`#942`](https://github.com/http-party/node-http-proxy/pull/942)
- Added check to passes/web-outgoing.js to make sure the header being s… [`#940`](https://github.com/http-party/node-http-proxy/pull/940)
- Created reverse-proxy.js example. [`#825`](https://github.com/http-party/node-http-proxy/pull/825)
- SSE example and test [`#922`](https://github.com/http-party/node-http-proxy/pull/922)
- More structured readme [`#912`](https://github.com/http-party/node-http-proxy/pull/912)
- Updated markdown docs to mention proxy rules module [`#910`](https://github.com/http-party/node-http-proxy/pull/910)
- Add tests for forwarding of continuation frames [`#901`](https://github.com/http-party/node-http-proxy/pull/901)
- Bump requires-port, server and ws [`#904`](https://github.com/http-party/node-http-proxy/pull/904)
- [example] add an example for NTLM authentication [`#903`](https://github.com/http-party/node-http-proxy/pull/903)

### Commits

- Organized README more [`cd1d777`](https://github.com/http-party/node-http-proxy/commit/cd1d7776e8fb5d67e2c52b9ef27d8c932e7b72e2)
- Add tests for testing forwarding of continuation frames [`64fa520`](https://github.com/http-party/node-http-proxy/commit/64fa52078913c6d4fe95673f182aac4924961e8b)
- Added back to top helpers [`6106d4c`](https://github.com/http-party/node-http-proxy/commit/6106d4c32f7c7960f0391591661e6f0d229db52d)
- [ci] use node 4.2 to test and do not allow failures [`f82ce18`](https://github.com/http-party/node-http-proxy/commit/f82ce18d2f187b085c2c4f49d857755d21c582b1)
- [fix] bump requires-port, server and ws [`9ea1e89`](https://github.com/http-party/node-http-proxy/commit/9ea1e89a2fd9c392cd40265bdb13494a3614e290)
- Updated markdown docs to mention proxy rules [`eea79ca`](https://github.com/http-party/node-http-proxy/commit/eea79cab53f27371cad387a524ee3aaefa742c48)
- Fixed tests depending on ignorePath [`f9540de`](https://github.com/http-party/node-http-proxy/commit/f9540de7b13f41091be2dcb68d8f23be65ad3885)
- Added check to passes/web-outgoing.js to make sure the header being set is not undefined, which should be the only falsey value that could accidently show up and break that call. This fixes windows NTLM auth issues behind http-proxy. [`3b39d2c`](https://github.com/http-party/node-http-proxy/commit/3b39d2c3dcb1785cc06043fcb226c652f554941e)
- No longer appends / to path if ignorePath is set [`f2093b5`](https://github.com/http-party/node-http-proxy/commit/f2093b5313c855cd6309cc0ddebb31f369e525ed)
- README.md: introduction to specify reverse proxy [`41414a5`](https://github.com/http-party/node-http-proxy/commit/41414a56a11ddfac3a337711ac4c64124eb62377)
- Added note for appending trailing / when using ignorePath [`0cb1d3c`](https://github.com/http-party/node-http-proxy/commit/0cb1d3c68e793fed9aa4a7624c32a018e796aa95)

## [1.12.0](https://github.com/http-party/node-http-proxy/compare/1.11.3...1.12.0) - 2015-10-22

### Merged

- Issue #896: provide a "proxyReq" event also for websocket connections. [`#897`](https://github.com/http-party/node-http-proxy/pull/897)

### Commits

- Provide a "proxyReq" event also for websocket connections. [`a05fc2d`](https://github.com/http-party/node-http-proxy/commit/a05fc2d1692d038f1eaad6d9b26c174039bc1949)
- fixes after PR review [`9752652`](https://github.com/http-party/node-http-proxy/commit/9752652e76da3bcfb6a635620e4162518ca43203)
- [dist] Version bump. 1.12.0 [`b5a6d0e`](https://github.com/http-party/node-http-proxy/commit/b5a6d0e58396363f4c457f6d1654614bdfcfcb73)

## [1.11.3](https://github.com/http-party/node-http-proxy/compare/1.11.2...1.11.3) - 2015-10-19

### Merged

- Removed unspecified trailing slash in proxy url [`#893`](https://github.com/http-party/node-http-proxy/pull/893)
- Updating the upgrading doc [`#892`](https://github.com/http-party/node-http-proxy/pull/892)

### Commits

- [dist] Update .travis.yml to be more modern. [`302d981`](https://github.com/http-party/node-http-proxy/commit/302d981dd2cf06dbf751b1f64e3dfea08d0f9476)
- [dist] Version bump. 1.11.3 [`60baca5`](https://github.com/http-party/node-http-proxy/commit/60baca5aed4f45ef1d7b3f7edd909375853d344b)
- docs: options.headers [`c86ae51`](https://github.com/http-party/node-http-proxy/commit/c86ae51bb9658309a9628f4f5182d4c45c803b84)

## [1.11.2](https://github.com/http-party/node-http-proxy/compare/v1.11.1...1.11.2) - 2015-08-30

### Merged

- Update gzip-middleware.js [`#870`](https://github.com/http-party/node-http-proxy/pull/870)
- Fix broken option list indentation [`#863`](https://github.com/http-party/node-http-proxy/pull/863)
- Added missing configuration options [`#852`](https://github.com/http-party/node-http-proxy/pull/852)
- Added installation instructions [`#823`](https://github.com/http-party/node-http-proxy/pull/823)
- fixes comment [`#817`](https://github.com/http-party/node-http-proxy/pull/817)

### Commits

- Created reverse-proxy.js example. [`38864d0`](https://github.com/http-party/node-http-proxy/commit/38864d016794b9ff3d8d1d1cb81a730b40a1bf9c)
- Added websocket set-cookie headers test [`855cebd`](https://github.com/http-party/node-http-proxy/commit/855cebdac4d33ef5f2fab4c4c78fdc07cdb61402)
- [fix] make more functional [`cea0e86`](https://github.com/http-party/node-http-proxy/commit/cea0e8676b3e609828320bb03051eaf78cc43b54)
- Modify the set-cookie header fix to work with node 0.10.x. [`da674ec`](https://github.com/http-party/node-http-proxy/commit/da674ec4df2b371f09e912f3b376c48581090a0f)
- Use raw headers instead parsed. [`8bfd90c`](https://github.com/http-party/node-http-proxy/commit/8bfd90c4d9331fd129f17a788ef9fc733654b7e0)
- Replaced Object.keys().map with for in loop. [`3d2350c`](https://github.com/http-party/node-http-proxy/commit/3d2350c54ff0fb9271f5fcfea1d23f22ad97c47c)
- [dist] Version bump. 1.11.2 [`30e3b37`](https://github.com/http-party/node-http-proxy/commit/30e3b371de0116e40e15156394f31c7e0b0aa9f1)
- Websocket key was unnecessary long. [`ca73208`](https://github.com/http-party/node-http-proxy/commit/ca732087498582df01ab78fb7da77912dab8f138)

## [v1.11.1](https://github.com/http-party/node-http-proxy/compare/v1.11.0...v1.11.1) - 2015-04-22

### Commits

- [dist] Version bump. 1.11.1 [`7e6c66a`](https://github.com/http-party/node-http-proxy/commit/7e6c66a7e485a6c0ec3a1c567bbe800fdc56c9fd)
- [fix] dont use bind in the one case we do [`d26ef56`](https://github.com/http-party/node-http-proxy/commit/d26ef56e1bc2a1232b06c01b4740e3bf35d63eda)
- [dist] update to new version of EE3 [`607f96c`](https://github.com/http-party/node-http-proxy/commit/607f96c00cbda2a6b881b8ff1db05437dbf4ce77)
- [fix] use the main export for EE3 [`18c77ca`](https://github.com/http-party/node-http-proxy/commit/18c77cafc7d5479502cf5c4d2b663d8f85cfd6d4)

## [v1.11.0](https://github.com/http-party/node-http-proxy/compare/v1.10.1...v1.11.0) - 2015-04-20

### Merged

- [api] add an ignorePath option if you want to disregard the path of the ... [`#759`](https://github.com/http-party/node-http-proxy/pull/759)

### Commits

- [dist] Version bump. 1.11.0 [`934e6c4`](https://github.com/http-party/node-http-proxy/commit/934e6c4d54292a1b961452074e02fb5d45da729a)

## [v1.10.1](https://github.com/http-party/node-http-proxy/compare/v1.10.0...v1.10.1) - 2015-04-02

### Merged

- Fix default port detection with node 0.12.x [`#799`](https://github.com/http-party/node-http-proxy/pull/799)

### Commits

- [dist] add semver and normalize package.json with --save-dev [`1b89bc9`](https://github.com/http-party/node-http-proxy/commit/1b89bc9a76c229070ff2572f7a0e1b969c4b4701)
- fix protocol and default port detection on node 0.12.x, compatible with 0.10.x [`5f14bca`](https://github.com/http-party/node-http-proxy/commit/5f14bcaa704fe8a5e6f59d3a89722f22958cade9)
- fix expected error message when node 0.12.x [`0ee314c`](https://github.com/http-party/node-http-proxy/commit/0ee314c436226391318b9a1b623cb3f7e8bf4df7)
- force cipher AES128-GCM-SHA256 in https tests [`c33d161`](https://github.com/http-party/node-http-proxy/commit/c33d1616cdbd60587ca2eb326c48b8a87ac56092)
- [fix] properly support iojs with test checking for HTTPS [`c6dfb04`](https://github.com/http-party/node-http-proxy/commit/c6dfb04a67f3b5ac9a402b7b08c1b8baf29f89e6)
- [dist] Version bump. 1.10.1 [`0bd446c`](https://github.com/http-party/node-http-proxy/commit/0bd446c680e9991accfaa3a6a70e411fdac79164)
- [ci] add 0.12 and iojs to travis [`a6ae6c4`](https://github.com/http-party/node-http-proxy/commit/a6ae6c499743ddade9db12b9f7404d980c79f683)

## [v1.10.0](https://github.com/http-party/node-http-proxy/compare/v1.9.1...v1.10.0) - 2015-04-01

### Merged

- Fixes / additions to URL rewriting [`#787`](https://github.com/http-party/node-http-proxy/pull/787)

### Commits

- [dist] Version bump. 1.10.0 [`1dabda2`](https://github.com/http-party/node-http-proxy/commit/1dabda241f3b93eb9195134042e7a3b84fd0ef57)

## [v1.9.1](https://github.com/http-party/node-http-proxy/compare/v1.9.0...v1.9.1) - 2015-04-01

### Merged

- Fix #747 [`#798`](https://github.com/http-party/node-http-proxy/pull/798)

### Fixed

- Merge pull request #798 from damonmcminn/master [`#747`](https://github.com/http-party/node-http-proxy/issues/747)
- Fix https://github.com/nodejitsu/node-http-proxy/issues/747 [`#747`](https://github.com/nodejitsu/node-http-proxy/issues/747)

### Commits

- Add test for https://github.com/nodejitsu/node-http-proxy/issues/747 [`d145152`](https://github.com/http-party/node-http-proxy/commit/d145152655a69479348b0ebc726d4dc19720a12b)
- [dist] Version bump. 1.9.1 [`21b30b7`](https://github.com/http-party/node-http-proxy/commit/21b30b754db4f6410c3d2052bc123b3fdae57c46)

## [v1.9.0](https://github.com/http-party/node-http-proxy/compare/v1.8.1...v1.9.0) - 2015-03-12

### Merged

- Adding the nodejs0.12 auth option [`#792`](https://github.com/http-party/node-http-proxy/pull/792)
- fix "x-forwarded-proto" in node 0.12 and iojs [`#789`](https://github.com/http-party/node-http-proxy/pull/789)
- Add support for auto host rewriting and protocol rewriting [`#1`](https://github.com/http-party/node-http-proxy/pull/1)
- changed highlighted part - very minor [`#756`](https://github.com/http-party/node-http-proxy/pull/756)
- Update README.md for benchmarks [`#625`](https://github.com/http-party/node-http-proxy/pull/625)

### Fixed

- fix "x-forwarded-proto" in node 0.12 and iojs [`#772`](https://github.com/http-party/node-http-proxy/issues/772)
- [api] add an ignorePath option if you want to disregard the path of the incoming request when proxying to the target server fixes #758 [`#758`](https://github.com/http-party/node-http-proxy/issues/758)

### Commits

- added auth header test [`df158bf`](https://github.com/http-party/node-http-proxy/commit/df158bfc53e35e62609d8169f3883f6dcf12b73c)
- added auth header test [`ff1626f`](https://github.com/http-party/node-http-proxy/commit/ff1626f0719652c92895cf80f9aacc22ededadad)
- refactor some tests for greater readability [`14415a5`](https://github.com/http-party/node-http-proxy/commit/14415a50741d1f258da884686455d87d68eb8121)
- only rewrite redirect urls when it matches target [`26029ba`](https://github.com/http-party/node-http-proxy/commit/26029ba7ac948b5dc0befb2091cc9a5862d0641c)
- auth header added [`ab5c3e5`](https://github.com/http-party/node-http-proxy/commit/ab5c3e5c819ca993e0616d178bc1d282af539508)
- [dist] Version bump. 1.9.0 [`87a92a7`](https://github.com/http-party/node-http-proxy/commit/87a92a72802a27f817fcba87382d55831fd04ddb)
- end of file line space [`e907d7b`](https://github.com/http-party/node-http-proxy/commit/e907d7bb2aa2825b43d9355cb1ee25bec47b15ad)
- space instead of tabs [`7298510`](https://github.com/http-party/node-http-proxy/commit/7298510e9170d74ff057487085bc1e898f044177)
- space instead of tabs [`63c9262`](https://github.com/http-party/node-http-proxy/commit/63c9262df5bd04d83432db44fce2a4d5b19a59ea)
- auth header added tests [`f55ffa3`](https://github.com/http-party/node-http-proxy/commit/f55ffa356a259c09685c6b768a404e4b73f674ce)

## [v1.8.1](https://github.com/http-party/node-http-proxy/compare/v1.8.0...v1.8.1) - 2014-12-17

### Commits

- Pass HTTPS client parameters. [`402ab05`](https://github.com/http-party/node-http-proxy/commit/402ab057340a29db7a521ff239c5e21ac0c12be8)
- [dist] Version bump. 1.8.1 [`3311106`](https://github.com/http-party/node-http-proxy/commit/3311106c2c2346f3ac1ffe402b80bca3c7c59275)

## [v1.8.0](https://github.com/http-party/node-http-proxy/compare/v1.7.3...v1.8.0) - 2014-12-17

### Merged

- Fix variables scope in test [`#752`](https://github.com/http-party/node-http-proxy/pull/752)
- Fix typo [`#751`](https://github.com/http-party/node-http-proxy/pull/751)

### Commits

- Added websocket close event test [`8bff3dd`](https://github.com/http-party/node-http-proxy/commit/8bff3ddc1276e3ba18fd68c34d8982148cd21455)
- Deprecated proxySocket event in favor to open event. [`c62610e`](https://github.com/http-party/node-http-proxy/commit/c62610e8e4d59e8ba4642370ff3fb933c6ddb4eb)
- Update README.md [`05d18a4`](https://github.com/http-party/node-http-proxy/commit/05d18a4e1ba6c2de41b0b803cd1793357979384d)
- [fix] style spacing wtf [`ea0a4de`](https://github.com/http-party/node-http-proxy/commit/ea0a4ded803b30144e442344ad5a38a0d34bb3ba)
- [api] add close event in ws-incoming.js [`2653786`](https://github.com/http-party/node-http-proxy/commit/26537866b3ca522927aa4604a958f90774c0c0c0)
- [minor] grammar [`f304861`](https://github.com/http-party/node-http-proxy/commit/f30486195cfa6cfcf6400ac445975d5adada72e4)
- Changed proxyServer and destiny to local variables. [`8a8a894`](https://github.com/http-party/node-http-proxy/commit/8a8a894092ddbec8f0365ced0e94a75b1307ecf1)
- [dist] Version bump. 1.8.0 [`f0db5b3`](https://github.com/http-party/node-http-proxy/commit/f0db5b3f708b0858f617d472dfdd0ba211b774ef)

## [v1.7.3](https://github.com/http-party/node-http-proxy/compare/v1.7.2...v1.7.3) - 2014-12-09

### Fixed

- [fix] use simple regex instead of indexOf to check the protocol to support without the colon fixes #711 [`#711`](https://github.com/http-party/node-http-proxy/issues/711)

### Commits

- [test] show that we support protocol without the colon [`89f9ca1`](https://github.com/http-party/node-http-proxy/commit/89f9ca1e89d679b2b85a8f85b65e8b0878694207)
- [dist] Version bump. 1.7.3 [`6a330ff`](https://github.com/http-party/node-http-proxy/commit/6a330ff904d02a41f9a1cac338a98da1849c54ca)

## [v1.7.2](https://github.com/http-party/node-http-proxy/compare/v1.7.1...v1.7.2) - 2014-12-08

### Merged

- Fix grammar in README.md [`#749`](https://github.com/http-party/node-http-proxy/pull/749)

### Fixed

- [fix] properly include port in host header with changeOrigin in all cases fixes #750 [`#750`](https://github.com/http-party/node-http-proxy/issues/750)

### Commits

- [test] add tests for the changeOrigin cases in properly setting the host header [`71a06aa`](https://github.com/http-party/node-http-proxy/commit/71a06aab0249487ff650c8a47906cc8281561664)
- [dist] pin down deps and add requires-port [`81874f7`](https://github.com/http-party/node-http-proxy/commit/81874f795b7df7929e03d9d4cb98a947b1ef114b)
- [dist] Version bump. 1.7.2 [`2086e49`](https://github.com/http-party/node-http-proxy/commit/2086e4917c97f347f84c54b166799bc8db9f4162)

## [v1.7.1](https://github.com/http-party/node-http-proxy/compare/v1.7.0...v1.7.1) - 2014-12-02

### Merged

- Adding harmon to the README [`#716`](https://github.com/http-party/node-http-proxy/pull/716)

### Fixed

- [fix] fix #738 [`#738`](https://github.com/http-party/node-http-proxy/issues/738)
- [fix] simple fixes #748 #744 #746 [`#748`](https://github.com/http-party/node-http-proxy/issues/748)

### Commits

- [test] add proper failing test case for #738 [`410a8ce`](https://github.com/http-party/node-http-proxy/commit/410a8ce94ccea566a8e50daf3b78e633b82875cb)
- [Bugfix] Allow for multiple ? in outgoing urls. [`70ed1c4`](https://github.com/http-party/node-http-proxy/commit/70ed1c4273bc64500e8bae9b60d7fd6a19135246)
- [dist] Version bump. 1.7.1 [`56a7b77`](https://github.com/http-party/node-http-proxy/commit/56a7b77645b13d337c1a2f879460193d310454c8)

## [v1.7.0](https://github.com/http-party/node-http-proxy/compare/v1.6.2...v1.7.0) - 2014-11-25

### Merged

- Allow optional redirect host rewriting. [`#741`](https://github.com/http-party/node-http-proxy/pull/741)
- Set `Content-Length` header for OPTIONS requests [`#742`](https://github.com/http-party/node-http-proxy/pull/742)
- copy headers instead of referencing them so they don't unexpectedly get overwritten [`#736`](https://github.com/http-party/node-http-proxy/pull/736)
- Updated to support error callback on proxy.web and start/proxyReq/end co... [`#735`](https://github.com/http-party/node-http-proxy/pull/735)

### Commits

- :pencil: Add host rewrite docs and specs. [`add8133`](https://github.com/http-party/node-http-proxy/commit/add81338a90dae132f9e74fd5a5905fbcef030b7)
- [minor] style consistency [`48ae5d8`](https://github.com/http-party/node-http-proxy/commit/48ae5d828c23d6f19c9e2dd8c922d88a09f5ed0f)
- Updated to support error callback on proxy.web and start/proxyReq/end continue working. [`9ba8311`](https://github.com/http-party/node-http-proxy/commit/9ba8311343fd01b32505b8607ecf4294200f9dde)
- style changes [`84036e9`](https://github.com/http-party/node-http-proxy/commit/84036e9ddd1d4d925006c5438b3bcc0f17ba7a48)
- [fix] be defensive and ensure location is in headers before running url.parse() [`8d68ac0`](https://github.com/http-party/node-http-proxy/commit/8d68ac0e0fa3080b31580aa08e92a46cc1f27696)
- [dist] Version bump. 1.7.0 [`276f65a`](https://github.com/http-party/node-http-proxy/commit/276f65a3b810ded01757ec4bfd4fe2b00a1e66a8)

## [v1.6.2](https://github.com/http-party/node-http-proxy/compare/v1.6.1...v1.6.2) - 2014-11-11

### Merged

- do not modify the query string [`#733`](https://github.com/http-party/node-http-proxy/pull/733)

### Commits

- [fix] style changes [`7c5e40a`](https://github.com/http-party/node-http-proxy/commit/7c5e40a429fbc0c538f38d29d74acb633cb9b8d4)
- [minor] this shouldnt be in var block [`3f19e6e`](https://github.com/http-party/node-http-proxy/commit/3f19e6e178e168a16beee74186691f3e0e54d517)
- [dist] Version bump. 1.6.2 [`709b3e9`](https://github.com/http-party/node-http-proxy/commit/709b3e96560d619fab2617f9ddb902b4982b4103)

## [v1.6.1](https://github.com/http-party/node-http-proxy/compare/v1.6.0...v1.6.1) - 2014-11-04

### Merged

- websocket needs to respect `options.secure` too [`#729`](https://github.com/http-party/node-http-proxy/pull/729)
- changeOrigin option docs fix [`#724`](https://github.com/http-party/node-http-proxy/pull/724)

### Commits

- [dist] Version bump. 1.6.1 [`fa797fc`](https://github.com/http-party/node-http-proxy/commit/fa797fca900c10ebc848a2b445204b47da799483)

## [v1.6.0](https://github.com/http-party/node-http-proxy/compare/v1.5.3...v1.6.0) - 2014-10-29

### Merged

- Added changeOrigin option with test and docs [`#723`](https://github.com/http-party/node-http-proxy/pull/723)
- I presume you mean couchdb here [`#717`](https://github.com/http-party/node-http-proxy/pull/717)
- update modify request body eg [`#712`](https://github.com/http-party/node-http-proxy/pull/712)

### Commits

- harmon notes [`9f684d0`](https://github.com/http-party/node-http-proxy/commit/9f684d0439174d889d7b9a4ef6e2353e09481b2d)
- [dist] Version bump. 1.6.0 [`43641b0`](https://github.com/http-party/node-http-proxy/commit/43641b00b34ccc05bdf09f904695061d7c857aeb)

## [v1.5.3](https://github.com/http-party/node-http-proxy/compare/v1.5.2...v1.5.3) - 2014-10-01

### Merged

- close socket if upstream request fails [`#709`](https://github.com/http-party/node-http-proxy/pull/709)

### Commits

- [dist] Version bump. 1.5.3 [`9577a0f`](https://github.com/http-party/node-http-proxy/commit/9577a0faf2b78af606168673407ac47a851c084c)

## [v1.5.2](https://github.com/http-party/node-http-proxy/compare/v1.5.1...v1.5.2) - 2014-10-01

### Merged

- close websocket if proxyReq is closed before upgrade [`#708`](https://github.com/http-party/node-http-proxy/pull/708)

### Commits

- test closing upstream socket prior to upgrade [`7730548`](https://github.com/http-party/node-http-proxy/commit/77305489d9b88d283802477e155340e5dacfcc2c)
- [dist] Version bump. 1.5.2 [`43c6f0c`](https://github.com/http-party/node-http-proxy/commit/43c6f0c7c06d25a670c410500a8623531df458b1)

## [v1.5.1](https://github.com/http-party/node-http-proxy/compare/v1.5.0...v1.5.1) - 2014-09-30

### Commits

- [fix] do a check to make sure the server exists before we try and emit [`10a294a`](https://github.com/http-party/node-http-proxy/commit/10a294af4d4baac30b98ea9bec683a974443b83d)
- [dist] Version bump. 1.5.1 [`f0bf741`](https://github.com/http-party/node-http-proxy/commit/f0bf7418156db2cb87a616b0a34bb1f028db9142)

## [v1.5.0](https://github.com/http-party/node-http-proxy/compare/v1.4.3...v1.5.0) - 2014-09-30

### Merged

- exposing proxySocket on socket to support sniffing messages coming from proxy target [`#706`](https://github.com/http-party/node-http-proxy/pull/706)
- Fixed misleading documentation [`#705`](https://github.com/http-party/node-http-proxy/pull/705)
- Fix typo in README.md [`#702`](https://github.com/http-party/node-http-proxy/pull/702)
- handle 'upgrade' in comma-separated connection header [`#691`](https://github.com/http-party/node-http-proxy/pull/691)

### Commits

- test new detection of connection: upgrade [`ec683b9`](https://github.com/http-party/node-http-proxy/commit/ec683b924b1ef8cbdd2cd2bfb7e141b502773163)
- emitting proxySocket on proxyServer [`000eb53`](https://github.com/http-party/node-http-proxy/commit/000eb533de144cad01cfd97edf9ab6c350593d3c)
- [fix] perf optimization so we have a precompiled regexp [`c0a796b`](https://github.com/http-party/node-http-proxy/commit/c0a796b3e31de4f22eef00d93164e7238d9aa3ba)
- use regex to check for upgrade header [`65a21bc`](https://github.com/http-party/node-http-proxy/commit/65a21bce6dbbc6142a851dc959e237c0ef2b1091)
- [dist] Version bump. 1.5.0 [`232258b`](https://github.com/http-party/node-http-proxy/commit/232258b6ec2229497fe557454a121d917968f5e8)
- [minor] extra space [`e7d50b1`](https://github.com/http-party/node-http-proxy/commit/e7d50b1a376035a50c82db38605e99feb30afd36)

## [v1.4.3](https://github.com/http-party/node-http-proxy/compare/v1.4.2...v1.4.3) - 2014-09-12

### Merged

- Urgent: Fix breaking bug on url joining resulting in paths like `///path`. [`#699`](https://github.com/http-party/node-http-proxy/pull/699)

### Commits

- [minor] Added missing JSDoc comments [`73e8a4c`](https://github.com/http-party/node-http-proxy/commit/73e8a4cdd576868bf61d0848cc51f083a75454f9)
- Fix breaking bug on url joining resulting in paths like `///path`. [`73d865b`](https://github.com/http-party/node-http-proxy/commit/73d865bc9f8940f61c1ad4812f220920ead553b5)
- [minor] Code style adjustment. [`3ab6e95`](https://github.com/http-party/node-http-proxy/commit/3ab6e9591e66c203647605b4f275d374472c9d5f)
- Bump version v1.4.3 [`554f59c`](https://github.com/http-party/node-http-proxy/commit/554f59c5182d58b359df0159a29ff5ea35dd3830)
- [ignore] Ignore npm-debug.log [`a934cb6`](https://github.com/http-party/node-http-proxy/commit/a934cb6a46298c380e9bc794f18873576cf73c4c)

## [v1.4.2](https://github.com/http-party/node-http-proxy/compare/v1.4.1...v1.4.2) - 2014-09-12

### Commits

- [fix] ensure path works on windows because path.join doesnt like URLs [`ed73f06`](https://github.com/http-party/node-http-proxy/commit/ed73f06ed307ad2204e565781cc3154047941a8c)
- [dist] Version bump. 1.4.2 [`df12aeb`](https://github.com/http-party/node-http-proxy/commit/df12aeb12de79de1157898d45f4347fd0037dd70)

## [v1.4.1](https://github.com/http-party/node-http-proxy/compare/v1.3.1...v1.4.1) - 2014-09-11

### Merged

- Trimming contents of distributed npm package. [`#644`](https://github.com/http-party/node-http-proxy/pull/644)
- Remove changelog - it was not maintained [`#669`](https://github.com/http-party/node-http-proxy/pull/669)
- Removed duplicated imported dependencies [`#695`](https://github.com/http-party/node-http-proxy/pull/695)

### Commits

- [test] add test for prependPath option [`e44fabe`](https://github.com/http-party/node-http-proxy/commit/e44fabe58a233b367d42f26f15113e2022f71d7b)
- [api] add prependPath option to go with path change [`9a534c6`](https://github.com/http-party/node-http-proxy/commit/9a534c6ff63d776140918bc839801d247affd18d)
- [dist] Version bump. 1.4.1 [`d5c656b`](https://github.com/http-party/node-http-proxy/commit/d5c656bceb50dc9008ef223bc58b918adcf05352)
- [dist] Version bump. 1.4.0 [`dceef40`](https://github.com/http-party/node-http-proxy/commit/dceef407a1130033679e7e836c6753b76187ce5f)

## [v1.3.1](https://github.com/http-party/node-http-proxy/compare/v1.3.0...v1.3.1) - 2014-09-09

### Merged

- Allow proxy to maintain the original target path [`#693`](https://github.com/http-party/node-http-proxy/pull/693)
- Clarify usable parameters for 'proxyRes' event [`#686`](https://github.com/http-party/node-http-proxy/pull/686)

### Commits

- fix tests for maintaining proxy path [`a65021d`](https://github.com/http-party/node-http-proxy/commit/a65021d52b0ee039486819b5a95f442229458776)
- Fix proxy path [`511b7b3`](https://github.com/http-party/node-http-proxy/commit/511b7b3d4743636de9d9fbe8ff409730d221d273)
- Clarify usable parameters for proxyRes event. [`49a0de1`](https://github.com/http-party/node-http-proxy/commit/49a0de1e7cdcec9b555695605ab914038f99d66b)
- [dist] Version bump. 1.3.1 [`fc73828`](https://github.com/http-party/node-http-proxy/commit/fc73828035baf3cea3664560f8964f2a2a200d0a)
- [ci] remove 0.11.x to avoid failing builds caused by TLS errors [`6b83ae4`](https://github.com/http-party/node-http-proxy/commit/6b83ae47bbf2d5eab8ac94b4d6130e09a21ac85b)

## [v1.3.0](https://github.com/http-party/node-http-proxy/compare/v1.2.1...v1.3.0) - 2014-08-14

### Merged

- Added functionality to close proxy. [`#679`](https://github.com/http-party/node-http-proxy/pull/679)

### Commits

- [fix] cleanup and stylize close function [`261742a`](https://github.com/http-party/node-http-proxy/commit/261742a4295268ef93f45aa0f1e3a04208a2aed3)
- updated close function for safety [`8be9d94`](https://github.com/http-party/node-http-proxy/commit/8be9d945d03169056bbf84d702292b5763b015dc)
- [dist] Version bump. 1.3.0 [`05f0b89`](https://github.com/http-party/node-http-proxy/commit/05f0b891a610fb7779f90916fcd9ed750df818b2)

## [v1.2.1](https://github.com/http-party/node-http-proxy/compare/v1.2.0...v1.2.1) - 2014-08-14

### Commits

- Added close method to proxy server. [`a3d0219`](https://github.com/http-party/node-http-proxy/commit/a3d02196c5e62cd58bc0ebe8a66afcdb905d96b3)
- [fix] emit an error if proper URL is not passed in as a target [`37036dd`](https://github.com/http-party/node-http-proxy/commit/37036dd32565f72ad5777e47509293db18b60ed3)
- [dist] Version bump. 1.2.1 [`0a6b424`](https://github.com/http-party/node-http-proxy/commit/0a6b424e2c3b6cef68362a71f0e56740b2605af7)

## [v1.2.0](https://github.com/http-party/node-http-proxy/compare/v1.1.6...v1.2.0) - 2014-08-05

### Merged

- [api] Add event-based ability to modify pre-flight proxy requests. [`#673`](https://github.com/http-party/node-http-proxy/pull/673)

### Commits

- [dist] Version bump. 1.2.0 [`63c53a1`](https://github.com/http-party/node-http-proxy/commit/63c53a177217283ec14e4f7c2e891db48842ab4b)

## [v1.1.6](https://github.com/http-party/node-http-proxy/compare/v1.1.5...v1.1.6) - 2014-07-17

### Fixed

- do proper checking for a pass not existing. fixes #671 [`#671`](https://github.com/http-party/node-http-proxy/issues/671)

### Commits

- Remove changelog - it was not maintained [`e336b52`](https://github.com/http-party/node-http-proxy/commit/e336b52629276e647abeee300d7091db44e5b885)
- [dist] Version bump. 1.1.6 [`ed9e12b`](https://github.com/http-party/node-http-proxy/commit/ed9e12b0edb0fc206610e94bd696425619868474)

## [v1.1.5](https://github.com/http-party/node-http-proxy/compare/v1.1.4...v1.1.5) - 2014-07-10

### Merged

- Fix simple-balancer example [`#666`](https://github.com/http-party/node-http-proxy/pull/666)
- Added proxyTimeout option and two tests for timeout [`#658`](https://github.com/http-party/node-http-proxy/pull/658)

### Fixed

- Fix #657 [`#657`](https://github.com/http-party/node-http-proxy/issues/657)
- Fix #657 [`#657`](https://github.com/http-party/node-http-proxy/issues/657)

### Commits

- Added targetTimeout option and two tests for timeout [`0f24351`](https://github.com/http-party/node-http-proxy/commit/0f243516e1c6737b95fba220a5028439264b5de6)
- Change name targetTimeout to proxyTimeout [`7b79a74`](https://github.com/http-party/node-http-proxy/commit/7b79a7409ade7a8c79b2ae5761abc4843529063a)
- Trimming contents of distributed npm package. [`431aba7`](https://github.com/http-party/node-http-proxy/commit/431aba79d8d521e228c1403aaf4fd4a26fba03c3)
- [api] also emit the target on a proxy error [`d1baa36`](https://github.com/http-party/node-http-proxy/commit/d1baa3684e449610a2aae270816a7b8a907e588e)
- [dist] Version bump. 1.1.5 [`7104a7c`](https://github.com/http-party/node-http-proxy/commit/7104a7c023073a49091969f825738c79ae036123)
- fix balancer example [`9df4bc1`](https://github.com/http-party/node-http-proxy/commit/9df4bc1e1216a8e53675f0be16fb9081c11da225)

## [v1.1.4](https://github.com/http-party/node-http-proxy/compare/v1.1.3...v1.1.4) - 2014-05-11

### Merged

- `proxyRes` event, provide access to the req and res objects [`#642`](https://github.com/http-party/node-http-proxy/pull/642)

### Commits

- Add a test for the proxyRes event [`1385635`](https://github.com/http-party/node-http-proxy/commit/1385635e18f081af759c8e088f2f6b0219df83db)
- [dist] Version bump. 1.1.4 [`7cb98a4`](https://github.com/http-party/node-http-proxy/commit/7cb98a4e417312f01cf4432b52dbb3773aca60a0)
- Add the req and res objects to the proxyRes event [`1213e46`](https://github.com/http-party/node-http-proxy/commit/1213e46b1b0975ad1d5c5d0aaeace40a0811118f)

## [v1.1.3](https://github.com/http-party/node-http-proxy/compare/v1.1.2...v1.1.3) - 2014-05-11

### Merged

- Don't override connection header if Upgrading [`#640`](https://github.com/http-party/node-http-proxy/pull/640)

### Commits

- Adding test cases on preventing upgrade override [`8aa7c51`](https://github.com/http-party/node-http-proxy/commit/8aa7c519b15f734af7db34d2102781adbeae10aa)
- Update README.md for benchmarks [`4947484`](https://github.com/http-party/node-http-proxy/commit/4947484806f839d5e0a1b615b56a1bc847b8f534)
- [minor] style [`ccad177`](https://github.com/http-party/node-http-proxy/commit/ccad17795417de74bea2bcb6d6c559a4601af76d)
- [dist] Version bump. 1.1.3 [`c472527`](https://github.com/http-party/node-http-proxy/commit/c472527ea60da8b2f737d5742bc61ad2772b7e0b)

## [v1.1.2](https://github.com/http-party/node-http-proxy/compare/v1.1.1...v1.1.2) - 2014-04-14

### Commits

- [fix test] handle proxy error since we are properly aborting the proxy Request [`61c8734`](https://github.com/http-party/node-http-proxy/commit/61c8734e8b1115fab0e0db23fd8eeccbae61eee0)
- [fix] handle error on incoming request as well and properly abort proxy if client request is aborted [`77a1cff`](https://github.com/http-party/node-http-proxy/commit/77a1cff9bcf697eab27819eef054024bdc0a2ba3)
- [dist] Version bump. 1.1.2 [`c54278b`](https://github.com/http-party/node-http-proxy/commit/c54278bd3b00e82f4253393b6f6beb1d5a1b19e5)

## [v1.1.1](https://github.com/http-party/node-http-proxy/compare/v1.1.0...v1.1.1) - 2014-04-11

### Commits

- [dist] Version bump. 1.1.1 [`d908e2a`](https://github.com/http-party/node-http-proxy/commit/d908e2ad61013ed1f6e2f80c4b67a6dce7d0f504)
- [fix] let user make the decision on what to do with the buffer [`4f07dc2`](https://github.com/http-party/node-http-proxy/commit/4f07dc220d700ac90bd8405f7cb0724bdae4b430)

## [v1.1.0](https://github.com/http-party/node-http-proxy/compare/v1.0.3...v1.1.0) - 2014-04-09

### Merged

- Update UPGRADING.md [`#616`](https://github.com/http-party/node-http-proxy/pull/616)

### Fixed

- [fix] always be an eventemitter for consistency fixes #606 [`#606`](https://github.com/http-party/node-http-proxy/issues/606)

### Commits

- [api] emit a start an an end event [`8b48a9f`](https://github.com/http-party/node-http-proxy/commit/8b48a9fdab01624f7249c53f25919b1295eefb10)
- [dist] Version bump. 1.1.0 [`97ceeb3`](https://github.com/http-party/node-http-proxy/commit/97ceeb37d04e5d2195352365985165866323c4d7)
- [minor] missing angle bracket [`eca765a`](https://github.com/http-party/node-http-proxy/commit/eca765a856164c077ff9128949019552cdaf9a67)

## [v1.0.3](https://github.com/http-party/node-http-proxy/compare/v1.0.2...v1.0.3) - 2014-03-27

### Merged

- Fix for #591 [`#592`](https://github.com/http-party/node-http-proxy/pull/592)
- Add Repository field to package.json [`#578`](https://github.com/http-party/node-http-proxy/pull/578)
- Fix doc: option lines [`#575`](https://github.com/http-party/node-http-proxy/pull/575)

### Fixed

- [api] add toProxy method to allow absolute URLs to be sent when sending to another proxy fixes #603 [`#603`](https://github.com/http-party/node-http-proxy/issues/603)

### Commits

- [doc] update docs with toProxy option [`ece85b4`](https://github.com/http-party/node-http-proxy/commit/ece85b4e1ba379b3ed084bd8f606e285c14d4db3)
- [fix] set connection to CLOSE in cases where the agent is false. [`89a22bc`](https://github.com/http-party/node-http-proxy/commit/89a22bc00396f069eeb054ce30891a204077d16d)
- @xtreme-topher-bullock - update package.json to have proper repository key and formatting [`68fa17b`](https://github.com/http-party/node-http-proxy/commit/68fa17bbcaa73ae2d9539cba6f6ddff29f9e30d5)
- [dist] Version bump. 1.0.3 [`07fceb7`](https://github.com/http-party/node-http-proxy/commit/07fceb7c7aed25a8991d0295db4b4a7e50d79cf9)
- Add support for localAddress [`e633b0f`](https://github.com/http-party/node-http-proxy/commit/e633b0f7e4fd719d809eaeb4725e589f79c271ab)

## [v1.0.2](https://github.com/http-party/node-http-proxy/compare/v1.0.1...v1.0.2) - 2014-01-28

### Merged

- Update README.md [`#566`](https://github.com/http-party/node-http-proxy/pull/566)
- Fix argument order for ws stream pass [`#560`](https://github.com/http-party/node-http-proxy/pull/560)
- Extend listen to enable IPv6 support. [`#558`](https://github.com/http-party/node-http-proxy/pull/558)
- Fix before and after type check [`#556`](https://github.com/http-party/node-http-proxy/pull/556)

### Fixed

- Close outgoing ws if incoming ws emits error [`#559`](https://github.com/http-party/node-http-proxy/issues/559)
- [fix] closes #555 [`#555`](https://github.com/http-party/node-http-proxy/issues/555)

### Commits

- [fix] replicate node core behavior and throw an error if the user does not add their own error listener [`daad470`](https://github.com/http-party/node-http-proxy/commit/daad4703f3a80014936c89f4d67affdc3246f478)
- [dist] Version bump. 1.0.2 [`4bdc3e4`](https://github.com/http-party/node-http-proxy/commit/4bdc3e4f455b2749c03961404db74e3112a3e9e8)
- [doc] Fix broken image in npm by using an absolute link [`8004f4e`](https://github.com/http-party/node-http-proxy/commit/8004f4e5fc0f535806e92ec4e1bd973a45367dac)

## [v1.0.1](https://github.com/http-party/node-http-proxy/compare/v1.0.0...v1.0.1) - 2014-01-17

### Fixed

- [fix] closes #553 [`#553`](https://github.com/http-party/node-http-proxy/issues/553)

### Commits

- [dist] bump v1.0.1 [`68c5512`](https://github.com/http-party/node-http-proxy/commit/68c55123039369cdf8a55a64b36b719c96b672cf)
- typo [`689459f`](https://github.com/http-party/node-http-proxy/commit/689459fe46885a1b3b8e32a4df55f2d1339143e5)

## [v1.0.0](https://github.com/http-party/node-http-proxy/compare/v0.10.4...v1.0.0) - 2014-01-16

### Merged

- Http proxy 1.0 [`#552`](https://github.com/http-party/node-http-proxy/pull/552)
- Caronte [`#551`](https://github.com/http-party/node-http-proxy/pull/551)
- Only emit response if a valid server is present [`#549`](https://github.com/http-party/node-http-proxy/pull/549)
- [fix] add `type` to before and after to grab correct `passes`, fixes #537 [`#539`](https://github.com/http-party/node-http-proxy/pull/539)
- export the proxy itself from the main require [`#536`](https://github.com/http-party/node-http-proxy/pull/536)

### Fixed

- [fix] closes #547 [`#547`](https://github.com/http-party/node-http-proxy/issues/547)
- Merge pull request #539 from nodejitsu/fix-before-after [`#537`](https://github.com/http-party/node-http-proxy/issues/537)
- [fix] add `type` to before and after to grab correct `passes`, fixes #537 [`#537`](https://github.com/http-party/node-http-proxy/issues/537)

### Commits

- [nuke] old files [`a4ee8f9`](https://github.com/http-party/node-http-proxy/commit/a4ee8f9d82f71ef423c401b1f5e9f712b13cbc98)
- [docs] upgrade UPGRADING.md [`e599151`](https://github.com/http-party/node-http-proxy/commit/e5991519dbc7838aa4b8aeb5077d1c1ec5a13813)
- [api] export the httpProxy.Server as the main export but preserve the createServer factory [`182c76c`](https://github.com/http-party/node-http-proxy/commit/182c76cd2322d4d4c041c2a964d51db396c5c96b)
- [fix] remove caronte [`d6d2d0c`](https://github.com/http-party/node-http-proxy/commit/d6d2d0c8821bba9888eee7c3881fc408b3b2008e)
- [fix] ee3 error handling [`d23353d`](https://github.com/http-party/node-http-proxy/commit/d23353d980d8aa1b2606e3d36a83d27432952bef)
- [fix] comments [`6fa23e1`](https://github.com/http-party/node-http-proxy/commit/6fa23e11f6dc0b9c09766b268611ade919bfaa08)

## [v0.10.4](https://github.com/http-party/node-http-proxy/compare/v0.10.3...v0.10.4) - 2013-12-27

### Merged

- Update README.md [`#521`](https://github.com/http-party/node-http-proxy/pull/521)
- Better examples [`#520`](https://github.com/http-party/node-http-proxy/pull/520)
- Send path in req.path and not the url [`#416`](https://github.com/http-party/node-http-proxy/pull/416)
- Fix websocket error handing [`#518`](https://github.com/http-party/node-http-proxy/pull/518)
- attempting to fix links to 2 source locations in README.md [`#502`](https://github.com/http-party/node-http-proxy/pull/502)
- [merge] rename codename to actual project name [`#492`](https://github.com/http-party/node-http-proxy/pull/492)
- [merge] Added error handling example [`#484`](https://github.com/http-party/node-http-proxy/pull/484)
- [merge] https & agent [`#482`](https://github.com/http-party/node-http-proxy/pull/482)
- [merge] caronte tests [`#476`](https://github.com/http-party/node-http-proxy/pull/476)
- FIX: ws error event [`#475`](https://github.com/http-party/node-http-proxy/pull/475)
- Fix accidental write to global variable. [`#472`](https://github.com/http-party/node-http-proxy/pull/472)
- [fix] 2 spelling mistakes [`#14`](https://github.com/http-party/node-http-proxy/pull/14)
- [fix] add ability to proxy websockets over HTTPS [`#11`](https://github.com/http-party/node-http-proxy/pull/11)
- Tests [`#3`](https://github.com/http-party/node-http-proxy/pull/3)

### Fixed

- determine x-forwarded-port from host header [`#341`](https://github.com/http-party/node-http-proxy/issues/341)
- [fix] closes #529 [`#529`](https://github.com/http-party/node-http-proxy/issues/529)
- [fix] fixes #341 [`#341`](https://github.com/http-party/node-http-proxy/issues/341)
- [tests] https test pass, fix #511. Exposed the rejectUnauthorized flag [`#511`](https://github.com/http-party/node-http-proxy/issues/511)
- [fix] pass proper options object that extend the global options and parse the per proxy args into options. fixes #510 [`#510`](https://github.com/http-party/node-http-proxy/issues/510)
- [readme] add links to badges on readme, fix #483 [`#483`](https://github.com/http-party/node-http-proxy/issues/483)
- [fix] pooled connections, closes #478 [`#478`](https://github.com/http-party/node-http-proxy/issues/478)
- [fix] add 0.10 link, fixes #459 [`#459`](https://github.com/http-party/node-http-proxy/issues/459)
- [fix] closes #473 [`#473`](https://github.com/http-party/node-http-proxy/issues/473)
- [fix] add 0.10 compatibily.. closes #474 [`#474`](https://github.com/http-party/node-http-proxy/issues/474)
- [fix] headers, closes #469 [`#469`](https://github.com/http-party/node-http-proxy/issues/469)
- [fix] headers, fixes #467 [`#467`](https://github.com/http-party/node-http-proxy/issues/467)
- [fix] yawnt baaaka .. fixes #8 [`#8`](https://github.com/http-party/node-http-proxy/issues/8)

### Commits

- [fix] more jshint intendation [`17399e7`](https://github.com/http-party/node-http-proxy/commit/17399e7c3ef9addf9dd8f7c628b273e693f128a1)
- [fix] tests [`a255f98`](https://github.com/http-party/node-http-proxy/commit/a255f984fecf24c9290f3ad58d1b68e54a7509eb)
- [minor] remove coverage [`335af81`](https://github.com/http-party/node-http-proxy/commit/335af81d0244e62ecb501690bd15bc5a04ec51a3)
- [examples] updated websockets examples [`ed8c9ee`](https://github.com/http-party/node-http-proxy/commit/ed8c9eeba99d60f39f5c36c4f34ed1a781d2cfd8)
- [tests] removed unused tests [`7e25bde`](https://github.com/http-party/node-http-proxy/commit/7e25bded27effc1b3d47121ce21465a4e2ec7c0b)
- [tests] Added a test case for run all the examples [`bc236d7`](https://github.com/http-party/node-http-proxy/commit/bc236d7e95ef10bc17cf551eea2cd2fb9bf265eb)
- [tests] drop the test of own streams, moved the usable tests [`dc9d7e5`](https://github.com/http-party/node-http-proxy/commit/dc9d7e5452c7d39ae1d242cb8021ca75e4f736d4)
- [fix] default port [`d166354`](https://github.com/http-party/node-http-proxy/commit/d1663549ec070e7ae8bc45ffb148f40ee903192f)
- [tests] added the ws passes test and the streams webscokets test [`8b3fe32`](https://github.com/http-party/node-http-proxy/commit/8b3fe32f6ae60ae067bc5e40cdc43015e689467f)
- [refactor minor] s/caronte/http-proxy/ or s/caronte/httpProxy/ where appropriate. [`bb0d28c`](https://github.com/http-party/node-http-proxy/commit/bb0d28c58729e2cc70e8446f7fbf1113a6fa9310)
- [examples] updated bodyDecoder middleware example [`c82ff2c`](https://github.com/http-party/node-http-proxy/commit/c82ff2c3c0c0165421fbc4e7e94fa3f59d59aa38)
- [dist] first [`4d13156`](https://github.com/http-party/node-http-proxy/commit/4d131567211bcefc6ef0b0592d374fef7bd5abd8)
- [examples] update forward and custom error examples [`b726116`](https://github.com/http-party/node-http-proxy/commit/b7261161343c3471201d6de36ba1030aced26425)
- [refactor docs] add descriptions [`d05af4a`](https://github.com/http-party/node-http-proxy/commit/d05af4af60a5f3d308aa68bf09ab0cf9e5528c52)
- [tests] make the tests run with the last refactor [`5bb83b9`](https://github.com/http-party/node-http-proxy/commit/5bb83b967edb514402698eecfe3db7ab5fe60b06)
- [examples] deleted this examples [`bdeabb7`](https://github.com/http-party/node-http-proxy/commit/bdeabb767a537bcb9f98ef74f6efe9762a9b1c34)
- websocket draft [`07551c6`](https://github.com/http-party/node-http-proxy/commit/07551c63e428551e5d6e52362efd9620a14c71b4)
- [fix] naming [`2a59366`](https://github.com/http-party/node-http-proxy/commit/2a593664a5768c90d9b2edf4c298460416b38926)
- [dist doc] Added documentation for consistent benchmarking of node-http-proxy [`f7f5fa7`](https://github.com/http-party/node-http-proxy/commit/f7f5fa727e8f1d3f4946e61ad03830dab1da01a5)
- [examples] update old examples [`7e44d36`](https://github.com/http-party/node-http-proxy/commit/7e44d3669bbd1b13e6452f265d52b22396f68b5d)
- [docs] more short examples to the Readme [`0393b5d`](https://github.com/http-party/node-http-proxy/commit/0393b5da990bb45e873bb80d87a0bc9e4dd6a477)
- [examples] updated old proxy examples [`e02317c`](https://github.com/http-party/node-http-proxy/commit/e02317ce86ff2dabd496cf7e2741e219a22ac817)
- [wip] Initial HTTPS-&gt;HTTP test, updated https-secure example. Work in progress, need to add more https tests [`33a2462`](https://github.com/http-party/node-http-proxy/commit/33a2462d28c7d1fa26b03bcf290242ff7cd83e7a)
- [docs] readme [`886a870`](https://github.com/http-party/node-http-proxy/commit/886a8707078f59d0467b34686455bb5bdfadbc0c)
- [examples] added error-handling using callbacks and HTTP-to-HTTPS examples [`d7064f2`](https://github.com/http-party/node-http-proxy/commit/d7064f2e1e149fe870cbb158932cb99f9f192fce)
- [examples] updated old examples [`588327c`](https://github.com/http-party/node-http-proxy/commit/588327c2c4392618b515164989f08ef20a30842b)
- stuff [`e45bfd6`](https://github.com/http-party/node-http-proxy/commit/e45bfd66a21a2470c5a4a4cc1d6095494bbc0f6b)
- [doc] added some documentation to functions and comments to understand better the code [`5dcdf2b`](https://github.com/http-party/node-http-proxy/commit/5dcdf2b36c24a9584f044b7529265b9ac861d8c7)
- Fixed issue where error callback would not invoke, including new test cases. Added req/res values to error events. [`0bfb9be`](https://github.com/http-party/node-http-proxy/commit/0bfb9be418926f2113489e92504038127d4c04bb)
- [examples] updated balancer examples [`831a44b`](https://github.com/http-party/node-http-proxy/commit/831a44b3c8c3acf6c046c47703a07cd6362a0d1c)
- socket.io stuff [`a74cd85`](https://github.com/http-party/node-http-proxy/commit/a74cd85c8a5aae2851acf7139648fefd6a02a57b)
- [tests] move contributions of @mmoulton to correct place [`7c72f3b`](https://github.com/http-party/node-http-proxy/commit/7c72f3b407a084a896e420c23ababc3e9357feca)
- [tests] this file is not necessary anymore [`881c7e6`](https://github.com/http-party/node-http-proxy/commit/881c7e62e0bef7b4b9f81b6fd121f7ad6641bd77)
- [refactor] move to leaner architecture [`8273cb6`](https://github.com/http-party/node-http-proxy/commit/8273cb6461e4d33f36e583b0354d1bea038d0a56)
- [fix] remove trailing whitespaces [`0aeaba7`](https://github.com/http-party/node-http-proxy/commit/0aeaba7fe6c51f150d0322eb90a77c1701ed88f5)
- [test] added tests for web-outgoing.js [`16a4d9d`](https://github.com/http-party/node-http-proxy/commit/16a4d9da1136b79f40ad80482d3fd17dc74274b1)
- [fix] some stuff start debugging proxystream [`d4f0da8`](https://github.com/http-party/node-http-proxy/commit/d4f0da898e5e8a2d6740e50a7fc34576435e1132)
- [tests] now each test use a different port to avoid some slow opening and closing ports [`c75d06c`](https://github.com/http-party/node-http-proxy/commit/c75d06c5f92eb7c814deb49bb33cf9fffc632d97)
- [tests] fixed inherits problem and listen for the correct event [`c65ffbb`](https://github.com/http-party/node-http-proxy/commit/c65ffbb976467dc1768983dcffe111d18e8f2db1)
- [fix] ProxyStraem now works [`356f43d`](https://github.com/http-party/node-http-proxy/commit/356f43d719998d135e0fc404ac8508e330cf1e5b)
- [examples] fix the copyright header of example files [`e592c53`](https://github.com/http-party/node-http-proxy/commit/e592c53d1a23b7920d603a9e9ac294fc0e841f6d)
- [feature] start working on the new server [`b79bd29`](https://github.com/http-party/node-http-proxy/commit/b79bd29d5e984f34b9c07fbdc803aed83b3fd0bb)
- ENH: updated examples [`f566a42`](https://github.com/http-party/node-http-proxy/commit/f566a42e511f4a6a8f3620f64e05df209e61b64f)
- [examples] add example of gzip using the connect.compress() middleware [`2142c50`](https://github.com/http-party/node-http-proxy/commit/2142c506e08f56d52e1995da5506c3e032f19c3c)
- [fix] refactor error handling [`601dbcb`](https://github.com/http-party/node-http-proxy/commit/601dbcbfe929af31995568b4f36b877245809058)
- [tests] fixed according new refactor and added test to common.setupSocket() [`1cb967b`](https://github.com/http-party/node-http-proxy/commit/1cb967b90aaa5b9da57727b8acbd95108437797a)
- [feature] websocket support [`79a14ac`](https://github.com/http-party/node-http-proxy/commit/79a14acfd2b2bf03f5ae2b334e7a37e619da6bb9)
- keepalive sockets [`dad211e`](https://github.com/http-party/node-http-proxy/commit/dad211e71c9ac3b32eba1ea3755edb688053b9d3)
- [tests] Using target field, tests now pass. We are missing the tests using forward field [`8085178`](https://github.com/http-party/node-http-proxy/commit/8085178dc2c24567adfb872a583863709ce60b5b)
- [fix] callback as optional error handler [`c7924e0`](https://github.com/http-party/node-http-proxy/commit/c7924e01f92aeec07333273f0882c1dd5e9521ae)
- ENH: added new https example, needs to be simplified before merge [`427d8d8`](https://github.com/http-party/node-http-proxy/commit/427d8d85369b0cd1d38afa0dd0f28ac98fa16001)
- [test] proxystream test [`c961279`](https://github.com/http-party/node-http-proxy/commit/c9612798f1207a4c40b616608bf6274d79ad0e4d)
- [lib] initial draft to websockets passes [`79f7f99`](https://github.com/http-party/node-http-proxy/commit/79f7f99528661162ae4153856888f078f666e017)
- [fix] minor [`7599cee`](https://github.com/http-party/node-http-proxy/commit/7599cee3fd03a5ce645e313f35557a41c9ac1aee)
- [tests] added HTTPS to HTTPS test [`31d919b`](https://github.com/http-party/node-http-proxy/commit/31d919b0a3d0b7f574e88fc5eed093c6b1a53548)
- [feature] started working on error propagation, kinda sucks, gotta think it over [`9ab8749`](https://github.com/http-party/node-http-proxy/commit/9ab8749a9bec33b49c495975e8364336ad7be1a3)
- [test] testing the onResponse proxy method [`27df8d7`](https://github.com/http-party/node-http-proxy/commit/27df8d72ad86d02cfce00a6e5c183d93dd50f97e)
- [fix] remove duplicate [`10c0f11`](https://github.com/http-party/node-http-proxy/commit/10c0f11b68e39552051e508c7bf20d65d2d59177)
- [tests] add more tests [`cedc5c4`](https://github.com/http-party/node-http-proxy/commit/cedc5c4bd2059585e1222ec4f03f09e8bcc808fc)
- [docs] Update readme with more how to [`ae0faef`](https://github.com/http-party/node-http-proxy/commit/ae0faef5aa0080d742a9740f9cb38bfd54b7d97e)
- [tests] added test for socket.io proxying [`10a0db4`](https://github.com/http-party/node-http-proxy/commit/10a0db4f0dd4594839f9098b9d67130085a067bc)
- [tests] added test HTTPS to HTTP using own server [`bbe3bfd`](https://github.com/http-party/node-http-proxy/commit/bbe3bfdf98255b82a185a798ff9f29e74615b6ca)
- [examples] update the error-handling example using the new error handle way [`a1b25a1`](https://github.com/http-party/node-http-proxy/commit/a1b25a123b4ff71e731f9beb27c5e078acfead65)
- [fix] quote [`c4ddc4e`](https://github.com/http-party/node-http-proxy/commit/c4ddc4edd324d9910a11eea14561a0e3b953f29c)
- ENH: updated README and added examples file. [`07091b5`](https://github.com/http-party/node-http-proxy/commit/07091b5077a40dfee29f6fd33ecb38d3fa25b801)
- [test] passes/web.js (first 2 funcs) [`d40e4be`](https://github.com/http-party/node-http-proxy/commit/d40e4beb62381b962b6cf3254451de0a39f182b1)
- [test] add test for forwardstream [`8fc3389`](https://github.com/http-party/node-http-proxy/commit/8fc33893672d26013c2b2ff396b777bcf1751527)
- [tests] fixing tests, fixed some typos and changed how passes are stored [`a704213`](https://github.com/http-party/node-http-proxy/commit/a7042132c881656dd32f915d9b0b962f0ef92efb)
- [test] added the lib/caronte/streams/forward.js initial test, one test pending [`2fac7b9`](https://github.com/http-party/node-http-proxy/commit/2fac7b9b009b12a940efb22de3af6db55ee686a9)
- [api] add draft for proxystream [`4f24664`](https://github.com/http-party/node-http-proxy/commit/4f24664e8a50aa9b9a3ea155d067b85f94a8c81b)
- [experiment] new api for proxying [`07cfa6b`](https://github.com/http-party/node-http-proxy/commit/07cfa6b981ff54d8d96eea6c9aa4b560ee3867ec)
- [tests] the options got a problem and this test probe that timeout is not being set [`1d1ee88`](https://github.com/http-party/node-http-proxy/commit/1d1ee8858283d7c8984f1c1d6c5185b6822f9235)
- new error propagation [`3a39e44`](https://github.com/http-party/node-http-proxy/commit/3a39e444ff68a74f6b586f0736bbd3f8a2511ca5)
- [fix] docs [`ec981c5`](https://github.com/http-party/node-http-proxy/commit/ec981c5b74bf43dd36c8ca89833b751f59f01d38)
- [examples] added concurrent proxy example [`04c1011`](https://github.com/http-party/node-http-proxy/commit/04c10113f7a3b568fb95b18f30e4aca3e059d961)
- [fix] closes number #487 [`cde08fb`](https://github.com/http-party/node-http-proxy/commit/cde08fb2ee2df03c9457678d8e6776a5d89165b2)
- [test] started writing tests [`16eacfa`](https://github.com/http-party/node-http-proxy/commit/16eacfa961d2a2d80534e95eba83010ed6ab01b4)
- [tests] added tests for websockets [`02007ed`](https://github.com/http-party/node-http-proxy/commit/02007ed0fb38f798436ae5669bb18d4f27496667)
- Revert "[fix] fixed options and server reference to can access them from passes functions" [`babdf53`](https://github.com/http-party/node-http-proxy/commit/babdf531fecd32f9af0963902909fcfa2cd374f1)
- mm test file [`1a7bef0`](https://github.com/http-party/node-http-proxy/commit/1a7bef0cda58243416a263075dc6eb51f22b6dec)
- [fix] fixed options and server reference to can access them from passes functions [`90fb01d`](https://github.com/http-party/node-http-proxy/commit/90fb01d38ac5af7ef395547b24e985b6f63b4abc)
- [examples] added forward example [`7a3f6df`](https://github.com/http-party/node-http-proxy/commit/7a3f6dfbcc80ba32fa81004438c637e8d29eb029)
- [docs] add UPGRADING.md [`db12f6c`](https://github.com/http-party/node-http-proxy/commit/db12f6c24e22c034c698457cc28ff60c990b55a5)
- DOC: Added error handling example [`32a4088`](https://github.com/http-party/node-http-proxy/commit/32a40889cedfd6b0d92224aa921700a7b7271c68)
- [examples] updated the modifyResponse-middleware example [`de3ff11`](https://github.com/http-party/node-http-proxy/commit/de3ff11656b4a847de3a63b28feed39b6c816480)
- [test] test onError part, proxying to no where [`b85aa16`](https://github.com/http-party/node-http-proxy/commit/b85aa16e75401a223a947cde444d42cf7eeafb67)
- ENH: updated agent options in `common.setupOutgoing` [`12cda56`](https://github.com/http-party/node-http-proxy/commit/12cda561afe534427a5f84da9d7e0beb64a8ecbc)
- [fix] minor and short fixes [`e0faaaf`](https://github.com/http-party/node-http-proxy/commit/e0faaaf81152203b96f0313c68706468e7ee7357)
- support websockets [`4a4607d`](https://github.com/http-party/node-http-proxy/commit/4a4607d075a912746386d1751fd6b0fc98cf6b20)
- [test] COVERAGE [`004a46c`](https://github.com/http-party/node-http-proxy/commit/004a46c09df2f0f7b15d8e8f7119bc6039e0c01c)
- [misc] add a LICENSE file [`584ce76`](https://github.com/http-party/node-http-proxy/commit/584ce76e7576c906e25cdd04a2e079f97bcf86ff)
- ENH: updated https and agent option [`13741a8`](https://github.com/http-party/node-http-proxy/commit/13741a823f1c1c884d4a37e597e4b188598b0e25)
- [fix] write connection header [`2c10f25`](https://github.com/http-party/node-http-proxy/commit/2c10f256b658bc0e906c20f29d94ab7eaf653055)
- [fix] merge #495, thanks @glasser [`d0862af`](https://github.com/http-party/node-http-proxy/commit/d0862aff0c693366dcb11649b6abe1d011268953)
- support forward [`8c8c455`](https://github.com/http-party/node-http-proxy/commit/8c8c455541f21ad9a9ac7ca19d1f37368206a2e2)
- [tests] fix tests set correct host headers [`cfd417d`](https://github.com/http-party/node-http-proxy/commit/cfd417de2352b0f05535b979dc15abff60c1fb96)
- [fix] Optimize fix for `x-forwarded-for-port`. [`2d42709`](https://github.com/http-party/node-http-proxy/commit/2d42709c3283637de16a49e815b03e63432bbd29)
- ENH: updated readme with an example [`edd8e2f`](https://github.com/http-party/node-http-proxy/commit/edd8e2f04e4b39391b062fa6437d61b4ebde8748)
- [doc] update README.md [`dcb873a`](https://github.com/http-party/node-http-proxy/commit/dcb873ad9992b1534615d59b8a0a70e8b87d7884)
- [test] passes/web.js XHeaders func [`c02b721`](https://github.com/http-party/node-http-proxy/commit/c02b721321c455bc287c3fed6b9b21392ce2fc70)
- [fix] fixed passes functions, now 'this' can be used and options are stored on 'this.options' [`9b3e1eb`](https://github.com/http-party/node-http-proxy/commit/9b3e1eb247df29d18ea299ff4ebb2f10eeb71269)
- Revert "[fix] fixed passes functions, now 'this' can be used and options are stored on 'this.options'" [`5e130de`](https://github.com/http-party/node-http-proxy/commit/5e130de8548ad41b821da49299b4fd1c9536c5f0)
- [minor] Remove duplicate dependencies and cleanup of the scripts [`a51b062`](https://github.com/http-party/node-http-proxy/commit/a51b0622780f48160001f9e74340f7d720cbfce6)
- TEST: added agent and header tests [`39b0c46`](https://github.com/http-party/node-http-proxy/commit/39b0c46a6967fda5329760ad93a8ec01bc4a6f14)
- [examples] fix styling and bad spaces [`6a6dfbb`](https://github.com/http-party/node-http-proxy/commit/6a6dfbb79dc156679f75dd519344d19a5b61613b)
- ENH: added error events [`1b867a7`](https://github.com/http-party/node-http-proxy/commit/1b867a7f594f7dfe49fc17ff53451a353ec509d9)
- [test] remove chunked on http1.0 [`ca09263`](https://github.com/http-party/node-http-proxy/commit/ca092635e7ac4d967b554e3b94a16a931946d464)
- [tests] fix test to use the new way to pass options [`52ecd52`](https://github.com/http-party/node-http-proxy/commit/52ecd52ee5aa78603e44ba8d5ff9187410351622)
- [examples] fixed https examples [`a467b7b`](https://github.com/http-party/node-http-proxy/commit/a467b7b4a9614a7cbfdc256524e1495616e3d4d9)
- Revert "[tests] fix test to use the new way to pass options" [`2bf20d6`](https://github.com/http-party/node-http-proxy/commit/2bf20d61d53201e9820c5f9215e641fcf88f5172)
- [fix] better code [`3d8e538`](https://github.com/http-party/node-http-proxy/commit/3d8e5383cd9d527825f95d9071a87865fcebca05)
- [feature] implement _write and _read [`6a4294c`](https://github.com/http-party/node-http-proxy/commit/6a4294cbdfe85fa162969b1393032adc9d418441)
- [fix] use the correct arguments order [`cc09ae6`](https://github.com/http-party/node-http-proxy/commit/cc09ae6a345cfde1689e1d8731c5822675c59d4d)
- [fix] fix the correct order of arguments in ws-incoming passes [`02df9a3`](https://github.com/http-party/node-http-proxy/commit/02df9a33c5cce17ea32a892017acbe5ce57ab2e5)
- [fix] write status [`e08d4ed`](https://github.com/http-party/node-http-proxy/commit/e08d4edad339d0f7f55900b3e6e6a0e770960215)
- [fix] finished jshint fixes [`455f97e`](https://github.com/http-party/node-http-proxy/commit/455f97e14cb4929e0a3a5c746471e9c5e76436fc)
- Update the README to describe middleware err handler. [`25bb3bf`](https://github.com/http-party/node-http-proxy/commit/25bb3bfa7012e0f975e10f0311cae8c39183fa41)
- Prevent headers to be sent twice [`8332e74`](https://github.com/http-party/node-http-proxy/commit/8332e744202ed9de94288d8f1c822cd9fe788983)
- [examples] added package.json with the dependencies needed by examples [`d85ccdd`](https://github.com/http-party/node-http-proxy/commit/d85ccdd333edcfc7551bcf8e0ffd7dc166e38e61)
- [tests] added .travis.yml file [`0602500`](https://github.com/http-party/node-http-proxy/commit/06025002303f351f71d9e5f78a93895257f0d283)
- [dist minor] 2 space indents next time @samalba [`7e8041d`](https://github.com/http-party/node-http-proxy/commit/7e8041d2b687b8375a1d0fe45270029c6e8ddee6)
- [fix] naming [`8931009`](https://github.com/http-party/node-http-proxy/commit/893100972c22febbf133134394bc0bcef47d9e12)
- Fix for #458. Host header may cause some sites not to be proxyable with changeOrigin enabled [`781c038`](https://github.com/http-party/node-http-proxy/commit/781c038f2b4d14a01cc9297e1e0dba6ce39dd6cb)
- [docs] typos, typos everywhere... [`03880d8`](https://github.com/http-party/node-http-proxy/commit/03880d8d069e9e17ca7d7aea6eb760f6626a869c)
- ENH: updated `ws` and `web` functions to use the global options object as a base [`268afe3`](https://github.com/http-party/node-http-proxy/commit/268afe34bb51448d511c9cd73c03e97d1c1baee0)
- [fix] make @mmalecki a happy camper [`c9cd6d2`](https://github.com/http-party/node-http-proxy/commit/c9cd6d2ad324e0e6222932c8f29f27621071e045)
- write [`f97c0c6`](https://github.com/http-party/node-http-proxy/commit/f97c0c6167371c5ff92e6361b1df02e3fd5506d7)
- [fix] [`a9f9e21`](https://github.com/http-party/node-http-proxy/commit/a9f9e21eda2f8e912523e6b62abb0101c0353505)
- [fix] coveralls.. will it work? [`f36cb4d`](https://github.com/http-party/node-http-proxy/commit/f36cb4d5a110fc86272e878278f103f313c86f56)
- ENH: updated target and forward options so that a string may be specified [`ef946a7`](https://github.com/http-party/node-http-proxy/commit/ef946a7697b38b13178881b3d1ebde63681dd4a1)
- added option for eventlistenerCount(max) [`8eb6780`](https://github.com/http-party/node-http-proxy/commit/8eb6780f8705caff13a5375446539b0621d497d7)
- [fix] support buffer [`1204a35`](https://github.com/http-party/node-http-proxy/commit/1204a35e467c6c1855ba0dac8f55d79f899148a6)
- DOC: updated readme with options [`1b5fb1d`](https://github.com/http-party/node-http-proxy/commit/1b5fb1d8fc21421b8383919d93e4149b586b211b)
- ENH: added 'headers' to available options, to add or overwrite existing headers [`7d840d3`](https://github.com/http-party/node-http-proxy/commit/7d840d35151be1aac612798754af47368594781d)
- [fix] move logo [`57abb7f`](https://github.com/http-party/node-http-proxy/commit/57abb7f26c14e281c3be07a8b84e3c79e066f59f)
- FIX: tests. still need to add more tests tho [`a350fad`](https://github.com/http-party/node-http-proxy/commit/a350fadea6bace293131581487f8c66948009449)
- [fix] move logo [`aaff196`](https://github.com/http-party/node-http-proxy/commit/aaff1966e4e2eb42c9890e57737f57a64e8d964a)
- [docs] add travis build status [`6b61878`](https://github.com/http-party/node-http-proxy/commit/6b618787598a2a37850898dbdb3b4fe8f3c3414d)
- [fix] do not send chunked responses to http1.0 clients [`8663ac1`](https://github.com/http-party/node-http-proxy/commit/8663ac1c43505f0081d906c3cd8e702d4b5ddeb0)
- [dist] Bump dependencies. [`a81dd8d`](https://github.com/http-party/node-http-proxy/commit/a81dd8d53e1595cba9acf5cc3ca9517165dcc4aa)
- [fix] readme [`4d3a4e1`](https://github.com/http-party/node-http-proxy/commit/4d3a4e1ee7370347898d1863ab73aa68ed345d8d)
- [fix] proxying to https [`26c4c43`](https://github.com/http-party/node-http-proxy/commit/26c4c43a06263ec6721bc0e8a90644297d0cf217)
- [fix] new logo [`ee3cc38`](https://github.com/http-party/node-http-proxy/commit/ee3cc380665a31ec6af28ddb73dfc543f430d3f8)
- [fix] naming convention [`7d71a86`](https://github.com/http-party/node-http-proxy/commit/7d71a867a8bdc375f7577cec3905cca89bbf415c)
- fix docs [`9243444`](https://github.com/http-party/node-http-proxy/commit/9243444ac006f73c00b0f1f78c4a77f342b0b4e4)
- [fix] short circuit [`a6256ca`](https://github.com/http-party/node-http-proxy/commit/a6256cac1df1739e3da78fe5f0cf122ef7ce6b14)
- [tests] this test is already in web-incoming tests [`920f1e7`](https://github.com/http-party/node-http-proxy/commit/920f1e7707aa1751577533cd368529f8a704d7af)
- Emit middlewareError when on middleware error. [`bc12ca3`](https://github.com/http-party/node-http-proxy/commit/bc12ca39394f9aeed3e3047f59035ba48afa2885)
- DOC: updated readme [`7ad5c0f`](https://github.com/http-party/node-http-proxy/commit/7ad5c0f993294c9e2e7650e15fbc62d11a2cb062)
- [docs] add logo [`8b05626`](https://github.com/http-party/node-http-proxy/commit/8b05626eed5e45e72cf9b1f14a4c4dca1dd2ed0f)
- [fix] making @stoke a happy camper [`34f16e7`](https://github.com/http-party/node-http-proxy/commit/34f16e74647095199f84ab61e10c8dafd60b505a)
- [feature] add buffer support [`e3f8d5f`](https://github.com/http-party/node-http-proxy/commit/e3f8d5fdbe1ebc4f04188d95bbef768d09718d2c)
- [Fix] 2 spelling mistakes [`5823842`](https://github.com/http-party/node-http-proxy/commit/58238421945bcc4236e280ebca7799b831ae29a4)
- [fix] do not call .end [`6e77cd3`](https://github.com/http-party/node-http-proxy/commit/6e77cd390929842088ae9f6deb922a6627ddfecd)
- attempting to fix link to valid options properties [`bbe2b27`](https://github.com/http-party/node-http-proxy/commit/bbe2b2788a7ee3c74fd44fe88b6dcf213264436f)
- [fix] slimmer proxying [`031aa0f`](https://github.com/http-party/node-http-proxy/commit/031aa0fbf30bd377696c4efa508f6fc769bf1070)
- [fix] use agent pool [`abf1d90`](https://github.com/http-party/node-http-proxy/commit/abf1d90fdf05a17ebe05a3e90d464a592e0aee69)
- [tests] fix test using undefined url [`c4d56a5`](https://github.com/http-party/node-http-proxy/commit/c4d56a5faf1e89cdeb911f0ece0efe065eb58c45)
- [fix] legacy [`162a42f`](https://github.com/http-party/node-http-proxy/commit/162a42f58f515c5418ccfac0b68f4c928103b1e1)
- [tests] fixing minor typos [`b333e63`](https://github.com/http-party/node-http-proxy/commit/b333e63648aa67ea1b1aaf17ba684e5fc6f751a6)
- Updated readme [`bd106d6`](https://github.com/http-party/node-http-proxy/commit/bd106d69f074a1c7018e685a4e144e23a17beb8c)
- [misc] use the local mocha instead the global [`f1aeb05`](https://github.com/http-party/node-http-proxy/commit/f1aeb0500cde39b63e570323e0e478530d1222ab)
- added unlimited listeners to the reverproxy event obj. [`1333c0c`](https://github.com/http-party/node-http-proxy/commit/1333c0cc62e7b590843f9b00326fe80137163c5e)
- [tests] throw error when no options, ALL TESTS PASSING! YAY [`86750c7`](https://github.com/http-party/node-http-proxy/commit/86750c7e594c419dfae957aaf7e44e61e1d480e8)
- ENH: updated example [`1c7ace2`](https://github.com/http-party/node-http-proxy/commit/1c7ace26c5a36fb63497f1ab67793c5b75495063)
- [merge] PR #470 [`38e6d7c`](https://github.com/http-party/node-http-proxy/commit/38e6d7cd5449a7264dcf5244b3dfd07b2dda60e1)
- [fix] remove stuff [`6a03e5f`](https://github.com/http-party/node-http-proxy/commit/6a03e5f7cf356416ea13584e279f5bfa3791c058)
- [test][misc] remove node@0.8 to test on travis [`8eff1a1`](https://github.com/http-party/node-http-proxy/commit/8eff1a1f26bb739dfc5a1ad90b140ff2a18921d5)
- merge with @cronopio [`0fb3381`](https://github.com/http-party/node-http-proxy/commit/0fb33810f5e70b714bd9868557d85a531b8e11e3)
- [merge] text [`98f29bd`](https://github.com/http-party/node-http-proxy/commit/98f29bdcfca9b818ffe107b09578539fdf379c8a)
- [fix] woops [`bd3df45`](https://github.com/http-party/node-http-proxy/commit/bd3df45010f282997cae3a699c7ecb885c01bdf8)
- [test] Test on newer version of node [`ebbba73`](https://github.com/http-party/node-http-proxy/commit/ebbba73eda49563ade09f38bdc8aef13d1cf6c00)
- new error propagation - follows [`1993faf`](https://github.com/http-party/node-http-proxy/commit/1993faf8a4227acda3423d46cf2cf13b4d9861e7)
- [fix] minor typo [`5a1504f`](https://github.com/http-party/node-http-proxy/commit/5a1504f0764b7747b53cc0d92a69ff3093e85ade)
- [fix] proxy to http(s) [`3c91ed3`](https://github.com/http-party/node-http-proxy/commit/3c91ed3d26d9af640d0c7a09fb9cdaf80ad673ca)
- Put the arguments the right way around in the README. [`1457980`](https://github.com/http-party/node-http-proxy/commit/145798062e332ac2aed7f8e8e3240e38464c870a)
- [fix] use some [`4480699`](https://github.com/http-party/node-http-proxy/commit/4480699d3a2a5080c051e7b8a100689fd1f58657)
- [fix] layout [`d7078e2`](https://github.com/http-party/node-http-proxy/commit/d7078e2fdd16d23d0b5f8f1d8a7ab3e9011fea4f)
- [docs] logo [`dd0f7b8`](https://github.com/http-party/node-http-proxy/commit/dd0f7b8876ae5b57fffab8857735b25b159f2bdb)
- [fix] url [`0637322`](https://github.com/http-party/node-http-proxy/commit/0637322d96e54bbcf5a14bf009dd73314cada4ce)
- [fix] opts [`adc5be0`](https://github.com/http-party/node-http-proxy/commit/adc5be020c7fff09a1c05ac771d5c5ab61002c23)
- [docs] fix syntax highlighting [`da9de70`](https://github.com/http-party/node-http-proxy/commit/da9de7034a452d1281217a349bc9403fddcc2b7f)
- [fix] typo [`275a519`](https://github.com/http-party/node-http-proxy/commit/275a5192fa257f78287a954b347e65023795487d)
- [tests] fix code coverage, changed pattern on blanket options [`4090250`](https://github.com/http-party/node-http-proxy/commit/40902506af3361b642b8798350b48404fe0a4e78)
- Put the arguments the right way around in emitter. [`7c8ecc8`](https://github.com/http-party/node-http-proxy/commit/7c8ecc8ea85b59fc16b55b9a142372b6ac168b2a)
- [fix] link [`72a89ea`](https://github.com/http-party/node-http-proxy/commit/72a89eab8bafef3742d78e8de8631094f961f427)
- [fix] space [`69f126b`](https://github.com/http-party/node-http-proxy/commit/69f126b34cbd190be8541a854d21f13bfb5a61bf)
- [fix] tests [`8269eca`](https://github.com/http-party/node-http-proxy/commit/8269eca2bb34d08336b8889e06e53d3522fa79fe)
- [fix] console [`18341d5`](https://github.com/http-party/node-http-proxy/commit/18341d559717e0a86f5ee4da024109e4b5a595a7)
- Set travis to run `npm test` while we fix coveralss.io integration [`e2a5d51`](https://github.com/http-party/node-http-proxy/commit/e2a5d513cac3ebceff446787fa106c7f00caf785)
- [fix] making @jcrugzz a happy camper [`2e7343d`](https://github.com/http-party/node-http-proxy/commit/2e7343d728a3187d48821b88ec2e2d4699bb2afe)
- [fix] minor typo [`5d66ce1`](https://github.com/http-party/node-http-proxy/commit/5d66ce11bb7eef7e704a2de2c0ef3b5f754843e9)
- [tests] tests fixed [`d60353f`](https://github.com/http-party/node-http-proxy/commit/d60353f80bbbcba128a2c51066e107365270e878)
- [tests] disabled the examples-test by now [`d83fdf6`](https://github.com/http-party/node-http-proxy/commit/d83fdf69a1121bfcfba72bbffcd3105ae5852c56)
- [fix] _ because it is unused [`590bb60`](https://github.com/http-party/node-http-proxy/commit/590bb604dae11223a0ae80469b59d6d341488f1f)
- [tests] disable test, by now is not throwing without options [`a2b1f0a`](https://github.com/http-party/node-http-proxy/commit/a2b1f0a4c9079342db6255c5f92db4a0cb992707)
- [fix] support target and forward [`961d2f9`](https://github.com/http-party/node-http-proxy/commit/961d2f9400b4cfd236c3c8ccbf401d37f8e871b8)
- [dist] Version bump. 0.10.4 [`840f6d8`](https://github.com/http-party/node-http-proxy/commit/840f6d8d29dffc11d3726123c2d400940ca2bdda)
- [fix] remove old reminescence [`4d65280`](https://github.com/http-party/node-http-proxy/commit/4d65280ea313438a94589bacf55f7a09cc107888)
- [feature] add emit proxyRes [`dda6f7a`](https://github.com/http-party/node-http-proxy/commit/dda6f7a45a46d2bf63e482d0b47b7c36ae548546)
- [docs] test badge [`1ceea3e`](https://github.com/http-party/node-http-proxy/commit/1ceea3e5f9b6232d60d673946bbccb7d8ccb4beb)
- [tests] remove caronte and use http-proxy for file names [`c9f5772`](https://github.com/http-party/node-http-proxy/commit/c9f5772fc18226aca31471bc96c44a6dbff5cbea)
- [logo] [`4c2f2f3`](https://github.com/http-party/node-http-proxy/commit/4c2f2f3b9a5ba65f97403e778a670f14301d52c1)

## [v0.10.3](https://github.com/http-party/node-http-proxy/compare/v0.10.2...v0.10.3) - 2013-06-20

### Merged

- Pass default certs to SNICallback example [`#419`](https://github.com/http-party/node-http-proxy/pull/419)

### Fixed

- Pass default certs to SNICallback example [`#399`](https://github.com/http-party/node-http-proxy/issues/399)

### Commits

- [dist] Bump version to 0.10.3 [`2fd748f`](https://github.com/http-party/node-http-proxy/commit/2fd748fb61dac7de0daa50aabbface7033c6a222)
- [fix] Respect `maxSockets` from `target` options in `RoutingProxy` [`e1d384e`](https://github.com/http-party/node-http-proxy/commit/e1d384e769e9f4adc5a06c516cfb721ff24b4b6d)
- Send path in req.path and not the url [`0c75323`](https://github.com/http-party/node-http-proxy/commit/0c753234c0c85333f909bdbef034ffb6e192bad5)

## [v0.10.2](https://github.com/http-party/node-http-proxy/compare/v0.10.1...v0.10.2) - 2013-04-21

### Merged

- Correct keep-alive responses to HTTP 1.0 clients [`#407`](https://github.com/http-party/node-http-proxy/pull/407)

### Fixed

- [minor] Style compliance. Fixes #402. [`#402`](https://github.com/http-party/node-http-proxy/issues/402)

### Commits

- Correct keep-alive responses to HTTP 1.0 clients. [`a29b5e8`](https://github.com/http-party/node-http-proxy/commit/a29b5e8e289c34c00d2b450e5fb9dd1969db4b97)
- [minor] Strip trailing whitespace. [`7fc39d7`](https://github.com/http-party/node-http-proxy/commit/7fc39d77f47311b82c24ab05f8e1a45a2733305c)
- Add headers on 'handshake' [`985025c`](https://github.com/http-party/node-http-proxy/commit/985025c90f3b2fafede64d8b17c318326f2423d9)
- Don't test raw HTTP 1.0 requests over HTTPS. [`daf53bd`](https://github.com/http-party/node-http-proxy/commit/daf53bd753879223dc84a49c92d0efaf576c1fd3)
- [dist] Version bump. 0.10.2 [`de0928f`](https://github.com/http-party/node-http-proxy/commit/de0928f616dd62165e8a22c00d091cabf31e1e87)

## [v0.10.1](https://github.com/http-party/node-http-proxy/compare/v0.10.0...v0.10.1) - 2013-04-12

### Merged

- Fix for slab buffer retention, leading to large memory consumption [`#370`](https://github.com/http-party/node-http-proxy/pull/370)

### Commits

- [dist] Version bump. 0.10.1 [`9c13ad4`](https://github.com/http-party/node-http-proxy/commit/9c13ad46e416125373d6604f3954ec3df1f55449)

## [v0.10.0](https://github.com/http-party/node-http-proxy/compare/v0.9.1...v0.10.0) - 2013-03-18

### Merged

- Change the emitter of the `proxyResponse` event [`#385`](https://github.com/http-party/node-http-proxy/pull/385)
- Fixing a bug that generates an unexpected TypeError [`#383`](https://github.com/http-party/node-http-proxy/pull/383)
- Mention Harmon used for response modifications in the readme [`#384`](https://github.com/http-party/node-http-proxy/pull/384)

### Commits

- [dist] Update CHANGELOG.md [`8665f3c`](https://github.com/http-party/node-http-proxy/commit/8665f3cc600feecbb4c8229699823149c69a144f)
- Harmon messsage [`35ba0db`](https://github.com/http-party/node-http-proxy/commit/35ba0db554c6bace21b1bacfa8f5fb6df4228db0)
- [fix breaking] Emit the `proxyResponse` event on the HttpProxy instance to reduce listener churn and reference counts. [`2620f06`](https://github.com/http-party/node-http-proxy/commit/2620f06e2db9a267945566f10837c4c2a5df753d)
- [dist] Version bump. 0.10.0 [`71183bf`](https://github.com/http-party/node-http-proxy/commit/71183bf30bc2b9ad2eaf57c51980eeb0bc7edff0)
- Fixing the if statement as it lead to 'TypeError: Parameter 'url' must be a string, not undefined' in certain cases [`c9b6895`](https://github.com/http-party/node-http-proxy/commit/c9b6895c5e14add6aba4f826a2173458a1896a5f)
- Harmon messsage [`4e42354`](https://github.com/http-party/node-http-proxy/commit/4e42354e77d5731a383d516fc0b249d5d0eda745)

## [v0.9.1](https://github.com/http-party/node-http-proxy/compare/v0.9.0...v0.9.1) - 2013-03-09

### Commits

- [dist doc] Updated CHANGELOG.md for `v0.9.1` [`ea5e214`](https://github.com/http-party/node-http-proxy/commit/ea5e214522d8ac34d1129b28ff188c0f232ce63f)
- [dist] Version bump. 0.9.1 [`701dc69`](https://github.com/http-party/node-http-proxy/commit/701dc698e3eb39ca6836a02611d8dce750f4e212)
- [breaking] Ensure that `webSocketProxyError` also receives the error to be consistent with `proxyError` events. [`c78356e`](https://github.com/http-party/node-http-proxy/commit/c78356e9cf27a21c57e4c98ef7dd3c22abe864c2)

## [v0.9.0](https://github.com/http-party/node-http-proxy/compare/v0.8.7...v0.9.0) - 2013-03-09

### Merged

- If HTTP 1.1 is used and backend doesn't return 'Connection' header, expicitly  return Connection: keep-alive. [`#298`](https://github.com/http-party/node-http-proxy/pull/298)
- add "with custom server logic" to the "Proxying WebSockets" section of the readme [`#332`](https://github.com/http-party/node-http-proxy/pull/332)
- routing proxy 'this' reference bug? [`#365`](https://github.com/http-party/node-http-proxy/pull/365)
- fixed issue #364 'proxyError' event emitted twice [`#374`](https://github.com/http-party/node-http-proxy/pull/374)
- Misleading documentation for Websockets via .createServer [`#349`](https://github.com/http-party/node-http-proxy/pull/349)

### Fixed

- [api test] Manually merge #195 from @tglines since that fork was deleted. Update tests to use new macros. Fixes #195. Fixes #60. [`#195`](https://github.com/http-party/node-http-proxy/issues/195) [`#60`](https://github.com/http-party/node-http-proxy/issues/60)
- [fix] Set "content-length" header to "0" if it is not already set on DELETE requests. Fixes #338. [`#338`](https://github.com/http-party/node-http-proxy/issues/338)
- [fix] Do not use "Transfer-Encoding: chunked" header for proxied DELETE requests with no "Content-Length" header. Fixes #373. [`#373`](https://github.com/http-party/node-http-proxy/issues/373)
- [fix] http-proxy should not modify the protocol in redirect request for external sites. Fixes #359. [`#359`](https://github.com/http-party/node-http-proxy/issues/359)
- [fix] Emit `notFound` event when ProxyTable location does not exist. Fixes #355. Fixes #333. [`#355`](https://github.com/http-party/node-http-proxy/issues/355) [`#333`](https://github.com/http-party/node-http-proxy/issues/333)
- [fix] Make options immutable in `RoutingProxy`. Fixes #248. [`#248`](https://github.com/http-party/node-http-proxy/issues/248)
- [fix] Remove special case handling of `304` responses since it was fixed in 182dcd3. Fixes #322. [`#322`](https://github.com/http-party/node-http-proxy/issues/322)
- [fix] Ensure `response.headers.location` is defined. Fixes #276. [`#276`](https://github.com/http-party/node-http-proxy/issues/276)

### Commits

- [minor] s/function(/function (/ s/){/) {/ [`9cecd97`](https://github.com/http-party/node-http-proxy/commit/9cecd97153ccce4f81c5eda35a49079e651fb27a)
- working on x-forwarded-for [`1332409`](https://github.com/http-party/node-http-proxy/commit/133240937dc63aca0007388327837bc24808f79a)
- Routing Proxy was not sending x-forward-*. Fixing It... [`916d44e`](https://github.com/http-party/node-http-proxy/commit/916d44e3d2a17bb9d5178f347ddad9796b988e05)
- Added timeout option and test to test new timeout parameter, added requestFail assertion. [`89d43c2`](https://github.com/http-party/node-http-proxy/commit/89d43c20dd0dec1dda1fd70e57f3f250b9e3b431)
- Add tests for headers bug fixes [`ecb5472`](https://github.com/http-party/node-http-proxy/commit/ecb547223f3f1d9bf551842c2026ee2f1a18638a)
- Added simple round robin example with websocket support [`83fbd42`](https://github.com/http-party/node-http-proxy/commit/83fbd4250660f41de1ab2b5490a3bf58200ae148)
- - support unix donain sockets and windows named pipes (socketPath) on node 0.8.x. On node 0.6.x the support was opaque via port, but on the new node, socketPath should be set explicitely. [`ffe74ed`](https://github.com/http-party/node-http-proxy/commit/ffe74ed299f81206b898147dbcc985519b2921f8)
- pathnameOnly flag added.  Ignores hostname and applies routing table to the paths being requested. [`46b078a`](https://github.com/http-party/node-http-proxy/commit/46b078a98d10de7726a3bbca89121acc57ad7625)
- [doc] added comments to pathnameOnly block. [`5e6be6c`](https://github.com/http-party/node-http-proxy/commit/5e6be6ccf5a39ff450e57d7b24e374a83569fa85)
- remove offending code, final fix for issue #364 [`3b84e27`](https://github.com/http-party/node-http-proxy/commit/3b84e27ab4efd5ce3b8ac837d699d4ff6661c7e7)
- memory leak fix in closing of the scokets [`2055d0c`](https://github.com/http-party/node-http-proxy/commit/2055d0c8ec16699ffb06adf6d64d9506920b2071)
- Fix truncated chunked responses [`ef66833`](https://github.com/http-party/node-http-proxy/commit/ef66833c4d7f07ae9f42026f2bcc0fbca2440579)
- Re-added previous description [`603106a`](https://github.com/http-party/node-http-proxy/commit/603106a13d28c0199fa4456cc9aee1692eb2588c)
- pathnameOnly option documented in the Readme.md [`a1607c1`](https://github.com/http-party/node-http-proxy/commit/a1607c1684a7d7617e5148a0dca882eb08a9f03b)
- [fix minor] Prevent crashes from attempting to remove listeners more than once when proxying websocket requests. [`a681493`](https://github.com/http-party/node-http-proxy/commit/a681493371ae63f026e869bf58b6fea682dc5de3)
- Added comments [`64efa7f`](https://github.com/http-party/node-http-proxy/commit/64efa7f9291a2377a32e942a247700b71b107993)
- Revert "[fix minor] Prevent crashes from attempting to remove listeners more than once when proxying websocket requests." [`c6da760`](https://github.com/http-party/node-http-proxy/commit/c6da760ca9f375025229fe3fc174aca943362f38)
- [doc dist] Update CHANGELOG.md for `v0.9.0`. [`133115c`](https://github.com/http-party/node-http-proxy/commit/133115c9760130dcef447efbd18c470c08795c90)
- add support for loading CA bundles [`10f6b05`](https://github.com/http-party/node-http-proxy/commit/10f6b0577518bdfcb6b43c1f516dc988bdcade53)
- problem: don't want to run my server as root to bind to privileged ports (e.g. 80, 443). [`2c36507`](https://github.com/http-party/node-http-proxy/commit/2c3650746cd90fed63b140a8d393e18bd35cd8f9)
- Add 'proxyResponse' event so observer can modify response headers or abort response. [`3b86a7a`](https://github.com/http-party/node-http-proxy/commit/3b86a7aae3fc366c5fa8645285a4368dbac7a0dc)
- [minor] Move private helper to end of file. [`476cbe7`](https://github.com/http-party/node-http-proxy/commit/476cbe741fc41b7f1eb269d841d922784e8b3c6b)
- Fix for retaining large slab buffers in node core [`d2888c8`](https://github.com/http-party/node-http-proxy/commit/d2888c83f5eab3fb82425ef4fd51e62621bf2764)
- [dist] Update `devDependencies` [`ad21310`](https://github.com/http-party/node-http-proxy/commit/ad213106d06cfc79004841f04b8e73fe7d7ef67a)
- [minor] Small whitespace compliance. [`ea0587a`](https://github.com/http-party/node-http-proxy/commit/ea0587a8f98b1eedc38c66b69293ae091e24be6e)
- [doc fix] Add undefined var in example. [`deca756`](https://github.com/http-party/node-http-proxy/commit/deca7565c51fd678354d26eaae7fe2481e36e2c3)
- working on x-forwarded-for [`31fc94a`](https://github.com/http-party/node-http-proxy/commit/31fc94aa5e43c54033d5384caaf104eebf3889bd)
- Allow event observers to access upstream response headers and data. [`4c130f5`](https://github.com/http-party/node-http-proxy/commit/4c130f5dac5f2cfbfc2618446b86244aff4cb04f)
- [fix doc] Fix bad variable reference in `README.md`. [`440013c`](https://github.com/http-party/node-http-proxy/commit/440013c263a96c6681bfe92a8f56db93b58efa8d)
- Change wording for handling websocket proxy events [`ee6bbe0`](https://github.com/http-party/node-http-proxy/commit/ee6bbe00244c90bd532b11ff1c796aea8c7372f8)
- [dist] Version bump. 0.9.0 [`c68e038`](https://github.com/http-party/node-http-proxy/commit/c68e0389120d8530e578e20496d8ee091e69a580)
- fix 'this' reference in routing proxy listener bindings [`15afc23`](https://github.com/http-party/node-http-proxy/commit/15afc23a275f3fa16653fff6179368122661a0af)
- cleanning [`8d87399`](https://github.com/http-party/node-http-proxy/commit/8d8739999fcaf4cdd8f2471046f6f036c44dc8f7)
- cleanning [`9672b99`](https://github.com/http-party/node-http-proxy/commit/9672b9927156a0dfe3ce4539f380aaf3172f6267)
- Fix typo which slipped in during patch clean-up [`ba65a48`](https://github.com/http-party/node-http-proxy/commit/ba65a485fcf7230e85cee77f6eefcd17e46c8f86)
- Remove data event that is not needed after-all. [`b1c4bd6`](https://github.com/http-party/node-http-proxy/commit/b1c4bd61e8ae5705d4cc97bf719c381554671967)

## [v0.8.7](https://github.com/http-party/node-http-proxy/compare/v0.8.6...v0.8.7) - 2012-12-22

### Commits

- [fix] Handle errors on request object [`edfe869`](https://github.com/http-party/node-http-proxy/commit/edfe86915941e465a06c1d0a3330ee32e5834aa6)
- [dist] Bump version to 0.8.7 [`26d3646`](https://github.com/http-party/node-http-proxy/commit/26d3646ff252129f35525ab0540a31f5617a31d2)
- [fix] Don't remove `error` listener after response ends [`223eacd`](https://github.com/http-party/node-http-proxy/commit/223eacda85a4267f2860f6c46f7dedfa9db8c224)

## [v0.8.6](https://github.com/http-party/node-http-proxy/compare/v0.8.5...v0.8.6) - 2012-12-21

### Merged

- http-proxy: 304 responses should emit 'end' too [`#337`](https://github.com/http-party/node-http-proxy/pull/337)

### Commits

- [bench] Remove silly "benchmarks" [`2bd9cd9`](https://github.com/http-party/node-http-proxy/commit/2bd9cd9adb6cea6763930468d22cb56fffab6218)
- [bench] Add a benchmark for websockets throughput [`6797a27`](https://github.com/http-party/node-http-proxy/commit/6797a2705a309d19a655ab468bcc80ba2e43cf41)
- [fix] Handle socket errors [`2a61ec8`](https://github.com/http-party/node-http-proxy/commit/2a61ec85bdaeed9a5fca2a117efb36a7f76becc4)
- [dist] Update `devDependencies` [`b81d9b7`](https://github.com/http-party/node-http-proxy/commit/b81d9b71daa32a571384cff29d81227993299236)
- [dist] Bump version to 0.8.6 [`6cd78f6`](https://github.com/http-party/node-http-proxy/commit/6cd78f6af9ca08b8797c409896eea2ae6bb6d835)
- [bench] More exact size display [`7bc1a62`](https://github.com/http-party/node-http-proxy/commit/7bc1a628feab78f8931e9e6481737dd871debfeb)

## [v0.8.5](https://github.com/http-party/node-http-proxy/compare/v0.8.4...v0.8.5) - 2012-11-16

### Merged

- lib: allow overriding maxSockets [`#323`](https://github.com/http-party/node-http-proxy/pull/323)

### Fixed

- [fix] Convert strings to numbers if possible in `.createServer` [`#321`](https://github.com/http-party/node-http-proxy/issues/321)

### Commits

- [test] Delete invalid core test [`886a395`](https://github.com/http-party/node-http-proxy/commit/886a395429f20163992ca76e7b0d059256f56ba6)
- [test] Upgrade `common.js` from node core [`fefbf04`](https://github.com/http-party/node-http-proxy/commit/fefbf04ac03126858bdad07df7b10131a46e17d6)
- add "with custom server logic" to the "Proxying WebSockets" section of the readme.md [`03dbe11`](https://github.com/http-party/node-http-proxy/commit/03dbe115c2b088737e5b9abcadf91a8298f56f1f)
- [test] Kill child process when exiting test runner [`74ec175`](https://github.com/http-party/node-http-proxy/commit/74ec1757153c503ce57eb552031648fe79731d48)
- [fix] Correctly kill test processes [`b8c27ed`](https://github.com/http-party/node-http-proxy/commit/b8c27ed565e416827b7c4bb123aa9ee119d008e6)
- [test] Make global detection work with older node versions [`3531fd6`](https://github.com/http-party/node-http-proxy/commit/3531fd609a8ce156d27c27ca38ac912a73aebfeb)
- [dist] Bump version to 0.8.5 [`22639b3`](https://github.com/http-party/node-http-proxy/commit/22639b378189ec78f9962dde64337df050e29a6f)
- [test] Run core tests on `npm test` [`41c9a9c`](https://github.com/http-party/node-http-proxy/commit/41c9a9caad679221b8f1d4dcfb74f9b2bdb8270b)
- [test] Stop testing on `node v0.9`, tests timeout [`9042665`](https://github.com/http-party/node-http-proxy/commit/9042665ea98a6587e1d6800e51d3c354c0a1b20a)

## [v0.8.4](https://github.com/http-party/node-http-proxy/compare/v0.8.2...v0.8.4) - 2012-10-23

### Merged

- Events patch [`#320`](https://github.com/http-party/node-http-proxy/pull/320)
-  documentation for options [`#315`](https://github.com/http-party/node-http-proxy/pull/315)
- Added travis build status [`#308`](https://github.com/http-party/node-http-proxy/pull/308)
- Fix installation instructions: s/http/https/ [`#302`](https://github.com/http-party/node-http-proxy/pull/302)
- If supplied pass changeOrigin option through to HttpProxy instance if set in RoutingProxy [`#285`](https://github.com/http-party/node-http-proxy/pull/285)

### Commits

- [fix test] Fix examples to use newest version of socket.io and helpers. Added tests for ensuring that examples require as expected with no errors. [`fd648a5`](https://github.com/http-party/node-http-proxy/commit/fd648a529090cefc202613fff3fdfec9ba0e6a72)
- [fix] spdy should look like https when forwarding (until we get a client) [`698b01d`](https://github.com/http-party/node-http-proxy/commit/698b01da8e1fe6195b00e5006032d262a0a86f4e)
- [docs] options [`4c8e1d9`](https://github.com/http-party/node-http-proxy/commit/4c8e1d96a36523a548959415903bc669ebcc138d)
- http-proxy: emit websocket:start [`5df6e7b`](https://github.com/http-party/node-http-proxy/commit/5df6e7bdb8d4685a18e94ff1bf117ce8eff8d1c9)
- [fix] `destroy()` websockets in case of an error [`0d00b06`](https://github.com/http-party/node-http-proxy/commit/0d00b06af307dc5c70c36e89617a08486eb665e2)
- [fix] Suppress EADDRINUSE errors from `test/examples-test.js` since we are just looking for require-time errors. Isolate tests to ensure idempotency of ports [`c4a7b15`](https://github.com/http-party/node-http-proxy/commit/c4a7b1584302fe12a8fc06b6774db5ff602c3607)
- [docs] more options [`d4cb9da`](https://github.com/http-party/node-http-proxy/commit/d4cb9dad6ce36a823c9e8970e0bb3266d844e536)
- If HTTP 1.1 is used and backend doesn't return 'Connection' header, explicitly [`850171c`](https://github.com/http-party/node-http-proxy/commit/850171cdc41cb93343f7c31f650ac908a8d2dacb)
- [refactor] Pass all options to `Agent` constructor [`eafdc74`](https://github.com/http-party/node-http-proxy/commit/eafdc744b67b33b5ed3cfc80de84dafcd850bdd0)
- Fix socket leaks when FIN packet isn't responded to [`24b8406`](https://github.com/http-party/node-http-proxy/commit/24b84068eac1c704d9f8df3dc833b976850c328f)
- [fix] Partial fix for rejecting self-signed certs in tests [`2e7d8a8`](https://github.com/http-party/node-http-proxy/commit/2e7d8a88f4b470dcc9da1639fe2a69e03251036c)
- [fix] Dont use `-i` when running vows because it supresses `--target=` and `--proxy=` CLI arguments [`1783ab0`](https://github.com/http-party/node-http-proxy/commit/1783ab0625743355eecc11f5cfd57469c429daa0)
- [test] Add `node v0.9` testing, test all branches [`4f6387c`](https://github.com/http-party/node-http-proxy/commit/4f6387c17f55c23da4aac161cf2e5a4dd2a25c40)
- [minor] Remove `setEncoding` on incoming socket [`812868d`](https://github.com/http-party/node-http-proxy/commit/812868ddfc720b6c4fd26603c2fe4d5ae68f2492)
- [dist] v0.8.3 [`a89a5b8`](https://github.com/http-party/node-http-proxy/commit/a89a5b80889a56dd31634096bc6546b6b7b26da2)
- [fix] Ignore npm version errors when installing dependencies for examples [`a454666`](https://github.com/http-party/node-http-proxy/commit/a454666e7a0465ed65b7bbd29cf1b0c6c126d153)
- [fix] function [`213e03c`](https://github.com/http-party/node-http-proxy/commit/213e03c99844c5c984fbf857bae32095165a1e8f)
- [dist] Bump version to 0.8.4 [`4d7e8a8`](https://github.com/http-party/node-http-proxy/commit/4d7e8a808d83d3db1b729820aba5f481ab3d18f4)
- [minor doc] Correct comment [`cee27fe`](https://github.com/http-party/node-http-proxy/commit/cee27feeddf9b4db06917dfa9e59e6bcd7e14c27)

## [v0.8.2](https://github.com/http-party/node-http-proxy/compare/v0.8.1...v0.8.2) - 2012-07-22

### Merged

- Add example for gzip middleware using a proxy table. [`#221`](https://github.com/http-party/node-http-proxy/pull/221)
- Implement RoutingProxy.prototype.remove [`#246`](https://github.com/http-party/node-http-proxy/pull/246)
- prefer `target.hostname` over `target.host` [`#235`](https://github.com/http-party/node-http-proxy/pull/235)
- add "Using two certificiates" to the https section of the readme.md [`#275`](https://github.com/http-party/node-http-proxy/pull/275)
- Add support for setting the host in the executable [`#268`](https://github.com/http-party/node-http-proxy/pull/268)
- Hi! I fixed some calls to "sys" for you! [`#270`](https://github.com/http-party/node-http-proxy/pull/270)
- Fix bug: x-forwarded-proto set incorrectly as httphttps or wswss [`#266`](https://github.com/http-party/node-http-proxy/pull/266)

### Commits

- [refactor] Rewrite tests to use saner vows idioms. Update tests to use latest socket.io [`4ae7a5b`](https://github.com/http-party/node-http-proxy/commit/4ae7a5b84011bb5b9ec3a36ded4c5e5b3330db80)
- [dist] Remove out-dated docco docs [`2d75510`](https://github.com/http-party/node-http-proxy/commit/2d75510d827c770c30a7292c31ef0f2007da7086)
- [refactor test] Finish removing old test code. [`e2dc7f9`](https://github.com/http-party/node-http-proxy/commit/e2dc7f96937e5d565fea16c9f56b9f5d3e427de2)
- [dist] Complete JSHint compliance except for `too many var statements` [`36226da`](https://github.com/http-party/node-http-proxy/commit/36226daa2e4cbc65fae80d2d09fd64c0e7ce36ba)
- [refactor test] Add support for `http*-to-http*` testing from CLI arguments [`828dbeb`](https://github.com/http-party/node-http-proxy/commit/828dbebcaaf11e338a7727bf9d2fff8bfbd3726e)
- [fix api] Optimize lookups in the ProxyTable. Ensure that RoutingProxy can proxy to `https` by default. [`55286a7`](https://github.com/http-party/node-http-proxy/commit/55286a7c499c0fe267f75d8e8441ff89f1e65f99)
- Whitespace fixes. [`04ce49c`](https://github.com/http-party/node-http-proxy/commit/04ce49c5b289acb6ad72303e9ac70c637ea490b2)
- [refactor tests] Finished refactoring tests to support `ws*-to-ws*` tests based on CLI arguments [`7e854d7`](https://github.com/http-party/node-http-proxy/commit/7e854d778b89201f7cb933e8bbda66316b98b0b4)
- [doc] Minor formatting updates to README.md [`6753951`](https://github.com/http-party/node-http-proxy/commit/67539519faf1f32073fdb562404bd897072e24ee)
- [fix] Changed require('util') to require('util') for compatibility with node v0.8 [`bf7e328`](https://github.com/http-party/node-http-proxy/commit/bf7e328fb837de69455c42f41822b0caae2777b6)
- [test] Add .travis.yml file for Travis CI. [`29e6e74`](https://github.com/http-party/node-http-proxy/commit/29e6e748f780629d05635eebb421e8ee1d125058)
- Use changeOrigin for proxyRequest. [`0273958`](https://github.com/http-party/node-http-proxy/commit/0273958b0a5c7823c6212cb6ce6e4f801a215d3b)
- adding support for setting the host [`06e78f2`](https://github.com/http-party/node-http-proxy/commit/06e78f27475165d023fd66afbe5dd626a6a548af)
- match style requested by @cronopio [`415d4ed`](https://github.com/http-party/node-http-proxy/commit/415d4ed908e45332421d683eb45e0d6873b85ae7)
- Fix bug: x-forwarded-proto set incorrectly [`0933f1c`](https://github.com/http-party/node-http-proxy/commit/0933f1c598c1b62a75e040c3ed3ccb262612d3c9)
- [dist] Version bump. 0.8.2 [`13c34d0`](https://github.com/http-party/node-http-proxy/commit/13c34d09b2f8be14fbbe4be77c49b23066667f1b)

## [v0.8.1](https://github.com/http-party/node-http-proxy/compare/v0.8.0...v0.8.1) - 2012-06-05

### Merged

- [misc] Updating the changelog. Close #137 [`#256`](https://github.com/http-party/node-http-proxy/pull/256)
- Fix problem with req.url not being not properly replaced. [`#218`](https://github.com/http-party/node-http-proxy/pull/218)
- Re-emit 'start', 'forward' and 'end' events in RoutingProxy, and fix some hanging issues. [`#216`](https://github.com/http-party/node-http-proxy/pull/216)
- Fixes to make the websockets example work. [`#225`](https://github.com/http-party/node-http-proxy/pull/225)
- [minor] Syntax error [`#222`](https://github.com/http-party/node-http-proxy/pull/222)
- [docs] Making README links consistent with latest project structure. [`#208`](https://github.com/http-party/node-http-proxy/pull/208)
- [docs] improved grammar [`#205`](https://github.com/http-party/node-http-proxy/pull/205)
- proposed doc addition for #180 [`#189`](https://github.com/http-party/node-http-proxy/pull/189)

### Fixed

- Merge pull request #256 from nodejitsu/changelog [`#137`](https://github.com/http-party/node-http-proxy/issues/137)
- [misc] Updating the changelog. Close #137 [`#137`](https://github.com/http-party/node-http-proxy/issues/137)

### Commits

- Whitespace fixes [`e9fd3f4`](https://github.com/http-party/node-http-proxy/commit/e9fd3f43d7e890f0164b5a03a34f196dd162d043)
- Added example for gzip middleware using a ProxyTable. [`6201328`](https://github.com/http-party/node-http-proxy/commit/62013281b8a980c53a38362f10d746bfbf36c52e)
- [examples] Added simple load balancer example [`fd7fcd8`](https://github.com/http-party/node-http-proxy/commit/fd7fcd8decbf0c7ab00cab84e151991e380b8fae)
- [dist] Update author field for consistency [`27316e2`](https://github.com/http-party/node-http-proxy/commit/27316e22e8e7786252583cdb9131cfd8cacb07c1)
- Add documentation for listening for proxy events to prevent a common mistake. [`4f2bc58`](https://github.com/http-party/node-http-proxy/commit/4f2bc58431c7f44d486ee8c1ee3136b3637f9405)
- Fix RoutingProxy hanging when there is an error [`b26b434`](https://github.com/http-party/node-http-proxy/commit/b26b434e9fc501f7e0c4a966dbee6220c355bc7c)
- prefer `target.hostname` over `target.host` [`c4d185d`](https://github.com/http-party/node-http-proxy/commit/c4d185dca9696c77d5c38d24d897c2679f6762a0)
- [doc] Fix style in websockets example [`ed06af9`](https://github.com/http-party/node-http-proxy/commit/ed06af97efe406ea2533009be64a6b568f9d0601)
- Add tests for remapping URL properly. [`5d839dd`](https://github.com/http-party/node-http-proxy/commit/5d839dd5f8890c6d2af96807b96d1bd5bb0f7276)
- fixed comment typos in examples/http/proxy-https-to-http.js and proxy-https-to-https.js, lines 37 and 46 [`868f7e7`](https://github.com/http-party/node-http-proxy/commit/868f7e7a287c4709c541c077f3e2303f45b1f072)
- [misc] changelog updated to version 0.8.1 [`e9a3a30`](https://github.com/http-party/node-http-proxy/commit/e9a3a3012c5507dff46afd3e5cececf43b1717ae)
- Implement RoutingProxy.prototype.remove [`0532995`](https://github.com/http-party/node-http-proxy/commit/0532995dfa0be53d285c886a9922b8915f297d36)
- Making README links consistent with latest project structure. [`7fa6599`](https://github.com/http-party/node-http-proxy/commit/7fa6599f4f2c92bb29bc5fc8a9ba06d704652c5e)
- Address ticket #180 here since that problem is so hard to discover when you run into it. If there was an error, people would search for the error text, but there isn't. [`73e415a`](https://github.com/http-party/node-http-proxy/commit/73e415a22634bfc9e5993377902f67ac3212714a)
- [tests] used socket.io 0.6.17 fixed version for tests [`45d67f4`](https://github.com/http-party/node-http-proxy/commit/45d67f42cba373db4f47765d6a3dd38a7d19dae6)
- [fix] x-forwarded-proto sets properly [`ca37ad7`](https://github.com/http-party/node-http-proxy/commit/ca37ad74367764cca479a1af63bd7491dc79606b)
- [doc] add missing {} to make an object [`843901e`](https://github.com/http-party/node-http-proxy/commit/843901eeeb24611ad24889f13edcbfd5dee4314d)
- fix the broken english and clarified the sentence (I hope) [`e15db4f`](https://github.com/http-party/node-http-proxy/commit/e15db4fb50db3e2191f3ebd30e12eeed9c376bc2)
- Re-emit 'start', 'forward' and 'end' events in RoutingProxy. [`99ee542`](https://github.com/http-party/node-http-proxy/commit/99ee54259eae70c0c680ee82efc7dd184313f182)
- [doc] call listen() to get the server started [`4fc1ee8`](https://github.com/http-party/node-http-proxy/commit/4fc1ee85d35d9feb468f808ddd11aaf186eaedd4)
- syntax error fixed [`5842d0e`](https://github.com/http-party/node-http-proxy/commit/5842d0ee7de875378d9b8ae240748dd2af567be9)
- [dist] Version bump 0.8.1 [`81f6095`](https://github.com/http-party/node-http-proxy/commit/81f6095cf08f84a84ae2bbda7ca0315729638fe0)
- finally removed hidden char [`4358a4c`](https://github.com/http-party/node-http-proxy/commit/4358a4c1225acf8c13536fd742b845166f3a65a6)
- [minor fix] delete white space [`df650d1`](https://github.com/http-party/node-http-proxy/commit/df650d11dd0a47653a4905f871d8d3d6c327d600)

## [v0.8.0](https://github.com/http-party/node-http-proxy/compare/v0.7.3...v0.8.0) - 2011-12-23

### Merged

- Fix issue where front-end is HTTPS, back-end is HTTP, and server issues a redirect. [`#165`](https://github.com/http-party/node-http-proxy/pull/165)
- Modified the ad-hoc proxy lookup to use _getKey(), rather than the error-prone in-line method. [`#164`](https://github.com/http-party/node-http-proxy/pull/164)
- Allows node-http-proxy to append new values to existing headers for incoming "x-forward-for","x-forward-proto" and "x-forward-port" [`#163`](https://github.com/http-party/node-http-proxy/pull/163)
- [fix] only set one drain listener while paused [`#136`](https://github.com/http-party/node-http-proxy/pull/136)
- [docs] grammar correction [`#134`](https://github.com/http-party/node-http-proxy/pull/134)

### Fixed

- [fix] Avoid `Transfer-Encoding: chunked` for HTTP/1.0 client, closes #59. [`#59`](https://github.com/http-party/node-http-proxy/issues/59)

### Commits

- [refactor minor] Update vendor/websocket.js to be compatible with node@0.6.x [`ea7fea6`](https://github.com/http-party/node-http-proxy/commit/ea7fea627255ed34d39902438b55e740c7c9b08c)
- [test] Add common.js file from core [`543f214`](https://github.com/http-party/node-http-proxy/commit/543f214361605cffdbee7b233029edf343c358c1)
- [test] Add core `test-http-proxy` test [`feb324b`](https://github.com/http-party/node-http-proxy/commit/feb324b0d4c0a2307493b35be944ed08ffc9187a)
- [test] Add core `test-http` test [`25a9e2d`](https://github.com/http-party/node-http-proxy/commit/25a9e2d217cabef07d6f161f5d6ded49342dbb2f)
- [test] Add core `test-http-host-headers` test [`f298411`](https://github.com/http-party/node-http-proxy/commit/f298411f76a106791f34dd4d31ea033a7bdca9c7)
- [test] Add core `test-http-extra-response` test [`c26ab5e`](https://github.com/http-party/node-http-proxy/commit/c26ab5e46ff2649f0ea6585f20d8f58b7d0cadef)
- [test] Add core `test-http-set-cookies` test [`b3b5cce`](https://github.com/http-party/node-http-proxy/commit/b3b5cce3aee98a7fd5b50fb8e1bd6bd5e1c7512f)
- [test] Add core `test-http-client-abort` test [`7bf8d4a`](https://github.com/http-party/node-http-proxy/commit/7bf8d4a7be668591b350144b4546559abf9a0b5f)
- [test] Add core `test-http-client-upload` test [`7648fe5`](https://github.com/http-party/node-http-proxy/commit/7648fe50c1859597dc390e9e628db938372483e7)
- [test] Add core `test-http-client-upload-buf` test [`5ac9878`](https://github.com/http-party/node-http-proxy/commit/5ac987857c934d07073b853f5243d2d8fc6d8c2b)
- [test] Add core `test-http-upgrade-server2` test [`bc98c0d`](https://github.com/http-party/node-http-proxy/commit/bc98c0dbce154ef266eef83d3c2f737a2d60f0e6)
- [test] Implement basic runner for multiple tests [`a4079c6`](https://github.com/http-party/node-http-proxy/commit/a4079c6a1c8b87334d12d47d67f060cbb1214696)
- [test] Add core `test-http-upload-timeout` test [`60ff181`](https://github.com/http-party/node-http-proxy/commit/60ff181af9c22405d3822ce5955f178ab13de79d)
- [test] Add core `test-http-status-code` test [`82060a5`](https://github.com/http-party/node-http-proxy/commit/82060a53430de05f2dc95450d8487bc8139544d5)
- [test] Add core `test-http-many-keep-alive-connections` test [`4e1ca6e`](https://github.com/http-party/node-http-proxy/commit/4e1ca6e61899b11cad1b437cc9d9490b9d856665)
- [test] Add core `test-http-chunked` test [`d7461f3`](https://github.com/http-party/node-http-proxy/commit/d7461f3206cca0691fbd438545ff325589770627)
- [test] Add core `test-http-head-response-has-no-body-end` test [`13389db`](https://github.com/http-party/node-http-proxy/commit/13389db1bef38a7fc7ddc3ada479a608f033020c)
- [test] Add core `test-http-server-multiheaders` test [`d7f15d0`](https://github.com/http-party/node-http-proxy/commit/d7f15d02f7477c76529fc76daddee5029079eb2d)
- [test] Add core `test-http-multi-line-headers` test [`35d2088`](https://github.com/http-party/node-http-proxy/commit/35d2088c96bacb44b17755176b6e9451ed0299dd)
- [test] Add core `test-http-head-response-has-no-body` test [`f79f3ad`](https://github.com/http-party/node-http-proxy/commit/f79f3adf0295ec5bb7fb9f6525b48ba5209d04c6)
- [refactor] Improved event handler cleanup  [`9f92332`](https://github.com/http-party/node-http-proxy/commit/9f923325d08ac018a3325beaa9e0805b5eda61e6)
- [fix minor] Correctly set x-forwarded-proto in WebSocket requests [`c81bae2`](https://github.com/http-party/node-http-proxy/commit/c81bae2fdde3bf0087fe71a39855c61c43ffb145)
- Revert "[refactor] Improved event handler cleanup " [`c83d88e`](https://github.com/http-party/node-http-proxy/commit/c83d88ee88faac10b53cd4296165ed85f26036b4)
- Allowing the common proxy headers' value to be appended in proxy chain scenarios. [`621f9b4`](https://github.com/http-party/node-http-proxy/commit/621f9b425a272421de98a674f1679f0c47912733)
- [test] Add basic test runner [`87999d0`](https://github.com/http-party/node-http-proxy/commit/87999d028880dfccca349c9c44f9e66a613c4d38)
- [examples] Add some hand-crafted middleware [`6e65c20`](https://github.com/http-party/node-http-proxy/commit/6e65c20017a2e1a87dc6d58e847bc6db16440f3c)
- [test] Add core `test-http-malformed-request` test [`a635389`](https://github.com/http-party/node-http-proxy/commit/a6353897cdbe8c380d52a060f5e66784f67ad98e)
- [example] Response modification middleware [`dd83199`](https://github.com/http-party/node-http-proxy/commit/dd8319972c1c2f9421a90a21dce9560fd5ca199f)
- [test] Add core `test-http-head-request` test [`c0857f2`](https://github.com/http-party/node-http-proxy/commit/c0857f2d59c33d91cb3e0c131c44ec1667f592fa)
- [test] Add core `test-http-response-close` test [`f1c0be3`](https://github.com/http-party/node-http-proxy/commit/f1c0be3f0bd2c5e87d44a37ba4f29aafd9903ad4)
- [refactor] core proxy logic. all tests should be passing. [`63ac925`](https://github.com/http-party/node-http-proxy/commit/63ac9252606d23e2003696da1fb34a539abee7ca)
- [test] Add core `test-http-contentLength0` test [`275109b`](https://github.com/http-party/node-http-proxy/commit/275109b2f8c8519c56ca9f456096d4002698fab1)
- [test] Add core `test-http-client-abort2` test [`98bbe54`](https://github.com/http-party/node-http-proxy/commit/98bbe541e4fa581f1b9e2eadb821c0609da6ab81)
- adding tests for url segment proxytable routing [`91e9bb9`](https://github.com/http-party/node-http-proxy/commit/91e9bb90709cc8a361066d6f6b8f51f58bfd7e36)
- [test] Add core `test-http-eof-on-connect` test [`80c216d`](https://github.com/http-party/node-http-proxy/commit/80c216df0cc59b88c6934f795c03ea16a737af34)
- [example] Replace `sys` usages with `util` [`8d701bb`](https://github.com/http-party/node-http-proxy/commit/8d701bb20b593c6cdf0ff1bc35cf83051b21a35e)
- [refactor] Updates to support http2 from @mikeal [`5b52c89`](https://github.com/http-party/node-http-proxy/commit/5b52c896947db42ac01e6038c9170d8859d33aea)
- [refactor] Listen for `socket` events since reverseProxy.socket is no longer set synchronously [`3828616`](https://github.com/http-party/node-http-proxy/commit/38286168161d4f4ad24d2ad95ccd8335e9ed08a4)
- [test] Run tests in `test/core/simple` by default [`68cebbe`](https://github.com/http-party/node-http-proxy/commit/68cebbe0e79ea283eea8a1ca850ab462c66c611a)
- simplify proxytable path segment rewrite logic [`c03a450`](https://github.com/http-party/node-http-proxy/commit/c03a450d9b952e1463ae2609303029e317ff5da2)
- change proxytable routing to route one level shallower [`4d50915`](https://github.com/http-party/node-http-proxy/commit/4d50915373b6afaafc7857a3e9366e8e77315683)
- [docs] Little explanation for test/core directory [`8ca5d83`](https://github.com/http-party/node-http-proxy/commit/8ca5d83497cc106a2456ff7f2ebe3db5c8634d69)
- [minor] Allow user to set `colors.mode` [`48d4a8b`](https://github.com/http-party/node-http-proxy/commit/48d4a8b263faa9acda06651bceeff50881f21b26)
- [minor] Indentation fix [`9e630da`](https://github.com/http-party/node-http-proxy/commit/9e630daf81d10485206ec136c3e1a07fe065ffeb)
- [v0.6] `http.Agent` uses different structure for sockets [`86b4122`](https://github.com/http-party/node-http-proxy/commit/86b4122323ca32d455714b1149b99acce49a9e45)
- [minor] Nicer output from test runner [`5c3d41b`](https://github.com/http-party/node-http-proxy/commit/5c3d41bf4e101d0250fb0b3db4a8dc078104dcad)
- Modified the ad-hoc proxy lookup to use _getKey(), rather than the [`553e7fb`](https://github.com/http-party/node-http-proxy/commit/553e7fbc335a9befd166d472f057aa50452a9d40)
- [fix] When client request is aborted, abort server request [`4d43d81`](https://github.com/http-party/node-http-proxy/commit/4d43d81e5c2d7c8088716d4fd574019f43ebb5ce)
- Fixes memory leak when clients abort connections [`c98ccb4`](https://github.com/http-party/node-http-proxy/commit/c98ccb40e9fe5c5198a1605fa8835efc3ff1856c)
- [fix test] Make test runner exit after test exits [`31a8c68`](https://github.com/http-party/node-http-proxy/commit/31a8c6800ddf8d91b477d980605a4c19284a1648)
- [test dist] Run core tests on `npm test` [`8358ef8`](https://github.com/http-party/node-http-proxy/commit/8358ef8a2bdf817c8ed515be7bc9cec0a9b5f486)
- don't add upgrade handler if a custom handler is passed in [`d6ea3a4`](https://github.com/http-party/node-http-proxy/commit/d6ea3a425c203695394eaba4ce8abd57f7809e98)
- always emit end in 0.4 [`182dcd3`](https://github.com/http-party/node-http-proxy/commit/182dcd34555f361c1bb2b8d2777689e64ce32f87)
- [fix] Fix incorrect depth check. [`3ab02f3`](https://github.com/http-party/node-http-proxy/commit/3ab02f3ad7f2c59d73c621695eb238233c16d09c)
- [minor] Everybody loves Unicode [`38bd906`](https://github.com/http-party/node-http-proxy/commit/38bd906f2bc9322b156b92c47457bb7904f0d23a)
- [test minor] Update copyright notice on test runner [`2ccc5c7`](https://github.com/http-party/node-http-proxy/commit/2ccc5c73eaef30ab5a2af7e456bfcc270583c460)
- [minor] When running tests output only basename [`e109eba`](https://github.com/http-party/node-http-proxy/commit/e109eba9724494737021579938c1094c9dfbc8ee)
- [dist] Version bump. 0.8.0 [`5055689`](https://github.com/http-party/node-http-proxy/commit/5055689a11f3b990f848bf2699e0111d9e708d5f)
- Revert "[dist] Adjusted engines field to allow for 0.6; version bump 0.7.7" [`1e33434`](https://github.com/http-party/node-http-proxy/commit/1e33434fcc4772c233825b5aada7472113c0be50)
- changeOrigin option: set the host header to the proxy destination [`f27d26f`](https://github.com/http-party/node-http-proxy/commit/f27d26f4515c900ea4cf1756ef279257a189e308)
- [dist] Adjusted engines field to allow for 0.6; version bump 0.7.7 [`30dac89`](https://github.com/http-party/node-http-proxy/commit/30dac898f30a8508b4c4b4236e9438987f320167)
- [fix] In routing proxy, match line beginning [`63dfc7f`](https://github.com/http-party/node-http-proxy/commit/63dfc7f1757fc9a1a9bceeb3b035e97be6504692)
- [v0.6] Don't use `agent.appendMessage()` [`6655e01`](https://github.com/http-party/node-http-proxy/commit/6655e0164216449a97090651230266da8ced0150)
- bump version 0.7.4 [`3dfba2b`](https://github.com/http-party/node-http-proxy/commit/3dfba2ba4591e0fcd65ff0bfd012b3ab749a0a02)
- bump version 0.7.6 [`c5dc929`](https://github.com/http-party/node-http-proxy/commit/c5dc9295c711177c165bfb34c67407e1a5a0ed06)
- Revert "update outgoing.headers.host incase the destination does proxying" [`2061c71`](https://github.com/http-party/node-http-proxy/commit/2061c713664b044852fdf67aa5e173e5c3b6d874)
- update outgoing.headers.host incase the destination does proxying [`65b7872`](https://github.com/http-party/node-http-proxy/commit/65b7872e6ad433deae4de823c63629cb341bd649)
- bump version 0.7.5 [`b4d41c3`](https://github.com/http-party/node-http-proxy/commit/b4d41c3628ade82792eb361b095ab014a88d537a)
- [minor] Fix indent on timeout notice [`c4124da`](https://github.com/http-party/node-http-proxy/commit/c4124da4f25860497790fc06c97dde6e8985ab73)
- [minor] Change test runner output order [`b76680b`](https://github.com/http-party/node-http-proxy/commit/b76680b045f69e03759bc119f4827f337a8f395d)
- grammar correction [`729496d`](https://github.com/http-party/node-http-proxy/commit/729496d2898612969f5369e7f1c313cb4034f96c)
- [dist] Test runner depends on `async` [`219b0ff`](https://github.com/http-party/node-http-proxy/commit/219b0ff8f8780cde4714267273b0a1637c84679f)
- [test fix] Remove unnecessary console.log in tests/websocket/websocket-proxy-test.js [`f188f4f`](https://github.com/http-party/node-http-proxy/commit/f188f4ffd8c47b6312cd88c28de7e5ac63565047)
- [test refactor] `test/core/{run =&gt; run-single}` [`004be38`](https://github.com/http-party/node-http-proxy/commit/004be38048792d6f1d3efb361a5e7e66d5dbee8d)

## [v0.7.3](https://github.com/http-party/node-http-proxy/compare/v0.7.2...v0.7.3) - 2011-10-03

### Commits

- added what is necessary for having proxyError on Routing proxywq [`b7adf86`](https://github.com/http-party/node-http-proxy/commit/b7adf866b595f0d64a3ef6bde19271276450e723)
- [dist] Version bump. 0.7.3 [`db185bb`](https://github.com/http-party/node-http-proxy/commit/db185bb303ce9c413b2abccbc885f8ec43b61202)

## [v0.7.2](https://github.com/http-party/node-http-proxy/compare/v0.7.1...v0.7.2) - 2011-09-30

### Merged

- [fix] Examples have working require paths now. [`#118`](https://github.com/http-party/node-http-proxy/pull/118)

### Commits

- [fix] Fixed require paths in examples [`2e8d4c6`](https://github.com/http-party/node-http-proxy/commit/2e8d4c6e49e2e9b27443c0b9ae2b96331715402b)
- [websockets] add latest websockets support [`45ef87e`](https://github.com/http-party/node-http-proxy/commit/45ef87e71bc9cccefe5fb6afc3121fb09b8efbc3)
- [dist] Version bump. 0.7.2 [`ccccc45`](https://github.com/http-party/node-http-proxy/commit/ccccc45f11fbe535017b1806fad43578f143649d)

## [v0.7.1](https://github.com/http-party/node-http-proxy/compare/v0.7.0...v0.7.1) - 2011-09-21

### Merged

- Readme fixes [`#114`](https://github.com/http-party/node-http-proxy/pull/114)
- #107: Set x-forwarded-for header (amongst others) [`#110`](https://github.com/http-party/node-http-proxy/pull/110)
- command line tool - make sure targetPort is an integer [`#109`](https://github.com/http-party/node-http-proxy/pull/109)

### Fixed

- [dist] Version bump v0.7.1, closes #107 #112 [`#107`](https://github.com/http-party/node-http-proxy/issues/107)

### Commits

- [test] Added a test for the "x-forwarded-for" header [`66e9820`](https://github.com/http-party/node-http-proxy/commit/66e982060c6c41ad7dfadce1403c8e13d267781a)
- [docs] Updated examples in README.md for 0.7.x API. [`24ef919`](https://github.com/http-party/node-http-proxy/commit/24ef9194953c27fb11a8f1ceb499e5feca11c30c)
- [examples] Updated examples to v0.7.x API. [`8fc8d96`](https://github.com/http-party/node-http-proxy/commit/8fc8d966c4681d514af00516b348105608e13382)
- [examples] More fixes to examples. [`549360a`](https://github.com/http-party/node-http-proxy/commit/549360a462c134cc2b02301070209084ec94c393)
- [fix] x-forwarded http headers should set properly. [`2677bb6`](https://github.com/http-party/node-http-proxy/commit/2677bb6c44244ea0b584db744955bedf7aee2c62)
- [fix] connection.socket -&gt; socket for source of x-forwarded-for data [`1f33943`](https://github.com/http-party/node-http-proxy/commit/1f33943b231cdf2cb619977801c7b0d4e98ab6df)
- Make sure the target port is an integer [`5ba25aa`](https://github.com/http-party/node-http-proxy/commit/5ba25aa3451f131b6c6c8892848a4f236f5b859e)

## [v0.7.0](https://github.com/http-party/node-http-proxy/compare/v0.6.6...v0.7.0) - 2011-09-10

### Fixed

- [fix] Add `x-forward-*` headers for WebSocket requests. Closes #74 [`#74`](https://github.com/http-party/node-http-proxy/issues/74)
- [doc] Document `setMaxSockets`. Fixes #81 [`#81`](https://github.com/http-party/node-http-proxy/issues/81)

### Commits

- [api test dist] Stubbed out the API for the higher-level `RoutingProxy` object to be exposed by `node-http-proxy` [`5927ecd`](https://github.com/http-party/node-http-proxy/commit/5927ecd62a082269c3b6a0ae4f5b4a673784bcdb)
- [api] Finalized the RoutingProxy API [`f765f90`](https://github.com/http-party/node-http-proxy/commit/f765f90ec37defaa2b493f859a982add51e25b76)
- [minor] Move private methods to the bottom of file(s) [`ec03d72`](https://github.com/http-party/node-http-proxy/commit/ec03d72c5d8749aee835f571869f69816be02265)
- [test] Updated tests to reflect finalized API of the RoutingProxy [`734769f`](https://github.com/http-party/node-http-proxy/commit/734769fa9b2c3054d45e33c3e552af80ce3f4740)
- [api doc] Rebuilt httpProxy.createServer() with the newer high-level RoutingProxy API [`598fe2e`](https://github.com/http-party/node-http-proxy/commit/598fe2e38def56518a1f0a8196b2fcb7f1bc569e)
- [minor] Remove commented out debug statements. [`5575bcf`](https://github.com/http-party/node-http-proxy/commit/5575bcf60c87def74d1755b2e5cc73e085dbf8c3)
- [doc] Updated examples [`13eaec5`](https://github.com/http-party/node-http-proxy/commit/13eaec55dc50e2aae164cb8adaa0f1a3c5a66c68)
- Add flow control [`6a7fd14`](https://github.com/http-party/node-http-proxy/commit/6a7fd14bfa9f25694d75cf490e32817ff15a94fe)
- Add flow control [`2b9e09b`](https://github.com/http-party/node-http-proxy/commit/2b9e09b00ac40e6c6de2b68754df7b8e8c1e3878)
- Emit drain if it doesn't happen on its own in 100ms [`37e2541`](https://github.com/http-party/node-http-proxy/commit/37e25418916a31e4a513ee5866d6013858d579cf)
- resume() can throw [`558a8a4`](https://github.com/http-party/node-http-proxy/commit/558a8a4f79716496dbdee13759c8641606458c05)
- [fix] Memory leak hunting. [`ca1d12c`](https://github.com/http-party/node-http-proxy/commit/ca1d12cf1bbfbe98b5159f9c02e2f6c818a1c749)
- Emit drain if it doesn't happen on its own in 100ms [`84be9f2`](https://github.com/http-party/node-http-proxy/commit/84be9f2c3a244c7dbfe2c6320fa26d85cf80ec31)
- resume() can throw [`0c71119`](https://github.com/http-party/node-http-proxy/commit/0c71119ee58ee84068120be72308ecb28cb3e532)
- [dist] Update examples/package.json to conform to nodejitsu style guidelines [`2937229`](https://github.com/http-party/node-http-proxy/commit/29372298208135f571538cc29dcc05f41f79b01c)
- Fixed large DoS vector in the middleware implementation [`0e36912`](https://github.com/http-party/node-http-proxy/commit/0e36912906640fdb007e0492b75c3f6a7b580ec6)
- [api] Added new `close()` method which cleans up sockets from HttpProxy instances [`0eae2a9`](https://github.com/http-party/node-http-proxy/commit/0eae2a913a2173d85478f8c9deec929388284ee2)
- Fixed large DoS vector in the middleware implementation [`07c8d2e`](https://github.com/http-party/node-http-proxy/commit/07c8d2ee6017264c3d4deac9f42ca264a3740b48)
- [minor] More contextual errors when middleware(s) error [`38315f6`](https://github.com/http-party/node-http-proxy/commit/38315f6b1f7b01bc6e55587878a57590135945c0)
- [dist] Update scripts in package.json [`6e1ade0`](https://github.com/http-party/node-http-proxy/commit/6e1ade0bb8174b744abb58df72b098bd96134ca4)
- [dist] Version bump. 0.7.0 [`0182ba3`](https://github.com/http-party/node-http-proxy/commit/0182ba37cd4c618cd50947ea2addef823349e49f)
- [merge] Merge from significant internal refactor in v0.7.x. No external API changes [`f7010e5`](https://github.com/http-party/node-http-proxy/commit/f7010e5169ac23114b9b35da272e9a041743fbb9)
- [minor] Small update to bin/node-http-proxy [`2cd8256`](https://github.com/http-party/node-http-proxy/commit/2cd8256c4d6089409f603655ea3b3a5ccf1fb065)
- [dist] Update .gitignore [`6c1c554`](https://github.com/http-party/node-http-proxy/commit/6c1c5544515bf17f0e6ed3588e16ae1a75f8a25b)
- [doc] Update README.md [`0ba5023`](https://github.com/http-party/node-http-proxy/commit/0ba5023e82fe8a08ed55194644d147c323368f41)
- [doc] Drop version number from README.md. [`bdf48be`](https://github.com/http-party/node-http-proxy/commit/bdf48bea36eae441c775e9321ab6e17db470bf27)
- [dist] Version bump. 0.7.0 [`00e34a1`](https://github.com/http-party/node-http-proxy/commit/00e34a10bd9ffca9e636b2e5aebb4f18ff6765ec)
- [test] Whitespace fix [`3a4d312`](https://github.com/http-party/node-http-proxy/commit/3a4d312eda08e7a5cecb3c82b04023e22f368e2b)
- [dist] Reorganize examples based on classification(s): http, websocket, or middleware [`81d6c31`](https://github.com/http-party/node-http-proxy/commit/81d6c318758231f77a52fab7de174fcc63b7a243)

## [v0.6.6](https://github.com/http-party/node-http-proxy/compare/v0.6.5...v0.6.6) - 2011-08-31

### Commits

- Memory leak hunting. [`f4fcf93`](https://github.com/http-party/node-http-proxy/commit/f4fcf934030e84c15cceca620e974aafc35f1691)
- [fix] Add guards to every throw-able res.end call [`e1c41d0`](https://github.com/http-party/node-http-proxy/commit/e1c41d06942b56f6cd65a079ae78b54456a8bbe1)
- [fix] Only set `x-forward-*` headers if req.connection and req.connection.socket [`de4a6fe`](https://github.com/http-party/node-http-proxy/commit/de4a6fe8a5f78460b030e635e5f4a63312cd4a76)
- [dist] Version bump. 0.6.6 [`967884c`](https://github.com/http-party/node-http-proxy/commit/967884c5de311f21b8405a5030730ef8db912531)

## [v0.6.5](https://github.com/http-party/node-http-proxy/compare/v0.6.4...v0.6.5) - 2011-08-29

### Commits

- [fix] Use `req.connection` for all x-forward-* headers [`f6dc12a`](https://github.com/http-party/node-http-proxy/commit/f6dc12a971fdd892614b32d2a4fb2ff39ddc0e67)
- [dist] Version bump. 0.6.5 [`7beead5`](https://github.com/http-party/node-http-proxy/commit/7beead54654bdc7f9ab4ed0c17000118a3e7b4fc)

## [v0.6.4](https://github.com/http-party/node-http-proxy/compare/v0.6.3...v0.6.4) - 2011-08-28

### Fixed

- Fix #95 Don't look on req.connection if it's not set. [`#95`](https://github.com/http-party/node-http-proxy/issues/95)

### Commits

- [api breaking] Begin refactor to optimize node-http-proxy by managing one instance of HttpProxy per `host:port` location [`d2b0e43`](https://github.com/http-party/node-http-proxy/commit/d2b0e4399e8026d3e2ece78ac8fdb1def6649950)
- [api test] Updated httpProxy.createServer() for new API exposed by simplified HttpProxy object. [`be4562d`](https://github.com/http-party/node-http-proxy/commit/be4562da9fafef8b26856f7f73f6c5a2c4e389b0)
- [test fix] A few minor fixes to ensure basic WebSocket tests are working. Better scope tests by supported protocol [`daf9231`](https://github.com/http-party/node-http-proxy/commit/daf9231a66f10a25782d2227df1b1501099ac5d1)
- [test] Updates for readability [`db10c4a`](https://github.com/http-party/node-http-proxy/commit/db10c4af918c3e4bc448163f4b9e9b9267145d47)
- Add guards to every throw-able res.end call [`7bda25b`](https://github.com/http-party/node-http-proxy/commit/7bda25b1c60d082f0f2fd12fc61b45a33b74f13d)
- [minor] Dont use `.bind()` [`340be42`](https://github.com/http-party/node-http-proxy/commit/340be42797e87fcc11859a771200075e7fe0c5f1)
- [dist] Version bump. 0.6.4 [`216d46d`](https://github.com/http-party/node-http-proxy/commit/216d46dc81bda1aeb0feb1318e34f37bee38c8fb)

## [v0.6.3](https://github.com/http-party/node-http-proxy/compare/v0.5.11...v0.6.3) - 2011-08-28

### Merged

- This adds a flag to ProxyRequest to disable the setting of x-forwarded-[for|port|proto] [`#73`](https://github.com/http-party/node-http-proxy/pull/73)

### Fixed

- Merge branch 'patch-1' of https://github.com/KimSchneider/node-http-proxy [`#80`](https://github.com/http-party/node-http-proxy/issues/80)

### Commits

- [minor] Style updates and whitespace cleaning for consistency [`f0917a3`](https://github.com/http-party/node-http-proxy/commit/f0917a3f97e8df2d58252f14c15ec54369c969ae)
- [api] refactor out middlewares from examples. [`2cf4e0a`](https://github.com/http-party/node-http-proxy/commit/2cf4e0a9e6c78dfd093c098fc87100ae71bc9450)
- [docs] add middleware examples (first draft) [`020290a`](https://github.com/http-party/node-http-proxy/commit/020290a162146c4996831f4f13d71c1dc949f508)
- [fix] use routing table mhen proxying WebSockets. [`efa17ef`](https://github.com/http-party/node-http-proxy/commit/efa17ef6cf614b763fc3b76570a24e750e2ddd31)
- Tested & fixed url middleware example, added comments. [`4cc18f4`](https://github.com/http-party/node-http-proxy/commit/4cc18f4217739b0bd1b3ac88287cc8a23d486b6b)
- [minor] add middleware to node-http-proxy [`b54666f`](https://github.com/http-party/node-http-proxy/commit/b54666ff69c574d842ce1349700c6b6248484d24)
- [minor] add middleware to node-http-proxy [`c773eed`](https://github.com/http-party/node-http-proxy/commit/c773eedeb6d0b22e2b41ab9215cfdc064a8095e3)
- [minor] add url-proxying middleware example [`45f3df8`](https://github.com/http-party/node-http-proxy/commit/45f3df80937ffd5854727c91ea6b0e09cf77e160)
- [fix] Removed bad example. [`2626308`](https://github.com/http-party/node-http-proxy/commit/2626308cd845982c82a284b0d0bc064090aaf116)
- [minor] add example to test concurrency [`6ec8d6c`](https://github.com/http-party/node-http-proxy/commit/6ec8d6caace3797841c0447feb081aa7920aa0dd)
- [minor] add example of using middleware to gzip response [`d3c0697`](https://github.com/http-party/node-http-proxy/commit/d3c06973a1bf1f1c54ca55a5d7f93b77133ef9a2)
- support old (port,host) and (options) style when using middlewares [`7976de1`](https://github.com/http-party/node-http-proxy/commit/7976de1121a40f963e18ea0a4673d185f847df4c)
- [minor] Added body decoder middleware example.  Needs fixing. [`8eaec35`](https://github.com/http-party/node-http-proxy/commit/8eaec3507456731c1138c0b8ebb4e51dedc7c300)
- [minor dist] Use `pkginfo`. Minor updates to variable scoping in `.createServer()` [`5d0bbb3`](https://github.com/http-party/node-http-proxy/commit/5d0bbb38c3af14907567e2dc7c4f84a915b60ce5)
- [doc] add comments to examples/url-middleware.js [`f6484de`](https://github.com/http-party/node-http-proxy/commit/f6484de4112463c74105db82d27f131d64478f1d)
- Handle cases where res.write throws [`be3a0d8`](https://github.com/http-party/node-http-proxy/commit/be3a0d84a1e75b45bc1fc63fe63cdabd9844eb59)
- [minor] code style changes [`8b48b7e`](https://github.com/http-party/node-http-proxy/commit/8b48b7e0af656fdbd6da2b16ec6365beec47c302)
- [doc] note in readme about middleware [`b5d5eaa`](https://github.com/http-party/node-http-proxy/commit/b5d5eaababa276f7d197e4b6a8a771b364b73139)
- Allow forwarding for x-forwarded-[for|port|proto] to enabled layering of http-proxies. [`404818b`](https://github.com/http-party/node-http-proxy/commit/404818b1dce9e77a917ce9f0c187772eb8c18042)
- [style] tidy [`0f8fe8e`](https://github.com/http-party/node-http-proxy/commit/0f8fe8e2460fd27edfba44989b78aa6b8c9a38e2)
- [fix] do not use middleware code if it's not needed [`2012588`](https://github.com/http-party/node-http-proxy/commit/20125889b362c61c85924810de446e1e7b18d079)
- [minor] minor fixes to gzip middleware example [`caa1f49`](https://github.com/http-party/node-http-proxy/commit/caa1f494ab4effabad6d08272c3606c1d82005ea)
- [minor] default enableXForwarded to true [`e3d95ec`](https://github.com/http-party/node-http-proxy/commit/e3d95ecab24700535184df32f3a97e8699099b7f)
- Updating to enableXForwarded [`ee3506a`](https://github.com/http-party/node-http-proxy/commit/ee3506a8e7262f780eeada331898d42ca0e9838a)
- [api] Expose adapted version of `stack` so it can be used with HttpProxy instances not created by `httpProxy.createServer()` [`5d6e6b9`](https://github.com/http-party/node-http-proxy/commit/5d6e6b9f78eb98b28db01490a36b23c1aade133f)
- The number of maxSockets has to be set after the agent is created. Setting the property in the constructor does not work. [`2caa5d2`](https://github.com/http-party/node-http-proxy/commit/2caa5d2b0d55898c133a0bf3a0048ee969efb121)
- [fix] Dont use res.* in proxyWebSocketRequest [`f7452bc`](https://github.com/http-party/node-http-proxy/commit/f7452bc42d963406f7ee19dfa353d72ce3252dd6)
- [fix] fix syntax errors. close issue #86 [`b8f8499`](https://github.com/http-party/node-http-proxy/commit/b8f84994b0515e12c9d87f89f81a8601be47a6ff)
- [api] merge middleware branch [`e6ff8d6`](https://github.com/http-party/node-http-proxy/commit/e6ff8d6597a977baf0caf4f69c75bfa93d7281f3)
- [dist] Version bump. 0.6.3 [`1389b70`](https://github.com/http-party/node-http-proxy/commit/1389b706b5c1d857c571c2947b7c758b5cc70ca3)
- merged [`5ba0f89`](https://github.com/http-party/node-http-proxy/commit/5ba0f89aa356b2e76f5cf64c16e8578d71c45d8a)
- [fix] handler variable in createServer was global (!)  [`25c06a3`](https://github.com/http-party/node-http-proxy/commit/25c06a3a952068de6a24c643cb0c872f7b9a0846)
- [dist] bump version 6.0 [`03475a5`](https://github.com/http-party/node-http-proxy/commit/03475a59445a1c1c1029d0673aafabe63af1e711)
- [dist] bump version 0.6.2 [`d8068a8`](https://github.com/http-party/node-http-proxy/commit/d8068a832d437790ce8680b9b34a9f171d75786c)
- [dist] bump version 5.12 [`5d33ad7`](https://github.com/http-party/node-http-proxy/commit/5d33ad711895b2afcbd6dd5e1c0449cee1ceae7b)
- [dist] bump version 0.6.1 [`fea371d`](https://github.com/http-party/node-http-proxy/commit/fea371dc0a47dfb4f84427e5740e8756f4e5b285)
- [fix] broken RegExp [`549bfea`](https://github.com/http-party/node-http-proxy/commit/549bfeac233888ec84edeec350ed5a7377f3773e)
- [doc] add note on middleware to Using node-http-proxy section of the README [`5bf2d59`](https://github.com/http-party/node-http-proxy/commit/5bf2d59241a7695f43bb89e5cb41ade2ab7a0ad2)

## [v0.5.11](https://github.com/http-party/node-http-proxy/compare/v0.5.10...v0.5.11) - 2011-06-26

### Fixed

- [api] Simplify the usage for the `.changeHeaders` option. Fixes #34 [`#34`](https://github.com/http-party/node-http-proxy/issues/34)

### Commits

- [api doc test] node-http-proxy now emits `websocket:*` on important WebSocket events. Added tests for these features and updated some code docs [`4f85ca0`](https://github.com/http-party/node-http-proxy/commit/4f85ca04e425a7d4df1e46c9cadd6026eeed32f6)
- [doc] Updated docco docs [`f0649d8`](https://github.com/http-party/node-http-proxy/commit/f0649d8d6a9f84ac61d5f173c585fa4307ffb3c3)
- [doc] Added examples/latent-websocket-proxy.js [`fcfe846`](https://github.com/http-party/node-http-proxy/commit/fcfe84626fff15be21ac83ccd69b96bf3ca1f7a2)
- [doc] Added sample for custom error messages using the `proxyError` event [`4cdbf0e`](https://github.com/http-party/node-http-proxy/commit/4cdbf0e8729a0665904b577376240c00e56ad876)
- [doc] Add examples/standalone-websocket-proxy.js [`1ee8ae7`](https://github.com/http-party/node-http-proxy/commit/1ee8ae710497e239716f72d45e2f61ead3995dc3)
- [dist] Version bump. 0.5.11 [`baf0b9e`](https://github.com/http-party/node-http-proxy/commit/baf0b9e25af53e2738812ff78614cc12966e99e3)
- [doc] Small update to code docs [`9d9509f`](https://github.com/http-party/node-http-proxy/commit/9d9509f791c4c566629c2e323259885f1c3db7ed)
- [minor] Add missing space [`b608a02`](https://github.com/http-party/node-http-proxy/commit/b608a029f8aa26f1a74a917e0bec0ac37e4615a0)

## [v0.5.10](https://github.com/http-party/node-http-proxy/compare/v0.5.9...v0.5.10) - 2011-06-13

### Commits

- [refactor] Manage our own internal list of Agent instances [`887c580`](https://github.com/http-party/node-http-proxy/commit/887c5808c90b7128c040e510e237ddb4d034fe3e)
- [doc] Update docco docs for 0.5.9 [`b4ac4d4`](https://github.com/http-party/node-http-proxy/commit/b4ac4d441fe4fb84d463bd889a5ce8d7f4d596ca)
- [test] Update tests to use `localhost` [`a1cdf00`](https://github.com/http-party/node-http-proxy/commit/a1cdf005b98c422c777c88a7d7baf2eeb91f732d)
- [dist] Version bump. 0.5.10 [`7b574d3`](https://github.com/http-party/node-http-proxy/commit/7b574d3d3e52b09a6445c011b8f2ae0d78282111)
- [doc] Bump version in README.md [`653c6ca`](https://github.com/http-party/node-http-proxy/commit/653c6ca1af607623b653d3148b1bb45a304aab87)

## [v0.5.9](https://github.com/http-party/node-http-proxy/compare/v0.5.8...v0.5.9) - 2011-05-23

### Commits

- [fix] Change sec-websocket-location header when proxying WSS --&gt; WS. Added test coverage for this scenario [`028d204`](https://github.com/http-party/node-http-proxy/commit/028d2044e71d70b7bc21d339de29e2275c3be5c2)
- [dist] Version bump. 0.5.9 [`57ca62c`](https://github.com/http-party/node-http-proxy/commit/57ca62c878c9a953f2344719556e05492ece3435)

## [v0.5.8](https://github.com/http-party/node-http-proxy/compare/v0.5.7...v0.5.8) - 2011-05-21

### Commits

- [doc] Regenerate docco docs [`c5fd368`](https://github.com/http-party/node-http-proxy/commit/c5fd368a8d803b6ab47e32e744a6fd6a6ca5361f)
- [doc] Update docco docs [`74120d8`](https://github.com/http-party/node-http-proxy/commit/74120d8988627bb0686d3a26cb8ec1408cc41287)
- [doc] Update to v0.5.7 in code and README.md [`6fd272a`](https://github.com/http-party/node-http-proxy/commit/6fd272ac18240811d8a8a39c85ee483557c414b3)
- [dist] Version bump. 0.5.8. Forwards compatible with new versions of nodejs [`76ecb51`](https://github.com/http-party/node-http-proxy/commit/76ecb51e7b41a23288f922c9c5df3ce40f67bf80)
- [fix] Dont force `Connection: close` now that Keep-Alive is supported [`a86d18b`](https://github.com/http-party/node-http-proxy/commit/a86d18bc7f93d013df715d1f4d88e651846f645d)
- [test] Update to vows description for web-socket-proxy-test.js [`a865fe6`](https://github.com/http-party/node-http-proxy/commit/a865fe662ff04a4badcc90ce2af80d2380c40a85)

## [v0.5.7](https://github.com/http-party/node-http-proxy/compare/v0.5.6...v0.5.7) - 2011-05-19

### Commits

- [api] Add `x-forwarded-proto` and `x-forwarded-port` to proxied HTTP requests [`421895f`](https://github.com/http-party/node-http-proxy/commit/421895fa308d49628bbbb546d542efa96769c3f4)
- [dist] Version bump. v0.5.7. Only good on node v0.4.7. See issue #48. [`0911c17`](https://github.com/http-party/node-http-proxy/commit/0911c1719e641c6e4342027e8d5d82c47c6f310e)
- [fix] Set `x-forwarded-for` from req.connection.socket.remoteAddress if req.connection.remoteAddress is not defined [`e9b3ec9`](https://github.com/http-party/node-http-proxy/commit/e9b3ec9b1d0ebf427e138176b28af44f0f973670)

## [v0.5.6](https://github.com/http-party/node-http-proxy/compare/v0.5.5...v0.5.6) - 2011-05-19

### Commits

- [fix doc] Add `error` handler to reverseProxy request when proxying WebSockets to prevent unhandled ParseError. Rename some variables in proxyWebSocketRequest to make the code more readable [`76580c2`](https://github.com/http-party/node-http-proxy/commit/76580c292a152c0007352a9d383f59e48993cd03)
- [doc] Regenerate docco docs [`bd45216`](https://github.com/http-party/node-http-proxy/commit/bd45216bc9207e5016f394a4bfee2bdffcc669c7)
- [api minor] Small refactor to emit `webSocketProxyError` from a single helper function on any of the various `error` events in the proxy chain [`5d2192e`](https://github.com/http-party/node-http-proxy/commit/5d2192e654f23e1b76e0b66554debe1590a3af64)
- [api] Manual merge of #46: add custom `proxyError` event and enable production error handling. [`652cca3`](https://github.com/http-party/node-http-proxy/commit/652cca37ea321ec9d1d55125217df0214c8090b6)
- [dist] Version bump. v0.5.6 Only good on node v0.4.7. See issue #48. [`f1c0f64`](https://github.com/http-party/node-http-proxy/commit/f1c0f641aa14dc3c267de37370a7369c3131c636)

## [v0.5.5](https://github.com/http-party/node-http-proxy/compare/v0.5.4...v0.5.5) - 2011-05-19

### Commits

- [fix] Change variable references for Websockets, bugs found from using wsbench [`7bf0cae`](https://github.com/http-party/node-http-proxy/commit/7bf0caef9fae86a34719f04f7b9926095fb6a146)
- [dist] Version bump. 0.5.5. Only good on node v0.4.7. See issue #48. [`acacc05`](https://github.com/http-party/node-http-proxy/commit/acacc0561f2efabc0a7859b9a410e954f2dca6fd)

## [v0.5.4](https://github.com/http-party/node-http-proxy/compare/v0.5.3...v0.5.4) - 2011-05-19

### Commits

- [doc] Update docco docs [`faf2618`](https://github.com/http-party/node-http-proxy/commit/faf2618cf3b53a972779514842bc4264ec9541fa)
- [doc] Update README.md to reflect the new HTTPS to HTTP proxy capabilities [`abc01bc`](https://github.com/http-party/node-http-proxy/commit/abc01bce293f7c1a88f9be08b0540407d2b0f4a1)
- [doc test api] Improve node-http-proxy API to allow for HTTPS to HTTP proxying scenarios. Update tests accordingly. [`895f577`](https://github.com/http-party/node-http-proxy/commit/895f577744e3cbcbb5f479c4aacec5323bb001f7)
- [doc] Update examples for HTTPS to HTTP proxying [`91737fa`](https://github.com/http-party/node-http-proxy/commit/91737fadb640f30d3cd959f29069537473207efd)
- [dist] Version bump. 0.5.4. Only good on node v0.4.7. See issue #48. [`c04eec1`](https://github.com/http-party/node-http-proxy/commit/c04eec1c370ca0eb212c96c0896c27b349f7ea97)
- [minor] Update README.md to conform to Github flavored markdown [`32a15dd`](https://github.com/http-party/node-http-proxy/commit/32a15dd79d860343453c38a7eef8339d7b99718b)
- [minor] Update README.md to conform to Github flavored markdown [`521fe27`](https://github.com/http-party/node-http-proxy/commit/521fe271853632563143fb4b76c032f7afa7831a)

## [v0.5.3](https://github.com/http-party/node-http-proxy/compare/v0.5.2...v0.5.3) - 2011-05-18

### Commits

- [test] Continued work around Origin mismatch tests [`44a8566`](https://github.com/http-party/node-http-proxy/commit/44a85664a80fd67e20bbc36d280816dbd1a796c5)
- [doc] Regenerate docco docs [`9e36d2d`](https://github.com/http-party/node-http-proxy/commit/9e36d2d2e619be322bb73092db2a9d72ef6709e8)
- [fix test api] Only change Origin headers in WebSocket requests when the `changeOrigin` option is set explicitly. Added tests to ensure Origin and sec-websocket-origin headers match when proxying websockets. [`9c6c4b9`](https://github.com/http-party/node-http-proxy/commit/9c6c4b908b7d6ce67144ba9d41702b5694254099)
- [test] Improve websocket tests to inspect outgoing and incoming HTTP headers to test origin mismatch bugs [`6e679c8`](https://github.com/http-party/node-http-proxy/commit/6e679c8019e1eb62b2b1da48628f89b8046203fd)
- [test] Refined tests to begin checking Origin == Sec-Websocket-Origin [`9ab54ab`](https://github.com/http-party/node-http-proxy/commit/9ab54ab47fc43d98f3182da9c41487f524933783)
- [doc minor] Update docs and code docs for v0.5.3 release [`03b9087`](https://github.com/http-party/node-http-proxy/commit/03b908744612faed82d9233f3b6d4af70368cf3c)
- [dist] Version bump. v0.5.3. Only good on node v0.4.7. See issue #48. [`d9fa261`](https://github.com/http-party/node-http-proxy/commit/d9fa261cdc97aee71279064e536a4a22edbe3b5b)

## [v0.5.2](https://github.com/http-party/node-http-proxy/compare/v0.5.1...v0.5.2) - 2011-05-17

### Merged

- Readme: fix syntax error, reformat code blocks [`#52`](https://github.com/http-party/node-http-proxy/pull/52)

### Commits

- format markdown for syntax highlighting on GitHub [`28f6dc1`](https://github.com/http-party/node-http-proxy/commit/28f6dc153a7d9fa9b6a08637c90765cf3a07fd3e)
- [doc] Regenerate docco docs [`a5e1e3e`](https://github.com/http-party/node-http-proxy/commit/a5e1e3e70d02f32ab86b711ec4b262df5955a1a9)
- [test] Fix tests in https mode [`1ee6bef`](https://github.com/http-party/node-http-proxy/commit/1ee6beff6aa3087e332701fd3cfda70b4e968ce8)
- [fix] Manage bookkeeping for incoming requests to the underlying sockets behind reverse proxied websocket events. Only use the appropriate variables in the closure scope of the `upgrade` event from this bookkeeping [`85223ea`](https://github.com/http-party/node-http-proxy/commit/85223ea0800ad63ea82783c9dc2dc4a0e3345ae8)
- [minor] Fix syntax in examples/ [`ff82946`](https://github.com/http-party/node-http-proxy/commit/ff829467d33d326c588861a46acc2bf9adbdddd2)
- add spacing around code blocks to fix README rendering [`ab8c264`](https://github.com/http-party/node-http-proxy/commit/ab8c264e6d729de81c93982f97875006e52240f0)
- [dist] Use devDependencies in package.json [`e6c52d4`](https://github.com/http-party/node-http-proxy/commit/e6c52d431f8a32e11cd347fbabeb7a03d0d40790)
- don't highlight non-javascript as javascript [`d5b9ba7`](https://github.com/http-party/node-http-proxy/commit/d5b9ba7180376b8a67b9cbfebe9acf7399cab3ed)
- fix syntax error in README example [`332d2d7`](https://github.com/http-party/node-http-proxy/commit/332d2d780ab62ccc996157dacd2498c568816ffc)
- [minor] Ignore npm modules and debug logs [`e90cbd6`](https://github.com/http-party/node-http-proxy/commit/e90cbd6f148633ef7d3e2de06aaabe1cc493cc37)
- [dist] Include docco module as a dev dependency [`d08c2bb`](https://github.com/http-party/node-http-proxy/commit/d08c2bb525ec661c0c8e6539e28605972b1ae9b8)
- [dist] Version bump. 0.5.2. Only good on node v0.4.7. See issue #48. [`360e79a`](https://github.com/http-party/node-http-proxy/commit/360e79a005d298f40f36ee0e25c34fe534311b09)

## [v0.5.1](https://github.com/http-party/node-http-proxy/compare/v0.5.0...v0.5.1) - 2011-05-10

### Commits

- [dist] Version bump. 0.5.1. Only good on node v0.4.7. See issue #48. [`6c80177`](https://github.com/http-party/node-http-proxy/commit/6c8017734053bc683f32a2b9f0ba18ba0c014855)
- Revert "Fixed "Invalid argument to getAgent" when proxying HTTP" [`40dc9de`](https://github.com/http-party/node-http-proxy/commit/40dc9dee2d1e617af7f85a056d281b4f220f2802)
- [fix] Fix typo in bin/node-http-proxy [`57127a3`](https://github.com/http-party/node-http-proxy/commit/57127a367193bcf12be2b367e1e01cbc57d685fe)
- Merged pull request #39 from timmattison/master. [`ac425d7`](https://github.com/http-party/node-http-proxy/commit/ac425d70ef63b847fe6eb17dbfc4b084d0dd2d20)
- Fixed "Invalid argument to getAgent" when proxying HTTP [`642e158`](https://github.com/http-party/node-http-proxy/commit/642e15805dbd572835bb4fee9527e4f2da658833)

## [v0.5.0](https://github.com/http-party/node-http-proxy/compare/v0.4.2...v0.5.0) - 2011-04-17

### Commits

- [doc] Breakout demo.js into files in example/. Add web-socket-proxy.js example [`6e4bf6a`](https://github.com/http-party/node-http-proxy/commit/6e4bf6a9cbc400fcd2be420649ce08936417dd83)
- [api test doc] Improve HTTPS support. Update minor documentation. Change tests accordingly. [`bf68dc3`](https://github.com/http-party/node-http-proxy/commit/bf68dc30a5c508bc8f533f52c083206b87963811)
- [api] Update WebSocket support to use http.Agent APIs [`b0b0183`](https://github.com/http-party/node-http-proxy/commit/b0b0183c2b54fa63bd2a6f9c92475c7f56d811a3)
- [api] Update `.proxyRequest()` and `.proxyWebSocketRequest()` APIs to take an options hash instead of a set of arguments. Add HTTPS support. [`cfddd12`](https://github.com/http-party/node-http-proxy/commit/cfddd12e821bd6b07ff2dbf0aa543ddfc3664dca)
- [doc api] Update README.md and CHANGELOG.md for v0.5.0. Update bin/node-http-proxy to read files specified in `config.https` [`212009d`](https://github.com/http-party/node-http-proxy/commit/212009df6b08de3c0c97a4e9ec43f60f6bf49ea6)
- [test] Add WebSocket tests [`4d18ac1`](https://github.com/http-party/node-http-proxy/commit/4d18ac1ae611f84e5e0cc599234124d183d81ffd)
- [doc] Regenerate docco docs [`c485c87`](https://github.com/http-party/node-http-proxy/commit/c485c8742c86b504823020d2cf6c1342a1bcce48)
- [doc test] Small updates to README.md. Update to try require socket.io [`12064d8`](https://github.com/http-party/node-http-proxy/commit/12064d8e5debf674cd5d367e563b699f10a4325e)
- [api] Remove winston logging in favor of custom events [`a89b397`](https://github.com/http-party/node-http-proxy/commit/a89b3976b25516db9b601c0327948f3d90fab006)
- [doc] Update README.md [`bd6a262`](https://github.com/http-party/node-http-proxy/commit/bd6a2622ad67b8c7ec15868037a48048207ce0df)
- [dist] Version bump. v0.5.0 [`ddf31b2`](https://github.com/http-party/node-http-proxy/commit/ddf31b22ec71ef9dacca9c178ee26b6314d9fdf4)
- [api] Update `request` event to be consistent by emitting both `req` and `res`. Add `x-forwarded-for` header. [`a3cb527`](https://github.com/http-party/node-http-proxy/commit/a3cb527be5e42d5192400933bf32a361b8c707c4)
- [api] Emit `end` event when done proxying [`5681fc1`](https://github.com/http-party/node-http-proxy/commit/5681fc1a28ff06dfa91d9bf5512c688235cafac4)
- [minor] Small update to README.md [`40c51a7`](https://github.com/http-party/node-http-proxy/commit/40c51a703baaf050b35f60131d3e78b42e7b0858)
- [dist] Move pgriess' websocket client into vendor/* [`7cbf447`](https://github.com/http-party/node-http-proxy/commit/7cbf44732068dc788d31432553b3bdfcfb39f743)

## [v0.4.2](https://github.com/http-party/node-http-proxy/compare/v0.4.1...v0.4.2) - 2011-04-13

### Commits

- [dist] Version bump. 0.4.2. Remove `eyes` dependency. [`a5d88aa`](https://github.com/http-party/node-http-proxy/commit/a5d88aaacc209bdceaf0799e99ff82bdce1bdc10)

## [v0.4.1](https://github.com/http-party/node-http-proxy/compare/v0.4.0...v0.4.1) - 2011-03-20

### Commits

- [dist] Version bump. 0.4.1. Fix package.json [`0d1a3fe`](https://github.com/http-party/node-http-proxy/commit/0d1a3fe99511dda1ac949536a9eb4a045db39979)

## [v0.4.0](https://github.com/http-party/node-http-proxy/compare/v0.3.1...v0.4.0) - 2011-03-20

### Commits

- [api] Further work on refactor for node 0.4.0 [`e39a9f9`](https://github.com/http-party/node-http-proxy/commit/e39a9f93d2f9ab6ea769fad5e9dda25d022d8a1a)
- [doc] Added docco generated literate coding documentation [`3bc7d16`](https://github.com/http-party/node-http-proxy/commit/3bc7d16adc48ad1aa1161bb02bd0c27d4fb20639)
- [doc api test] Wrap things up for v0.4.0 release: Add hostnameOnly routing to ProxyTable, add more documentation, fix edge-cases until they can be further investigated in node.js core [`5715318`](https://github.com/http-party/node-http-proxy/commit/571531820e2233b0d2f7268a1d4db8510fcabf91)
- [api] First pass at removing pool and working with node v0.4.0 [`9faa924`](https://github.com/http-party/node-http-proxy/commit/9faa924a29544cfd84c28cb1c45489f495e3806a)
- [doc api test] Rename HttpProxy.pause to HttpProxy.resume. Update documentation and tests accordingly [`4110448`](https://github.com/http-party/node-http-proxy/commit/4110448046dd945afe3e092968d9382d573a369a)
- [doc] Added more documentation [`973f19f`](https://github.com/http-party/node-http-proxy/commit/973f19fd5a14e3bfad5f67e54710a4076a469fe0)
- [doc] Regenerate docco docs [`6c42f04`](https://github.com/http-party/node-http-proxy/commit/6c42f045241194061c3786ba5827aebf88070201)
- [api] Force connection header to be `close` until keep-alive is replemented [`3fd3c96`](https://github.com/http-party/node-http-proxy/commit/3fd3c96fa05fda45c7ef9ff44594644ac54f4a1e)
- [dist] Version bump. 0.4.0 [`cbb5fbc`](https://github.com/http-party/node-http-proxy/commit/cbb5fbccd0e65c51eba14e75ef44184714cc8971)
- [api test] All tests are passing when run as individual files [`389159d`](https://github.com/http-party/node-http-proxy/commit/389159da1b91ab60b8de3c379d84e76c703e6b59)
- [minor doc] Update demo and small fix to node-http-proxy [`d8c5406`](https://github.com/http-party/node-http-proxy/commit/d8c54063dc5961fa619f7c04fa2d225da9aa1439)
- [fix] Fixed cli parsing issue when --argument=value is not used [`34cba38`](https://github.com/http-party/node-http-proxy/commit/34cba38c297d6dcb845e95b9e1ce0271da1631d2)
- [test] Small update to proxy-table-test.js [`3588687`](https://github.com/http-party/node-http-proxy/commit/3588687874eb691fe59407a207d38efa418211d0)
- [minor] Expose version on module [`1dd9b3b`](https://github.com/http-party/node-http-proxy/commit/1dd9b3b15088a3c4595faae64822969014a61d52)
- [doc] Update to v0.3.1 in README.md [`8ef2e1f`](https://github.com/http-party/node-http-proxy/commit/8ef2e1fe33e0fca2b80c0d6474dba994e625f094)
- [dist] Change package.json for npm version bump [`0e7f362`](https://github.com/http-party/node-http-proxy/commit/0e7f3626718ecf108f3cafa814b0f4ffb3e6faa2)

## [v0.3.1](https://github.com/http-party/node-http-proxy/compare/v0.3.0...v0.3.1) - 2010-11-22

### Commits

- [api test doc] Updated tests. Added ProxyTable functionality [`bedc7a3`](https://github.com/http-party/node-http-proxy/commit/bedc7a3ae57d5ec07b372a550fa69772f9fbc19e)
- [test] Simplified tests. Added tests for experimental websocket support [`8c3e993`](https://github.com/http-party/node-http-proxy/commit/8c3e993833e2a09376fdb5e7c847ff00b53e70d8)
- [test doc api] Added forward proxy functionality with tests [`c06f4bf`](https://github.com/http-party/node-http-proxy/commit/c06f4bf7fe50f29677dc5a5aad596193fc893018)
- [dist minor] Removed vendored pool. Changed all references of sys to util [`8251296`](https://github.com/http-party/node-http-proxy/commit/8251296d7f5c472ec523316e905d678042b043d3)
- WebSocket proxy support, fixed 304 code halting [`7249ef3`](https://github.com/http-party/node-http-proxy/commit/7249ef3ee776c66acc95036dc76a2d08dc3f6350)
- [api] pseduo-vendor pool until pull request is finalized [`7c2eb5d`](https://github.com/http-party/node-http-proxy/commit/7c2eb5de3531f20ea92c99dd8ab207d26be9dce8)
- No-server fix [`f84880f`](https://github.com/http-party/node-http-proxy/commit/f84880fcd946e55585d8e901e5bc32933f629837)
- [api test bin doc] Added bin script and simple logging [`00014d6`](https://github.com/http-party/node-http-proxy/commit/00014d624c052e7404ce96c7e06769440c4eae2a)
- [debug] Removed pool as a dependency for stress test [`73381cf`](https://github.com/http-party/node-http-proxy/commit/73381cf71ae92b9ed1c2da5986aa7ca31a7cf2e8)
- 'end' event becomes 'close', added more try-catch handling [`cd78af5`](https://github.com/http-party/node-http-proxy/commit/cd78af5feaa67c5005df921a8d1a61575a58fca2)
- Added support of automatic websocket tunneling, added test for it [`56003b5`](https://github.com/http-party/node-http-proxy/commit/56003b527625b2d83a191f3172005c87856aa87d)
- [debug] Better debug messages to try to determine if pool is slowly losing clients to forever busy [`dd1918d`](https://github.com/http-party/node-http-proxy/commit/dd1918dc360dc0f9553c35c82f3f0f93ac3bfb46)
- [doc dist] Version bump. Added CHANGELOG.md [`de53d5e`](https://github.com/http-party/node-http-proxy/commit/de53d5eb2c3d671be0ad0e736a6435c3bf5f55f4)
- Moved error handling to response.on('end'), fixed error handling in websocket's part [`7e61f0c`](https://github.com/http-party/node-http-proxy/commit/7e61f0cf5725dedf37b956545639c2d6129855d3)
- [minor] Pushing hot-fix from Mikeal for vendored pool repo [`60791f3`](https://github.com/http-party/node-http-proxy/commit/60791f361f8a11f9d1bad2c6366bf0ce72b40f66)
- [api] Integrated commits from donnerjack and worked on pool changes [`3bb458e`](https://github.com/http-party/node-http-proxy/commit/3bb458e115037bc27691705d255b0d2e2504a9f1)
- [doc] Updated Copyright ... added Fedor [`9128a8c`](https://github.com/http-party/node-http-proxy/commit/9128a8c5a15d0f64a0bae946f3e741ea708bc56f)
- [minor] Listen to error event on pool so we dont fail out unexpectedly anymore [`711258e`](https://github.com/http-party/node-http-proxy/commit/711258ef469d064cc0dbe0f0320ed1047ed0bd54)
- adding more debugging messages [`5d54ea5`](https://github.com/http-party/node-http-proxy/commit/5d54ea58c93c26635e0de96871e824baffea34dd)
- adding some debug messages for live testing [`4069a7e`](https://github.com/http-party/node-http-proxy/commit/4069a7e98c22a48bae7fd57ad5f315d0e5006dfc)
- [minor] Listen to error events re-emitted by pool into the ClientRequest [`f8bff4c`](https://github.com/http-party/node-http-proxy/commit/f8bff4c618ab2a6b6185ac973cd0e21cea19c23a)
- [minor] Updated max clients for pool [`32aaf74`](https://github.com/http-party/node-http-proxy/commit/32aaf74e95f8a39d847b352ca984145e7abe89a6)
- [debug] Trying to repair pool busy client growth [`7b0ea85`](https://github.com/http-party/node-http-proxy/commit/7b0ea85e2ac58d5f711f64b855f746fb2423a276)
- [debug] Roll back last commit ... connection = close was ineffective [`266e524`](https://github.com/http-party/node-http-proxy/commit/266e5246eacb4877bb6ab557e6e6b9b8434ad612)

## [v0.3.0](https://github.com/http-party/node-http-proxy/compare/v0.2.0...v0.3.0) - 2010-09-10

### Commits

- [api] Revert to old 0.1.x codebase for bug testing and performance comparison [`66afb2a`](https://github.com/http-party/node-http-proxy/commit/66afb2a2a35a479512ce2601c89b82f13596fc9f)
- [api test dist doc] Updated for 0.3.0 release [`a9084b9`](https://github.com/http-party/node-http-proxy/commit/a9084b923afa66c3004abec4951ff02e031631da)
- [api] Object creation is cheap for HttpProxy, so lets take advantage [`9f0aeac`](https://github.com/http-party/node-http-proxy/commit/9f0aeacab1a632136f5905a0d03ad04be9f93f51)
- [doc] Update contributors for 0.3.0 [`6d47d98`](https://github.com/http-party/node-http-proxy/commit/6d47d98f5345b7f335c3b93f8e4a31dd90235dda)

## [v0.2.0](https://github.com/http-party/node-http-proxy/compare/v0.1.5...v0.2.0) - 2010-09-07

### Commits

- [dist] Version bump and update to README + LICENCE. Word to Mikeal for coming thru for 0.2.0 [`69c162d`](https://github.com/http-party/node-http-proxy/commit/69c162dc3da334b2ece0a19be5ea4c8da7e0fe87)
- [api dist] Merge of branch 0.2.0 [`fd61828`](https://github.com/http-party/node-http-proxy/commit/fd618289338ca2d7595f695c0b8531b40145bbca)
- [api] Completely refactored node-http-proxy with help from Mikeal [`1221939`](https://github.com/http-party/node-http-proxy/commit/1221939accf00467adb25f8908e991e984043c85)
- [api minor debug] Remove debug code, set Connection header if not set [`6d08f24`](https://github.com/http-party/node-http-proxy/commit/6d08f24c863e071eb4a0d3ede15656e5e7c27c4b)
- [debug] Added some debugging to figure out why AB wont complete a test with v0.2.0 [`9715ebd`](https://github.com/http-party/node-http-proxy/commit/9715ebd40bdbbe883eb383676d5b0df24968dd72)
- [api] Integrated a little more from Mikeal to make our return headers consistent [`eb39018`](https://github.com/http-party/node-http-proxy/commit/eb39018fd0b5751dd90fabce905997e52f2ffecd)
- [doc] Updated README.md [`f291efb`](https://github.com/http-party/node-http-proxy/commit/f291efbaa4360d6e7ff4004cc11f8df0d737c1d0)

## v0.1.5 - 2010-09-02

### Commits

- [api] More changes for createServer api [`5d94ae2`](https://github.com/http-party/node-http-proxy/commit/5d94ae27bc2d56d1f817b0cf1dfdb01dcc376393)
- added colors and asciimo [`d490b50`](https://github.com/http-party/node-http-proxy/commit/d490b50ada8c1024cb785335966b71d69fae3407)
- [api] First commit of http-proxy [`30b68c1`](https://github.com/http-party/node-http-proxy/commit/30b68c153270619119ec36615bb54ee7a2816ecc)
- updating demo [`c4b7c0d`](https://github.com/http-party/node-http-proxy/commit/c4b7c0d8a0cc5fd7f43257594bd0a71c7bd12a63)
- initial release v0.1.0, sure to have many updates coming. [`85f7372`](https://github.com/http-party/node-http-proxy/commit/85f73723415ec54539721777e77d5d10de383469)
- fleshing out demo [`994f748`](https://github.com/http-party/node-http-proxy/commit/994f7481ce07c15afa5ab993b79d920b8220be44)
- [docs] added benchmarks [`bbed176`](https://github.com/http-party/node-http-proxy/commit/bbed17640f84e56aaea06c6d4eb7d04952957fce)
- updated paths to use npm [`972c8c0`](https://github.com/http-party/node-http-proxy/commit/972c8c05274c72c7320291389f88b0694ac290ca)
- added spark demo [`d0ad931`](https://github.com/http-party/node-http-proxy/commit/d0ad93176d8430301a8a42f8c2b817674ce7ba32)
- [test] Updated tests to include support for latent requests [`095e86a`](https://github.com/http-party/node-http-proxy/commit/095e86aa653c1c8e07cd1403697e0e4b638b8294)
- started to flesh out simple demo based on tests [`2fb5ffb`](https://github.com/http-party/node-http-proxy/commit/2fb5ffba7765462e95badd0f7243e65395a3fd2e)
- added createServer but hated it, gonna remove [`b1eb13e`](https://github.com/http-party/node-http-proxy/commit/b1eb13eb70b67ea76f5ab720d566894677a53ca2)
- [test] Updated node-http-proxy tests [`2f265a2`](https://github.com/http-party/node-http-proxy/commit/2f265a23e4a10971495d0bd7b324b7ba786e5065)
- [api] Updated request hashes to use a unique identifier [`c887a75`](https://github.com/http-party/node-http-proxy/commit/c887a757623f5a3d7d1e0fafeb00b96731c89872)
- [api] Updated http-proxy to work with vows [`ead7567`](https://github.com/http-party/node-http-proxy/commit/ead7567db8099264a2001fd876cded84bc4f111f)
- [dist] Renamed node-proxy to node-http-proxy, updated package.json [`2f49810`](https://github.com/http-party/node-http-proxy/commit/2f49810ef86f49927991f32ae42605f1118b0c25)
- updating docs, almost there [`6e651f4`](https://github.com/http-party/node-http-proxy/commit/6e651f420f4d1e15dbbf823a8e3b311e9533c805)
- changed api to better reflect nodes api. updated demos, tests, docs [`bde98f4`](https://github.com/http-party/node-http-proxy/commit/bde98f489234fe22f49468011b7e342cd108603f)
- updating docs [`341bbd4`](https://github.com/http-party/node-http-proxy/commit/341bbd404f3fd81e65197b3830c3fa9e544bc1e7)
- fixed npm package, i think. bumped version 0.1.1 [`fca40da`](https://github.com/http-party/node-http-proxy/commit/fca40da694d8df17ed6140265e374c0ceabd1167)
- updated demo [`b622702`](https://github.com/http-party/node-http-proxy/commit/b62270210e7ad3c54fd6b2c86bde9f9942328a67)
- added readme [`d6a2f8a`](https://github.com/http-party/node-http-proxy/commit/d6a2f8aa7dae3f6721b9607a702c68b1ad7fc692)
- [api] Corrected chain of argument passing [`da55777`](https://github.com/http-party/node-http-proxy/commit/da55777a92d100a5ddb7a8267e56ba26bd8c2270)
- updated demo [`e9511ea`](https://github.com/http-party/node-http-proxy/commit/e9511eafdf9ada6a0ce6defb3c5f2299411633b1)
- [deploy] Added package.json [`dce80b9`](https://github.com/http-party/node-http-proxy/commit/dce80b9b4546064da1943e0e396e19b41390588a)
- updated readme [`76d0649`](https://github.com/http-party/node-http-proxy/commit/76d0649abcafd80509af922503c5544e646bcebb)
- update to docs and package.json [`d15bba4`](https://github.com/http-party/node-http-proxy/commit/d15bba4c1d2cbdaf0af27f3adcaa1db9b534d968)
- [minor] Removed eyes dependency [`eaeed83`](https://github.com/http-party/node-http-proxy/commit/eaeed8306d6dc6e1b30223cf6d59cda6d5bb76de)
- merge [`93505a4`](https://github.com/http-party/node-http-proxy/commit/93505a422c688b7f41fdaf304270c893ef4cf09a)
- fixed additional port / server mismatches for new api [`15c18b6`](https://github.com/http-party/node-http-proxy/commit/15c18b612d6cd5a1f3ae46b5590dda1fc586fb35)
- [doc] added nodejitsu.com link to ReadMe. http-proxy is used in our front facing load-balancers. look for bugs...try to improve benchmarks.... ^_^ [`6661753`](https://github.com/http-party/node-http-proxy/commit/6661753f07dcf4e5ae684df4d1709f3c238346c9)
- removed extra self, updated colors requirement, bumped to version 0.1.3 [`9bc5b6f`](https://github.com/http-party/node-http-proxy/commit/9bc5b6f8621fb2a37e84524c3e5b91aab9b45675)
- fixed pathing issue, bumped version 0.1.3 [`ede6490`](https://github.com/http-party/node-http-proxy/commit/ede649037e08b615a8995179f46bc701550354d6)
- updated docs [`07d96bb`](https://github.com/http-party/node-http-proxy/commit/07d96bb8887a7880a21a739e0a8f495698e7e79e)
- updated docs [`1594367`](https://github.com/http-party/node-http-proxy/commit/15943675edef490d9b8732345a750bc5ab1f5d7e)
- updated readme [`fb8c5ab`](https://github.com/http-party/node-http-proxy/commit/fb8c5abd3c2a722c1c18046dcf2fffea4fa7d050)
- updated docs [`17b6c69`](https://github.com/http-party/node-http-proxy/commit/17b6c6998544572300fc9d4faa63af1aee4c3d88)
- updated docs [`c8dd8c4`](https://github.com/http-party/node-http-proxy/commit/c8dd8c4e28e09f25c161980316b259d81d5a4e91)
- updated package.json again [`ddba155`](https://github.com/http-party/node-http-proxy/commit/ddba155377942259554842f37de98c508130fe11)
- initial release v0.1.0, sure to have many updates coming. [`6a1baa2`](https://github.com/http-party/node-http-proxy/commit/6a1baa25ccf9fc3a3fc4d1a4764c968993e48cab)
- bumped to version 0.1.5 [`b195a16`](https://github.com/http-party/node-http-proxy/commit/b195a16406534912161671448a53d6633a1f2458)
- updated readme [`9aa2216`](https://github.com/http-party/node-http-proxy/commit/9aa22162f139ab2fa6df6b11e2a96336ee1d2612)
- added spark demo [`d408e39`](https://github.com/http-party/node-http-proxy/commit/d408e39ed6dbd44709d0164a95ad9bc67f76ba13)
- bumped to version 0.1.4. improved on api [`82b8228`](https://github.com/http-party/node-http-proxy/commit/82b822827d35a54501068f9880111473e19c72f9)
- initial release v0.1.0, sure to have many updates coming. [`1e04552`](https://github.com/http-party/node-http-proxy/commit/1e04552bd8f39e3dcba36bbf7fb36674e5c0c9ff)
- updated readme [`0a2eaaa`](https://github.com/http-party/node-http-proxy/commit/0a2eaaa7db690f86aca8c0b952f745e806ad818c)
- updating docs [`198000f`](https://github.com/http-party/node-http-proxy/commit/198000feefd525125a2031557b3556978a057bde)
- [api] Added createServer api to node-http-proxy [`2e2b55f`](https://github.com/http-party/node-http-proxy/commit/2e2b55f113eb3bc81c43717c0db5de695fb694c1)
