"""
智能导办服务模块
提供基于政务服务数据的智能导办功能
支持服务推荐、办事指导、流程查询等
"""

import os
import re
from qa_database import QADatabase
from sensitive_filter import sensitive_filter


class SmartGuideService:
    """智能导办服务"""

    def __init__(self):
        # 初始化问答数据库
        self.qa_db = QADatabase()
        
        # 服务分类映射
        self.service_categories = {
            '证件办理': ['身份证', '护照', '驾驶证', '营业执照', '许可证'],
            '社会保障': ['社保', '医保', '养老', '失业', '工伤', '生育'],
            '户籍管理': ['户口', '迁移', '落户', '户籍', '居住证'],
            '税务服务': ['纳税', '税务', '发票', '申报', '减免'],
            '教育服务': ['入学', '转学', '学籍', '助学', '奖学金'],
            '医疗卫生': ['就医', '预约', '体检', '疫苗', '健康证'],
            '交通出行': ['车辆', '违章', '年检', '过户', '停车'],
            '住房保障': ['公积金', '房产', '租房', '购房', '补贴'],
            '企业服务': ['注册', '变更', '注销', '年检', '备案'],
            '民政服务': ['结婚', '离婚', '收养', '救助', '优抚']
        }
        
        # 常见关键词映射
        self.keyword_mapping = {
            '办证': '证件办理',
            '办卡': '证件办理', 
            '申请': '证件办理',
            '社保卡': '社会保障',
            '医保卡': '社会保障',
            '落户': '户籍管理',
            '迁户': '户籍管理',
            '报税': '税务服务',
            '缴税': '税务服务',
            '上学': '教育服务',
            '入学': '教育服务',
            '看病': '医疗卫生',
            '就医': '医疗卫生',
            '买车': '交通出行',
            '卖车': '交通出行',
            '买房': '住房保障',
            '租房': '住房保障',
            '开公司': '企业服务',
            '注册公司': '企业服务',
            '结婚证': '民政服务',
            '离婚': '民政服务'
        }

    def analyze_user_intent(self, user_input):
        """
        分析用户意图，识别所需的政务服务类型
        
        Args:
            user_input (str): 用户输入的问题或需求描述
            
        Returns:
            dict: 包含意图分析结果的字典
        """
        try:
            print(f"🎯 智能导办分析用户意图: {user_input}")
            
            # 安全校验
            safety_check = sensitive_filter.check_sensitive_content(user_input)
            if not safety_check['is_safe']:
                return {
                    'success': False,
                    'error': safety_check['message'],
                    'intent': None,
                    'services': []
                }
            
            # 意图识别
            detected_categories = []
            matched_keywords = []
            
            # 1. 直接关键词匹配
            for keyword, category in self.keyword_mapping.items():
                if keyword in user_input:
                    if category not in detected_categories:
                        detected_categories.append(category)
                    matched_keywords.append(keyword)
            
            # 2. 服务分类关键词匹配
            for category, keywords in self.service_categories.items():
                for keyword in keywords:
                    if keyword in user_input:
                        if category not in detected_categories:
                            detected_categories.append(category)
                        matched_keywords.append(keyword)
            
            # 3. 从问答数据库中查找相关服务
            related_services = self._find_related_services(user_input)
            
            return {
                'success': True,
                'intent': detected_categories[0] if detected_categories else '综合服务',
                'categories': detected_categories,
                'keywords': matched_keywords,
                'services': related_services,
                'user_input': user_input
            }
            
        except Exception as e:
            print(f"❌ 智能导办意图分析失败: {e}")
            return {
                'success': False,
                'error': f'意图分析失败: {str(e)}',
                'intent': None,
                'services': []
            }

    def _find_related_services(self, user_input):
        """
        从问答数据库中查找相关的政务服务
        
        Args:
            user_input (str): 用户输入
            
        Returns:
            list: 相关服务列表
        """
        related_services = []
        
        try:
            # 遍历问答数据库，查找匹配的服务
            for qa_item in self.qa_db.qa_data:
                # 检查问题标题匹配
                if any(keyword in qa_item['question'] for keyword in user_input.split()):
                    service_info = {
                        'id': qa_item['id'],
                        'title': qa_item['question'],
                        'type': qa_item['type'],
                        'description': qa_item['answer'][:100] + '...' if len(qa_item['answer']) > 100 else qa_item['answer'],
                        'has_online_service': qa_item['doit'] == '是',
                        'url': qa_item['url'],
                        'phone': qa_item['phone']
                    }
                    related_services.append(service_info)
                
                # 检查关键词匹配
                elif qa_item['keywords']:
                    for keyword in qa_item['keywords']:
                        if keyword in user_input:
                            service_info = {
                                'id': qa_item['id'],
                                'title': qa_item['question'],
                                'type': qa_item['type'],
                                'description': qa_item['answer'][:100] + '...' if len(qa_item['answer']) > 100 else qa_item['answer'],
                                'has_online_service': qa_item['doit'] == '是',
                                'url': qa_item['url'],
                                'phone': qa_item['phone']
                            }
                            related_services.append(service_info)
                            break
            
            # 去重并限制返回数量
            seen_ids = set()
            unique_services = []
            for service in related_services:
                if service['id'] not in seen_ids:
                    unique_services.append(service)
                    seen_ids.add(service['id'])
                    if len(unique_services) >= 5:  # 最多返回5个相关服务
                        break
            
            return unique_services
            
        except Exception as e:
            print(f"❌ 查找相关服务失败: {e}")
            return []

    def get_service_recommendations(self, category=None):
        """
        获取服务推荐
        
        Args:
            category (str): 服务分类，如果为None则返回热门服务
            
        Returns:
            dict: 包含推荐服务的结果字典
        """
        try:
            print(f"🎯 获取服务推荐，分类: {category}")
            
            recommendations = []
            
            if category and category in self.service_categories:
                # 根据分类推荐服务
                category_keywords = self.service_categories[category]
                
                for qa_item in self.qa_db.qa_data:
                    # 检查是否属于该分类
                    if qa_item['type'] == category or any(keyword in qa_item['question'] for keyword in category_keywords):
                        service_info = {
                            'id': qa_item['id'],
                            'title': qa_item['question'],
                            'type': qa_item['type'],
                            'description': qa_item['answer'][:100] + '...' if len(qa_item['answer']) > 100 else qa_item['answer'],
                            'has_online_service': qa_item['doit'] == '是',
                            'url': qa_item['url'],
                            'phone': qa_item['phone']
                        }
                        recommendations.append(service_info)
                        
                        if len(recommendations) >= 8:  # 每个分类最多8个推荐
                            break
            else:
                # 返回热门服务（前10个）
                for qa_item in self.qa_db.qa_data[:10]:
                    service_info = {
                        'id': qa_item['id'],
                        'title': qa_item['question'],
                        'type': qa_item['type'],
                        'description': qa_item['answer'][:100] + '...' if len(qa_item['answer']) > 100 else qa_item['answer'],
                        'has_online_service': qa_item['doit'] == '是',
                        'url': qa_item['url'],
                        'phone': qa_item['phone']
                    }
                    recommendations.append(service_info)
            
            return {
                'success': True,
                'category': category or '热门服务',
                'recommendations': recommendations,
                'total_count': len(recommendations)
            }
            
        except Exception as e:
            print(f"❌ 获取服务推荐失败: {e}")
            return {
                'success': False,
                'error': f'获取服务推荐失败: {str(e)}',
                'recommendations': []
            }

    def generate_guide_response(self, analysis_result):
        """
        根据意图分析结果生成智能导办回复
        
        Args:
            analysis_result (dict): 意图分析结果
            
        Returns:
            str: 格式化的回复内容
        """
        try:
            if not analysis_result['success']:
                return analysis_result['error']
            
            user_input = analysis_result['user_input']
            intent = analysis_result['intent']
            services = analysis_result['services']
            
            # 构建回复内容
            response_parts = []
            
            # 1. 意图确认
            if intent != '综合服务':
                response_parts.append(f"## 🎯 服务识别\n\n我理解您需要办理 **{intent}** 相关的业务。")
            else:
                response_parts.append(f"## 🎯 智能导办\n\n我来为您提供 **{user_input}** 相关的办事指导。")
            
            # 2. 相关服务推荐
            if services:
                response_parts.append("## 📋 相关服务")
                for i, service in enumerate(services, 1):
                    service_text = f"\n**{i}. {service['title']}**"
                    if service['type']:
                        service_text += f" ({service['type']})"
                    service_text += f"\n{service['description']}"
                    
                    if service['has_online_service'] and service['url']:
                        service_text += f"\n🔗 [在线办理]({service['url']})"
                    
                    if service['phone']:
                        service_text += f"\n📞 咨询电话：{service['phone']}"
                    
                    response_parts.append(service_text)
            else:
                response_parts.append("## 📋 服务建议\n\n暂未找到完全匹配的服务，建议您：\n- 点击下方服务图标浏览所有可用服务\n- 拨打人工咨询电话：010-88888888")
            
            # 3. 操作指引
            response_parts.append("## 💡 操作指引\n\n- **点击服务图标**：浏览更多政务服务\n- **描述具体需求**：我会为您提供更精准的指导\n- **人工咨询**：010-88888888")
            
            return "\n\n".join(response_parts)
            
        except Exception as e:
            print(f"❌ 生成导办回复失败: {e}")
            return f"抱歉，生成回复时出现错误。请拨打人工咨询电话：010-88888888"
