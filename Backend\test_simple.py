"""
简单测试智能问数服务
"""

print("开始测试...")

try:
    import pandas as pd
    print("✅ pandas导入成功")
    
    import os
    print("✅ os导入成功")
    
    # 检查数据文件
    if os.path.exists('../DB/veg_price.xlsx'):
        print("✅ 找到数据文件: ../DB/veg_price.xlsx")
        df = pd.read_excel('../DB/veg_price.xlsx')
        print(f"📊 数据形状: {df.shape}")
        print(f"📊 列名: {df.columns.tolist()}")
        print("📊 前3行:")
        print(df.head(3))
    elif os.path.exists('DB/veg_price.xlsx'):
        print("✅ 找到数据文件: DB/veg_price.xlsx")
        df = pd.read_excel('DB/veg_price.xlsx')
        print(f"📊 数据形状: {df.shape}")
        print(f"📊 列名: {df.columns.tolist()}")
        print("📊 前3行:")
        print(df.head(3))
    else:
        print("❌ 未找到数据文件")
        
    # 测试导入智能问数服务
    from smart_data_service import SmartDataService
    print("✅ SmartDataService导入成功")
    
    # 初始化服务
    service = SmartDataService()
    print("✅ 服务初始化成功")
    
    if service.df is not None:
        print(f"✅ 数据加载成功: {len(service.df)} 行")
    else:
        print("❌ 数据加载失败")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
