#!/usr/bin/env python3
"""
测试智能问数服务
"""

import sys
import os

# 添加Backend目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

try:
    from smart_data_service import SmartDataService
    
    print("✅ 成功导入SmartDataService")
    
    # 初始化服务
    service = SmartDataService()
    print("✅ 智能问数服务初始化成功")
    
    # 检查数据
    if service.df is not None:
        print(f"📊 数据概览:")
        print(f"  - 数据行数: {len(service.df)}")
        print(f"  - 菜品种类: {service.df['variety'].nunique()}")
        print(f"  - 时间范围: {service.df['date'].min()} 到 {service.df['date'].max()}")
        print(f"  - 主要菜品: {list(service.df['variety'].value_counts().head(5).index)}")
        
        # 测试分析功能
        print("\n🔍 测试分析功能:")
        test_questions = [
            "最近蔬菜价格怎么样？",
            "白菜的价格趋势如何？",
            "哪种蔬菜最便宜？"
        ]
        
        for question in test_questions:
            print(f"\n问题: {question}")
            result = service.analyze_question(question)
            if result['success']:
                print("✅ 分析成功")
                print(f"📝 回答长度: {len(result['analysis_report'])} 字符")
                print(f"📊 图表数量: {len(result.get('charts', []))}")
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
    else:
        print("❌ 数据未加载")
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
