<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能政务平台</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Markdown 渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>

    <!-- Chart.js 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-container">
            <div class="logo-section">
                <img src="pic/shukelogo.png" alt="数科logo" class="logo">
                <div class="logo-divider"></div>
                <div class="ai-badge">
                    <span class="ai-label">A I</span>
                    <span class="ai-text">联通数科智能政务平台</span>
                </div>
            </div>
            
            <nav class="nav-links">
                
                <a href="#" class="nav-link"><i class="fas fa-user"></i> 无障碍</a>
                <a href="#" class="nav-link"><i class="fas fa-list"></i> 网站导航</a>
            </nav>
        </div>
        
    
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 左侧AI助手区域 -->
        <aside class="ai-assistant">
            <div class="ai-greeting">
                <h2><span class="ai-hi">您好！</span><span class="ai-desc">我是联通数科智能小政！<br>您有什么问题可以问我哦！</span></h2>
                <p class="ai-subtitle">@政策解答小能手 @政务办事小能手</p>
                <p class="ai-disclaimer">本服务由人工智能生成，回答内容仅供参考！感谢您的理解与支持！</p>
            </div>
            <div class="ai-avatar-center">
                <img src="pic/robot2.gif" alt="智能机器人" style="max-width:300px;max-height:500px;">
            </div>

            <!-- 个性卡片功能区 -->
            <div class="personality-cards">
               
                <div class="cards-grid">
                    <div class="personality-card" data-service="市情概况">
                        <div class="card-icon purple">
                            <i class="fas fa-city"></i>
                        </div>
                        <div class="card-content">
                            <h4>市情概况</h4>
                            <p>城市概况信息</p>
                        </div>
                    </div>

                    <div class="personality-card" data-service="智能问策">
                        <div class="card-icon blue">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="card-content">
                            <h4>智能问策</h4>
                            <p>政策相关信息</p>
                        </div>
                    </div>

                    <div class="personality-card" data-service="智能导办">
                        <div class="card-icon pink">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="card-content">
                            <h4>智能导办</h4>
                            <p>办事服务导航</p>
                        </div>
                    </div>

                    <div class="personality-card" data-service="智能问数">
                        <div class="card-icon green">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="card-content">
                            <h4>智能问数</h4>
                            <p>数据统计分析</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ai-info">
                <div class="info-item">
                    <i class="fas fa-headset"></i>
                    <span>如需专业人工服务，您可以拨打咨询电话
                        （010-88888888/010-66666666），我们将竭诚为您服务！</span>
                </div>
            </div>
        </aside>

        <!-- 中央对话区域 -->
        <section class="chat-area">
            <div class="chat-messages">
                <!-- AI欢迎语，仅首次进入显示 -->
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>您好，我是联通数科智能小政！很高兴为您服务，有什么问题您可以问我哦，我会尽力帮您解答！<br>如果还没想好，您可以试着问我：<br><span class="suggest-question" data-question="某某市如何办理教育入学？" role="button" tabindex="0" aria-label="点击询问：某某市如何办理教育入学？">某某市如何办理教育入学？</span> <span class="suggest-question" data-question="某某市如何领取养老金？" role="button" tabindex="0" aria-label="点击询问：某某市如何领取养老金？">某某市如何领取养老金？</span></p>
                    </div>
                    <div class="message-time">刚刚</div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-area">
                <!-- 高频办事板块 -->
                <div class="service-section">
                    <div class="service-grid">
                        <!-- 服务项和翻页按钮由JS动态渲染 -->
                    </div>
                </div>
                
                <div class="input-actions">
                    <button class="action-btn" aria-label="新建对话"><i class="fas fa-plus"></i> 新建对话</button>

                    <button class="action-btn active" id="qaListBtn" aria-label="政务问答"><i class="fas fa-comments"></i> 政务问答</button>
                    <button class="action-btn" id="knowledgeSearchBtn" aria-label="知识库检索"><i class="fas fa-database"></i> 知识库检索</button>
                    <button class="action-btn" aria-label="知识库+联网搜索"><i class="fas fa-globe"></i> 知识库+联网搜索</button>
                </div>
                
                <div class="input-container">
                    
                    <div class="input-box">
                        <input type="text" placeholder="请描述您遇到的问题，输入问题提问" class="chat-input">
                        <button class="send-btn" aria-label="发送消息">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部信息 -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-links">
                <a href="#">网站地图</a>
                <a href="#">隐私声明</a>
            </div>
            <div class="footer-info">
                <p>某某市人民政府主办 某某市政务服务和大数据局承办</p>
                <p>某某市政务服务热线：010-12345</p>
            </div>
            <div class="footer-buttons">
                <a class="footer-btn" href="https://www.gov.cn/zhengce/index.htm" target="_blank" rel="noopener noreferrer">中国政府网</a>
                <button class="footer-btn" aria-label="信用查询">信用XXX</button>
                <a class="footer-btn" href="https://zfwzgl.www.gov.cn/boxpro/custom/pucha" target="_blank" rel="noopener noreferrer">网站找错</a>
                <!-- 删除二维码区域 -->
            </div>
        </div>
    </footer>

    <!-- 不满意反馈弹窗 -->
    <div id="feedbackModal" class="feedback-modal">
        <div class="feedback-modal-content">
            <div class="feedback-modal-header">
                <h3>请告诉我们您不满意的原因，我们将继续改进</h3>
            </div>
            <div class="feedback-modal-body">
                <div class="feedback-reasons">
                    <button class="feedback-reason-btn" data-reason="答非所问">答非所问</button>
                    <button class="feedback-reason-btn" data-reason="回答内容看不懂">回答内容看不懂</button>
                    <button class="feedback-reason-btn" data-reason="回答不完整">回答不完整</button>
                    <button class="feedback-reason-btn" data-reason="回答错误">回答错误</button>
                    <button class="feedback-reason-btn" data-reason="敏感/有害回答">敏感/有害回答</button>
                    <button class="feedback-reason-btn" data-reason="对我没有帮助">对我没有帮助</button>
                </div>
                <div class="feedback-textarea-container">
                    <textarea id="feedbackText" class="feedback-textarea" placeholder="请输入您的意见" maxlength="500"></textarea>
                    <div class="feedback-char-count">
                        <span id="charCount">0</span>/500
                    </div>
                </div>
            </div>
            <div class="feedback-modal-footer">
                <button id="cancelFeedback" class="feedback-btn-cancel">取消</button>
                <button id="confirmFeedback" class="feedback-btn-confirm">确定</button>
            </div>
        </div>
    </div>

    <!-- 内容纠错弹窗 -->
    <div id="correctionModal" class="correction-modal">
        <div class="correction-modal-content">
            <div class="correction-modal-header">
                <h3>内容纠错</h3>
                <p class="correction-subtitle">请详细描述错误内容，我们将及时核实并修正</p>
            </div>
            <div class="correction-modal-body">
                <div class="correction-form-group">
                    <label for="originalContent" class="correction-label">原文内容 <span class="required">*</span></label>
                    <textarea id="originalContent" class="correction-textarea" placeholder="请复制粘贴需要纠错的原文内容" maxlength="1000" required></textarea>
                    <div class="correction-char-count">
                        <span id="originalCharCount">0</span>/1000
                    </div>
                </div>

                <div class="correction-form-group">
                    <label for="errorReason" class="correction-label">错误原因 <span class="required">*</span></label>
                    <textarea id="errorReason" class="correction-textarea" placeholder="请说明具体的错误原因，如：信息过时、数据错误、表述不准确等" maxlength="500" required></textarea>
                    <div class="correction-char-count">
                        <span id="errorCharCount">0</span>/500
                    </div>
                </div>

                <div class="correction-form-group">
                    <label for="referenceSource" class="correction-label">参考依据</label>
                    <textarea id="referenceSource" class="correction-textarea" placeholder="请提供正确信息的参考依据，如：官方文件、法规条文、权威网站链接等（选填）" maxlength="500"></textarea>
                    <div class="correction-char-count">
                        <span id="referenceCharCount">0</span>/500
                    </div>
                </div>

                <div class="correction-contact-group">
                    <label for="contactInfo" class="correction-label">联系方式（选填）</label>
                    <input type="text" id="contactInfo" class="correction-input" placeholder="如需反馈处理结果，请留下您的联系方式" maxlength="100">
                </div>
            </div>
            <div class="correction-modal-footer">
                <button id="cancelCorrection" class="correction-btn-cancel">取消</button>
                <button id="confirmCorrection" class="correction-btn-confirm">提交纠错</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>