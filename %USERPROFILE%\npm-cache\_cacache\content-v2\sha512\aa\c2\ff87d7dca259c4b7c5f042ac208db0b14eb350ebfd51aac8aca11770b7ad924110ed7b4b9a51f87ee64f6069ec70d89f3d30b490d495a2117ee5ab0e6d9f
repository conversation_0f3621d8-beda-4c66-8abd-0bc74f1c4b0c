{"_id": "side-channel-list", "name": "side-channel-list", "dist-tags": {"latest": "1.0.0"}, "versions": {"1.0.0": {"name": "side-channel-list", "version": "1.0.0", "description": "Store information about any JS value in a side channel, using a linked list", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "evalmd README.md && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-list.git"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/side-channel-list/issues"}, "homepage": "https://github.com/ljharb/side-channel-list#readme", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "side-channel-list@1.0.0", "gitHead": "d4879491c2533a30961ba464c2613f7d7c7cf936", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "shasum": "10cb5984263115d3b7a0e336591e290a830af8ad", "tarball": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "fileCount": 13, "unpackedSize": 14741, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQCEbYLueEqQgC2W2ADBUY7qFxILlurdtKbgX+rGwu2GAQIfBBk7jjnu67EZEHrZtBSxBPMyxt6Qp46T9wNJkflUrA=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/side-channel-list_1.0.0_1733862024904_0.5380686564616168"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-10T20:20:24.903Z", "1.0.0": "2024-12-10T20:20:25.133Z", "modified": "2024-12-10T20:20:25.467Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "Store information about any JS value in a side channel, using a linked list", "homepage": "https://github.com/ljharb/side-channel-list#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-list.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/side-channel-list/issues"}, "license": "MIT", "readme": "# side-channel-list <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nStore information about any JS value in a side channel, using a linked list.\n\nWarning: this implementation will leak memory until you `delete` the `key`.\nUse [`side-channel`](https://npmjs.com/side-channel) for the best available strategy.\n\n## Getting started\n\n```sh\nnpm install --save side-channel-list\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst getSideChannelList = require('side-channel-list');\n\nconst channel = getSideChannelList();\n\nconst key = {};\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n\nchannel.set(key, 42);\n\nchannel.assert(key); // does not throw\nassert.equal(channel.has(key), true);\nassert.equal(channel.get(key), 42);\n\nchannel.delete(key);\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/side-channel-list\n[npm-version-svg]: https://versionbadg.es/ljharb/side-channel-list.svg\n[deps-svg]: https://david-dm.org/ljharb/side-channel-list.svg\n[deps-url]: https://david-dm.org/ljharb/side-channel-list\n[dev-deps-svg]: https://david-dm.org/ljharb/side-channel-list/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/side-channel-list#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/side-channel-list.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/side-channel-list.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/side-channel-list.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=side-channel-list\n[codecov-image]: https://codecov.io/gh/ljharb/side-channel-list/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/side-channel-list/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/side-channel-list\n[actions-url]: https://github.com/ljharb/side-channel-list/actions\n", "readmeFilename": "README.md"}