{"_id": "basic-auth", "_rev": "78-846f63ee38ae4cebb32cf2ca1fde0dfa", "name": "basic-auth", "dist-tags": {"latest": "2.0.1"}, "versions": {"0.0.1": {"name": "basic-auth", "version": "0.0.1", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-basic-auth", "bugs": {"url": "https://github.com/visionmedia/node-basic-auth/issues"}, "dist": {"shasum": "31ddb65843f6c35c6fea7beb46a987cb8ce18924", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-0.0.1.tgz", "integrity": "sha512-sCz6E05DMvrA9dUBGJFfnQ3qs+/lQkVr7qjOT5XMMNfpTzWbpkElpzXfnbNlBjPnDQyz0uBFJ4nELJRIdcKoNQ==", "signatures": [{"sig": "MEQCIC7OlSny6YFRpDDTBWcd6OBOOxb6YXyOH6sh2eBO0piwAiBvEWvSEu1+61h1JpKKr/asNomJkwYGkbBzseuOI8WsNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-basic-auth", "type": "git"}, "_npmVersion": "1.3.15", "description": "generic basic auth parser", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.0": {"name": "basic-auth", "version": "1.0.0", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/visionmedia/node-basic-auth", "bugs": {"url": "https://github.com/visionmedia/node-basic-auth/issues"}, "dist": {"shasum": "111b2d9ff8e4e6d136b8c84ea5e096cb87351637", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.0.tgz", "integrity": "sha512-qzxS7/bW/LSiKZzdZw3isPjiVmzXbJLM3ImZZ62WMR3oJQAyqy094Nnb0TA2ZZm65xB7nu0acfTQ99z7wwCDCw==", "signatures": [{"sig": "MEUCIC5LlIiXZlFzHwct0YZf1d5V9lCqZTcyvNQpi0fdbCQhAiEAmBns6cs/Wec4qnmSJUkX7hSPb0cwBqHCfymKPw2z0iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "111b2d9ff8e4e6d136b8c84ea5e096cb87351637", "gitHead": "099e8c703ea3994d72240492aba9b115517cf45e", "scripts": {"test": "make test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/visionmedia/node-basic-auth", "type": "git"}, "_npmVersion": "1.4.16", "description": "generic basic auth parser", "directories": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.1": {"name": "basic-auth", "version": "1.0.1", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/basic-auth#readme", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "dist": {"shasum": "4bae1dbfbf0aec4dc5dc47a8d3675b50140f3bf8", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.1.tgz", "integrity": "sha512-3RS6sgcUJ+v/J+dPdSnwh/NltSkr1Y77atfeOVKM+ioZWGpH/6emrGBMSBCYqSwfAC6MSs7vEDaa6XjXKgs9kg==", "signatures": [{"sig": "MEUCIAY49L8gHvK/UuydVFFqbNVR0Ltx88KCYmAfcMOmvqwUAiEA763mR91VNF/FI8wqAct4tN2bu5hpa3dX1ai4b1GxLKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "4bae1dbfbf0aec4dc5dc47a8d3675b50140f3bf8", "engines": {"node": ">= 0.8"}, "gitHead": "0eea397ee77a5170c196d031d0038c2506301f27", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/jshttp/basic-auth.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "node.js basic auth parser", "directories": {}, "_nodeVersion": "2.0.0", "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.13"}}, "1.0.2": {"name": "basic-auth", "version": "1.0.2", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@1.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/basic-auth", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "dist": {"shasum": "42900137fb0c91462d14b3739c14bf2bb624171d", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.2.tgz", "integrity": "sha512-ahPRRXSB01g52OXyC4IRlp4qoHhGIG0PRF/HtcnQ7nbXvEgCC0h/Kdujlr0fxUDQEpLCAlM7P9IZSYbfOjTSKw==", "signatures": [{"sig": "MEYCIQD1FR8sMnl9KcJw93/elR59BmBaymixD62xC/kn5wAeMwIhAOq5C4Pu1/ZmkuemVci7tgGJfLeXP5m42yUWi/5NkqAJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "42900137fb0c91462d14b3739c14bf2bb624171d", "engines": {"node": ">= 0.6"}, "gitHead": "5e2d7ef5bad393e17ed0035f4926846137559260", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/basic-auth", "type": "git"}, "_npmVersion": "1.4.28", "description": "node.js basic auth parser", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.15"}}, "1.0.3": {"name": "basic-auth", "version": "1.0.3", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@1.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/basic-auth", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "dist": {"shasum": "41f55523e589405038ee3567958c62a5ed70551a", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.3.tgz", "integrity": "sha512-fkXSqXkCTgBy5HVNQ2wP1Fnc/JZjnREwM3hfU8h5RyUN8X9WMQBJem6ZmlsSs7Y4f3fQ7z09vcARgOa0iaPaZA==", "signatures": [{"sig": "MEQCIEF4H0UtbIt5QsUsZCvsuAJT9SN+9MQUA9e3YTzcPL1dAiBUAEUaiWkTTNADL5LUXpbIN/aXO0lQG+DRRXIn/EcXqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "41f55523e589405038ee3567958c62a5ed70551a", "engines": {"node": ">= 0.6"}, "gitHead": "eec1944e5a54c907676822096d40bc7c52c0aff3", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/basic-auth", "type": "git"}, "_npmVersion": "1.4.28", "description": "node.js basic auth parser", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.17"}}, "1.0.4": {"name": "basic-auth", "version": "1.0.4", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/basic-auth#readme", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "dist": {"shasum": "030935b01de7c9b94a824b29f3fccb750d3a5290", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.4.tgz", "integrity": "sha512-uvq3I/zC5TmG0WZJDzsXzIytU9GiiSq23Gl27Dq9sV81JTfPfQhtdADECP1DJZeJoZPuYU0Y81hWC5y/dOR+Yw==", "signatures": [{"sig": "MEYCIQCGdkAfKSc6rVA70anePok8actqEi9WuVRFFz/NawD1GAIhANkzvG52yzBd1X8T2sTnwdJ6bhHQF7BoLxJ/+cd13uX7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "030935b01de7c9b94a824b29f3fccb750d3a5290", "engines": {"node": ">= 0.6"}, "gitHead": "4682d99600383bad5a266efbaa5055657dd9891d", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/basic-auth.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "node.js basic auth parser", "directories": {}, "_nodeVersion": "4.4.3", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/basic-auth-1.0.4.tgz_1462938878912_0.717464140150696", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0": {"name": "basic-auth", "version": "1.1.0", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/basic-auth", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "dist": {"shasum": "45221ee429f7ee1e5035be3f51533f1cdfd29884", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.1.0.tgz", "integrity": "sha512-CtGuTyWf3ig+sgRyC7uP6DM3N+5ur/p8L+FPfsd+BbIfIs74TFfCajZTHnCw6K5dqM0bZEbRIqRy1fAdiUJhTA==", "signatures": [{"sig": "MEQCIDBqpj7F7VHcXMCk3JEI59f6jKm9TwZvGvbPFA3bNCRGAiBTQWy2rmyfYn8MT23oMSp6wsU0Q1VfXnHXa/pzBo9fow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "45221ee429f7ee1e5035be3f51533f1cdfd29884", "engines": {"node": ">= 0.6"}, "gitHead": "5a0fcd9f4dbf72e2a105d4e815987d3492925875", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/basic-auth", "type": "git"}, "_npmVersion": "1.4.28", "description": "node.js basic auth parser", "directories": {}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.10.2", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/basic-auth-1.1.0.tgz_1479495426183_0.6680189804174006", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0": {"name": "basic-auth", "version": "2.0.0", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@2.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/basic-auth#readme", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "dist": {"shasum": "015db3f353e02e56377755f962742e8981e7bbba", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.0.tgz", "integrity": "sha512-BNzMKPNNIV3+C02TSO9oILCAPS/htf/upL+TRV2gOnPwSQbFKuL249fwVozDNbSq/AeyoRoVeQgJcK4sN77aHg==", "signatures": [{"sig": "MEUCIC7PXPsmq2YjeS9X+URyBkd+zmutyoCGL/BP2/9bd7TrAiEA4g0lfN1etNnACtsDWSnjsRuZWLELwiNR18og/9O8SqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "015db3f353e02e56377755f962742e8981e7bbba", "engines": {"node": ">= 0.8"}, "gitHead": "704457317b54809b750274cc794d05d43cbb190c", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/basic-auth.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "node.js basic auth parser", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"safe-buffer": "5.1.1"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/basic-auth-2.0.0.tgz_1505275895449_0.5881294559221715", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "basic-auth", "version": "2.0.1", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "_id": "basic-auth@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/basic-auth#readme", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "dist": {"shasum": "b998279bf47ce38344b4f3cf916d4679bbf51e3a", "tarball": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "signatures": [{"sig": "MEYCIQCs0LgOVgPqNf4i73zrZuL2NTyib+AeJujMIlcYHhtDOwIhAOTUNt5VzjNLZf2I/Q8AzLCwBFrp6TRRto4IN5ChEmp7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboxNLCRA9TVsSAnZWagAABjAP/36HVdFJZd41XWu4V4Lq\nC9iexogRT/eQ3offpCaOg6Z4phYNUhvspCNzpvWDK+PgFHhL+iT1JaseR8L6\ns10FIWUsV8MWf+PhRyaixrHTlYeKtw86EKCDagUQ+3F/WuR0t1l8tJ61oXmX\nt9mGcmdk9lC7gtq29nY/OP5pIR1wgNcFKXvxhdQ5qQNm1xBJM8HlhkrbJcNV\ncCS22k010P7YGny1hBsDd9d60s8N0qRYYv5jZdAEPyvfe7EuAAuhfjkd7OWZ\notO2E5ynAkHit5MekUdV0s+pUciO8doBCT+KzueHQWt5r7RD4+RgxlCMqzG7\nk4b7uZe0hxvnzRJPoTdWZzdrA5VPwAD+0+TKGUNUzqkeSZViYajwxXBF4WHT\necB+0KUhPMI6IIGdeiIYmL7tSa6hwzqxTy4HhFQ3VHZAi+MCcNFMZ/LuOQyX\nMUiAtxzyLMXCYfe0U3Lk/fBdrcBZAYAHreAdYXwksV2PPvoEc28/qomshKDo\nbVfv3pJvaXDHIEiJBR/4g99ploihTkmOxbqF5ogDBXoim+ql24zuEgHJD3v/\nKPmXTGCyeS6Jrk7hL2bw8mmy0uVb6/LYpKOvcWtTwkIW32DxQMM7zK0y1wq+\n9tSN539Byca0qpbDdh1hgGjwtfaciNb3fGNLGF3QuLYbvrOd4K+cE67vJdbI\nZMlO\r\n=cB++\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "bde0bac8ba99f5d461a339cb6b8a598a0586d699", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/basic-auth.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "node.js basic auth parser", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"safe-buffer": "5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "5.2.0", "eslint": "5.6.0", "istanbul": "0.4.5", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/basic-auth_2.0.1_1537413962464_0.07525772488456206", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-11-30T19:20:04.033Z", "modified": "2025-05-14T14:56:13.389Z", "0.0.1": "2013-11-30T19:20:06.195Z", "1.0.0": "2014-07-07T00:19:25.361Z", "1.0.1": "2015-05-05T05:54:12.251Z", "1.0.2": "2015-06-13T03:51:35.314Z", "1.0.3": "2015-07-02T04:45:20.715Z", "1.0.4": "2016-05-11T03:54:40.196Z", "1.1.0": "2016-11-18T18:57:06.779Z", "2.0.0": "2017-09-13T04:11:36.377Z", "2.0.1": "2018-09-20T03:26:02.582Z"}, "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/basic-auth#readme", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "repository": {"url": "git+https://github.com/jshttp/basic-auth.git", "type": "git"}, "description": "node.js basic auth parser", "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "readme": "# basic-auth\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nGeneric basic auth Authorization header field parser for whatever.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```\n$ npm install basic-auth\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar auth = require('basic-auth')\n```\n\n### auth(req)\n\nGet the basic auth credentials from the given request. The `Authorization`\nheader is parsed and if the header is invalid, `undefined` is returned,\notherwise an object with `name` and `pass` properties.\n\n### auth.parse(string)\n\nParse a basic auth authorization header string. This will return an object\nwith `name` and `pass` properties, or `undefined` if the string is invalid.\n\n## Example\n\nPass a Node.js request object to the module export. If parsing fails\n`undefined` is returned, otherwise an object with `.name` and `.pass`.\n\n<!-- eslint-disable no-unused-vars, no-undef -->\n\n```js\nvar auth = require('basic-auth')\nvar user = auth(req)\n// => { name: 'something', pass: 'whatever' }\n```\n\nA header string from any other location can also be parsed with\n`auth.parse`, for example a `Proxy-Authorization` header:\n\n<!-- eslint-disable no-unused-vars, no-undef -->\n\n```js\nvar auth = require('basic-auth')\nvar user = auth.parse(req.getHeader('Proxy-Authorization'))\n```\n\n### With vanilla node.js http server\n\n```js\nvar http = require('http')\nvar auth = require('basic-auth')\nvar compare = require('tsscmp')\n\n// Create server\nvar server = http.createServer(function (req, res) {\n  var credentials = auth(req)\n\n  // Check credentials\n  // The \"check\" function will typically be against your user store\n  if (!credentials || !check(credentials.name, credentials.pass)) {\n    res.statusCode = 401\n    res.setHeader('WWW-Authenticate', 'Basic realm=\"example\"')\n    res.end('Access denied')\n  } else {\n    res.end('Access granted')\n  }\n})\n\n// Basic function to validate credentials for example\nfunction check (name, pass) {\n  var valid = true\n\n  // Simple method to prevent short-circut and use timing-safe compare\n  valid = compare(name, 'john') && valid\n  valid = compare(pass, 'secret') && valid\n\n  return valid\n}\n\n// Listen\nserver.listen(3000)\n```\n\n# License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/basic-auth/master\n[coveralls-url]: https://coveralls.io/r/jshttp/basic-auth?branch=master\n[downloads-image]: https://badgen.net/npm/dm/basic-auth\n[downloads-url]: https://npmjs.org/package/basic-auth\n[node-version-image]: https://badgen.net/npm/node/basic-auth\n[node-version-url]: https://nodejs.org/en/download\n[npm-image]: https://badgen.net/npm/v/basic-auth\n[npm-url]: https://npmjs.org/package/basic-auth\n[travis-image]: https://badgen.net/travis/jshttp/basic-auth/master\n[travis-url]: https://travis-ci.org/jshttp/basic-auth\n", "readmeFilename": "README.md", "users": {"xuu": true, "izzy": true, "j.su": true, "hyteer": true, "melliw": true, "monjer": true, "quafoo": true, "cooboor": true, "hckhanh": true, "im_dodo": true, "jaguarj": true, "tomchao": true, "dburdese": true, "drveresh": true, "josudoey": true, "maxblock": true, "pdilyard": true, "tfentonz": true, "tmurngon": true, "artmadiar": true, "gavinning": true, "3creatives": true, "garrickajo": true, "ghkddbguse": true, "langri-sha": true, "leonardorb": true, "rocket0191": true, "sammok2003": true, "sanketss84": true, "garenyondem": true, "icerainnuaa": true, "mikermcneil": true, "bobjohnson23": true, "hugojosefson": true, "iori20091101": true, "justintormey": true, "mobeicaoyuan": true, "mucahitnezir": true, "rowantaubitz": true, "markthethomas": true, "serge-nikitin": true, "fabian.moron.zirfas": true}}