{"_id": "gopd", "_rev": "5-00039d9c1c4528d08bf80575c3d614b1", "name": "gopd", "dist-tags": {"latest": "1.2.0"}, "versions": {"1.0.1": {"name": "gopd", "version": "1.0.1", "keywords": ["ecmascript", "javascript", "getownpropertydescriptor", "property", "descriptor"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "gopd@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/gopd#readme", "bugs": {"url": "https://github.com/ljharb/gopd/issues"}, "dist": {"shasum": "29ff76de69dac7489b7c0918a5788e56477c332c", "tarball": "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==", "signatures": [{"sig": "MEQCIBDoe9bXK8xSrmnY7XYTdbRAJEJVVA0b45+dVu3Eg9wIAiB2VNbwGpWsB1yrINoQU+ws9M1JNuQxLmGMAk0b5gsNLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYg1UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp80g/9GhDy6/of0kgDHy/13dC/BlTRUfBcftwssUMjnwLWOAAR2xQC\r\nGt59WehnCNUeGfXCzfnU9Y/jBnDi7cIMFLk75QRkqH45oapYOgm7Yu0eQSbS\r\n3j2dQiSCoDva/lTl0RjtT148pbSKXc692hodS1T9nIAxbtjB5EE1mpau+n2n\r\n6oECynnKt7bPmsYswzkyaut2Eu8qMCZzhLwmqNFoCTchqXWI62kS7ijfFoiC\r\nkhFmYC36JVOyxyZ/cCPo88otFPjixBvz2oPl8XJKYckd/JAJov62wx63YjYx\r\nAV8Vy+vzYLeiMNOm9Mca/Z6Rfv8s4pJnzMu8vJTjJwtSh9mnuLFnpiPAB4Ws\r\n84PQodRR6p1Ib5yzBmJFxX37oNt39KvBgpzDEkA5hJ7sEnzs8vsqcfa/heKF\r\nI4UyjrD8zXAfpQs/n5RIEaedyIpzY8eBCSE0rE5bzrp9KuOF54I3zk4rwWMP\r\nwc+zcvXoghXKtyMfFgL8IxrIyAmP/8jT/is9rUQNEHyFSCw/8TJJnZJofxkk\r\nBMGQu9RfmbW2kIAevM4DbBzqbI1O9Ih0tqYa4MbOuGN+Mmap3Dooa/CNVmXW\r\n9KTev2I/6JGBek5DMnr2vtkuweSFs1fDsP4kKIRKyhUU7IRfOVkE3I3bhz3N\r\nvd4oK+PvfP0FWnP7zVA1D/jaJZ0xZMk9xQ8=\r\n=Z17m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e3236ba6cdee15eef18be63dcc46045c035d4429", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "evalmd README.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/gopd.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.0.0", "dependencies": {"get-intrinsic": "^1.1.3"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.1", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/gopd_1.0.1_1667370324550_0.****************", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "gopd", "version": "1.1.0", "keywords": ["ecmascript", "javascript", "getownpropertydescriptor", "property", "descriptor"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "gopd@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/gopd#readme", "bugs": {"url": "https://github.com/ljharb/gopd/issues"}, "dist": {"shasum": "df8f0839c2d48caefc32a025a49294d39606c912", "tarball": "https://registry.npmjs.org/gopd/-/gopd-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-FQoVQnqcdk4hVM4JN1eromaun4iuS34oStkdlLENLdpULsuQcTyXj8w7ayhuUfPwEYZ1ZOooOTT6fdA9Vmx/RA==", "signatures": [{"sig": "MEUCIQCYFfoSS2RtJe8o1roQ5J/pb+FBM20OWS2vaS79GgCargIga4Ybl23B69VWyCmHpedvbZfCenjmQ0/HqRjrHJn/E3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9648}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "69fd8eadf521333bfbcf2d8d4414ab138f835ab8", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "tsc -p . && attw -P", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "evalmd README.md", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/gopd.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "dependencies": {"get-intrinsic": "^1.2.4"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "^5.8.0-dev.********", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/gopd_1.1.0_1732943773840_0.****************", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "gopd", "version": "1.2.0", "description": "`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.", "main": "index.js", "exports": {".": "./index.js", "./gOPD": "./gOPD.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "tsc -p . && attw -P", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/gopd.git"}, "keywords": ["ecmascript", "javascript", "getownpropertydescriptor", "property", "descriptor"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/gopd/issues"}, "homepage": "https://github.com/ljharb/gopd#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "gopd@1.2.0", "gitHead": "7f867f97d7c895a982a3b631287fd2c36d39c2ff", "types": "./index.d.ts", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "shasum": "89f56b8217bdbc8802bd299df6d7f1081d7e51a1", "tarball": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "fileCount": 12, "unpackedSize": 9869, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5XMsZlbC6fTaTnci63FcU7n0ajefDF31NCM5hiSNjAgIhAKOtFohttj8T5Ejj+saoHldL3znN/rzau+8Ljry8gxan"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/gopd_1.2.0_1733329312532_0.9387002246933662"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-11-02T05:36:15.165Z", "modified": "2024-12-04T16:21:52.914Z", "1.0.0": "2022-11-02T05:36:15.356Z", "1.0.1": "2022-11-02T06:25:24.709Z", "1.1.0": "2024-11-30T05:16:14.022Z", "1.2.0": "2024-12-04T16:21:52.727Z"}, "bugs": {"url": "https://github.com/ljharb/gopd/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/gopd#readme", "keywords": ["ecmascript", "javascript", "getownpropertydescriptor", "property", "descriptor"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/gopd.git"}, "description": "`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# gopd <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.\n\n## Usage\n\n```javascript\nvar gOPD = require('gopd');\nvar assert = require('assert');\n\nif (gOPD) {\n\tassert.equal(typeof gOPD, 'function', 'descriptors supported');\n\t// use gOPD like Object.getOwnPropertyDescriptor here\n} else {\n\tassert.ok(!gOPD, 'descriptors not supported');\n}\n```\n\n[package-url]: https://npmjs.org/package/gopd\n[npm-version-svg]: https://versionbadg.es/ljharb/gopd.svg\n[deps-svg]: https://david-dm.org/ljharb/gopd.svg\n[deps-url]: https://david-dm.org/ljharb/gopd\n[dev-deps-svg]: https://david-dm.org/ljharb/gopd/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/gopd#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/gopd.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/gopd.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/gopd.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=gopd\n[codecov-image]: https://codecov.io/gh/ljharb/gopd/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/gopd/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/gopd\n[actions-url]: https://github.com/ljharb/gopd/actions\n", "readmeFilename": "README.md"}