"""
智能问数服务模块
基于蔬菜价格数据的智能分析和可视化服务
支持自然语言问答、数据分析、图表生成等功能
"""

import os
import re
import pandas as pd
import requests
import json
import logging
from datetime import datetime
from sensitive_filter import sensitive_filter

# 配置日志
# 确保日志目录存在
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file_path = os.path.join(log_dir, 'smart_data.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 创建专用的智能问数日志记录器
smart_data_logger = logging.getLogger('SmartData')


class SmartDataService:
    """智能问数服务"""

    def __init__(self):
        # 初始化数据
        self.df = None
        self.load_data()
        
        # DeepSeek API配置
        self.api_key = "***********************************"
        self.api_url = "https://api.deepseek.com/chat/completions"

    def load_data(self):
        """加载蔬菜价格数据"""
        smart_data_logger.info("🔄 开始加载蔬菜价格数据...")

        try:
            # 尝试从不同路径加载数据
            data_paths = [
                'DB/veg_price.xlsx',
                '../DB/veg_price.xlsx',
                'veg_price.xlsx'
            ]

            smart_data_logger.debug(f"📂 尝试从以下路径加载数据: {data_paths}")

            for path in data_paths:
                if os.path.exists(path):
                    smart_data_logger.info(f"📁 找到数据文件: {path}")
                    try:
                        self.df = pd.read_excel(path)
                        smart_data_logger.info(f"📊 成功读取Excel文件，数据形状: {self.df.shape}")

                        # 标准化列名
                        if len(self.df.columns) >= 4:
                            original_columns = list(self.df.columns)
                            self.df.columns = ['date', 'max_price', 'min_price', 'variety']
                            smart_data_logger.info(f"🏷️ 列名标准化: {original_columns} -> {list(self.df.columns)}")
                        else:
                            smart_data_logger.warning(f"⚠️ 数据文件列数不足: {len(self.df.columns)}，需要至少4列")
                            continue

                        # 转换日期格式
                        try:
                            self.df['date'] = pd.to_datetime(self.df['date'])
                            smart_data_logger.info(f"📅 日期格式转换成功，日期范围: {self.df['date'].min()} 到 {self.df['date'].max()}")
                        except Exception as date_error:
                            smart_data_logger.error(f"⚠️ 日期格式转换失败: {date_error}，使用示例数据")
                            self.create_sample_data()
                            return

                        # 数据验证
                        varieties = self.df['variety'].nunique()
                        records = len(self.df)
                        smart_data_logger.info(f"✅ 成功加载数据文件: {path}")
                        smart_data_logger.info(f"📊 数据概览: {records} 条记录, {varieties} 种蔬菜")
                        smart_data_logger.info(f"🥬 蔬菜品种: {list(self.df['variety'].unique())}")

                        # 数据质量检查
                        null_count = self.df.isnull().sum().sum()
                        if null_count > 0:
                            smart_data_logger.warning(f"⚠️ 发现 {null_count} 个空值")

                        return
                    except Exception as e:
                        smart_data_logger.error(f"⚠️ 加载文件 {path} 失败: {e}")
                        continue
                else:
                    smart_data_logger.debug(f"📂 文件不存在: {path}")

            # 如果没有找到数据文件，创建示例数据
            smart_data_logger.warning("⚠️ 未找到有效数据文件，创建示例数据")
            self.create_sample_data()

        except Exception as e:
            smart_data_logger.error(f"❌ 数据加载失败: {e}")
            self.create_sample_data()

    def create_sample_data(self):
        """创建示例数据"""
        import numpy as np
        
        # 创建示例蔬菜价格数据
        varieties = ['白菜', '萝卜', '土豆', '西红柿', '黄瓜', '茄子', '豆角', '韭菜']
        dates = pd.date_range('2024-01-01', '2024-12-31', freq='D')
        
        data = []
        for variety in varieties:
            base_price = np.random.uniform(2, 8)  # 基础价格
            for date in dates:
                # 添加季节性和随机波动
                seasonal_factor = 1 + 0.3 * np.sin(2 * np.pi * date.dayofyear / 365)
                random_factor = np.random.uniform(0.8, 1.2)
                
                max_price = base_price * seasonal_factor * random_factor
                min_price = max_price * np.random.uniform(0.7, 0.9)
                
                data.append({
                    'date': date,
                    'max_price': round(max_price, 2),
                    'min_price': round(min_price, 2),
                    'variety': variety
                })
        
        self.df = pd.DataFrame(data)
        print("✅ 创建示例数据完成")

    def extract_relevant_data(self, user_question, max_rows=1000):
        """根据用户问题提取相关数据"""
        smart_data_logger.info(f"🔍 开始提取相关数据，最大行数: {max_rows}")

        if self.df is None:
            smart_data_logger.error("❌ 数据框为空，无法提取数据")
            return pd.DataFrame()

        question_lower = user_question.lower()
        smart_data_logger.debug(f"🔤 问题关键词: {question_lower}")

        # 提取特定菜品
        mentioned_varieties = []
        for variety in self.df['variety'].unique():
            if variety.lower() in question_lower:
                mentioned_varieties.append(variety)

        if mentioned_varieties:
            smart_data_logger.info(f"🥬 识别到特定蔬菜品种: {mentioned_varieties}")
        else:
            smart_data_logger.info("🔍 未识别到特定蔬菜品种，将使用全部数据")

        # 根据问题类型筛选数据
        filtered_df = self.df.copy()
        original_count = len(filtered_df)

        # 如果提到了特定菜品，优先显示这些菜品的数据
        if mentioned_varieties:
            filtered_df = filtered_df[filtered_df['variety'].isin(mentioned_varieties)]
            smart_data_logger.info(f"📊 按品种筛选后数据量: {len(filtered_df)} 条 (原始: {original_count} 条)")

        # 根据问题类型进行不同的采样策略
        if '趋势' in question_lower or '变化' in question_lower:
            # 对于趋势分析，按时间排序并采样
            filtered_df = filtered_df.sort_values('date')
            if len(filtered_df) > max_rows:
                # 均匀采样以保持时间分布
                step = len(filtered_df) // max_rows
                filtered_df = filtered_df.iloc[::step]

        elif '排名' in question_lower or '最贵' in question_lower or '最便宜' in question_lower:
            # 对于排名分析，按价格排序
            if '最贵' in question_lower:
                filtered_df = filtered_df.nlargest(max_rows, 'max_price')
            elif '最便宜' in question_lower:
                filtered_df = filtered_df.nsmallest(max_rows, 'max_price')
            else:
                # 显示价格范围的代表性样本
                high_price = filtered_df.nlargest(max_rows//2, 'max_price')
                low_price = filtered_df.nsmallest(max_rows//2, 'max_price')
                filtered_df = pd.concat([high_price, low_price])

        elif '对比' in question_lower or '比较' in question_lower:
            # 对于对比分析，确保每个类别都有代表性数据
            if not mentioned_varieties:
                # 如果没有指定具体对象，选择主要的菜品
                top_varieties = self.df['variety'].value_counts().head(5).index
                filtered_df = filtered_df[filtered_df['variety'].isin(top_varieties)]

            # 按类别分组采样
            if len(filtered_df) > max_rows:
                if mentioned_varieties or 'variety' in question_lower:
                    filtered_df = filtered_df.groupby('variety').apply(
                        lambda x: x.sample(min(len(x), max_rows//len(filtered_df['variety'].unique())))
                    ).reset_index(drop=True)

        else:
            # 默认情况：随机采样，但保持数据的代表性
            if len(filtered_df) > max_rows:
                # 分层采样：确保每个菜品都有代表
                sampled_dfs = []
                for variety in filtered_df['variety'].unique()[:10]:  # 限制菜品数量
                    variety_data = filtered_df[filtered_df['variety'] == variety]
                    sample_size = min(len(variety_data), max_rows // 10)
                    sampled_dfs.append(variety_data.sample(sample_size))
                filtered_df = pd.concat(sampled_dfs).drop_duplicates()

        # 最终确保不超过最大行数
        if len(filtered_df) > max_rows:
            filtered_df = filtered_df.sample(max_rows)

        return filtered_df

    def format_data_for_llm(self, data_df):
        """将数据格式化为适合LLM处理的文本格式"""
        if len(data_df) == 0:
            return "没有找到相关数据。"

        # 基本统计信息
        summary = f"""
数据概览：
- 数据条数：{len(data_df)}
- 时间范围：{data_df['date'].min().strftime('%Y-%m-%d')} 到 {data_df['date'].max().strftime('%Y-%m-%d')}
- 涉及菜品：{', '.join(data_df['variety'].unique()[:10])}{'...' if len(data_df['variety'].unique()) > 10 else ''}

价格统计：
- 最高价格：{data_df['max_price'].max():.2f}元
- 最低价格：{data_df['min_price'].min():.2f}元
- 平均最高价：{data_df['max_price'].mean():.2f}元
- 平均最低价：{data_df['min_price'].mean():.2f}元

"""

        # 详细数据样本（前50行）
        sample_data = data_df.head(50)
        data_text = "\n详细数据样本：\n"
        data_text += "日期,菜品,最高价,最低价\n"

        for _, row in sample_data.iterrows():
            data_text += f"{row['date'].strftime('%Y-%m-%d')},{row['variety']},{row['max_price']:.2f},{row['min_price']:.2f}\n"

        if len(data_df) > 50:
            data_text += f"\n... 还有 {len(data_df) - 50} 行数据\n"

        # 关键统计信息
        stats_text = "\n关键统计信息：\n"

        # 按菜品统计
        variety_stats = data_df.groupby('variety').agg({
            'max_price': ['mean', 'max', 'min'],
            'min_price': ['mean', 'max', 'min']
        }).round(2)

        stats_text += "\n各菜品价格统计（平均最高价，最高价，最低价）：\n"
        for variety in variety_stats.index[:10]:  # 只显示前10个
            max_avg = variety_stats.loc[variety, ('max_price', 'mean')]
            max_high = variety_stats.loc[variety, ('max_price', 'max')]
            max_low = variety_stats.loc[variety, ('max_price', 'min')]
            stats_text += f"{variety}: 平均{max_avg:.2f}元, 最高{max_high:.2f}元, 最低{max_low:.2f}元\n"

        return summary + data_text + stats_text

    def call_deepseek_api(self, user_question, relevant_data):
        """调用DeepSeek API进行智能分析"""
        api_start_time = datetime.now()
        smart_data_logger.info("🤖 开始调用DeepSeek API...")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        prompt = f"""
你是一个专业的数据分析师。用户提供了一个关于菜品价格变动的数据表：
以下是根据用户问题筛选的相关数据：
{relevant_data}

用户问题：{user_question}

请基于这些实际数据进行深入分析，直接回答用户的问题：

要求：
- 请用markdown格式回答，包含清晰的标题和结构，要求排版美观，字体不要太大，不要加emoji！
- 在分析中引用具体的数据点和数值！
- 如果数据中有异常值或特殊情况，请特别指出！

总字数不要超过400字！总字数不要超过400字！总字数不要超过400字！
"""

        data = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "stream": False
        }

        smart_data_logger.debug(f"📝 API请求数据大小: {len(json.dumps(data))} 字符")
        smart_data_logger.debug(f"🔑 使用API密钥: {self.api_key[:10]}...")

        try:
            smart_data_logger.info(f"📡 发送API请求到: {self.api_url}")
            response = requests.post(self.api_url, headers=headers, json=data, timeout=30)

            api_time = (datetime.now() - api_start_time).total_seconds()
            smart_data_logger.info(f"⏱️ API响应时间: {api_time:.2f} 秒")

            response.raise_for_status()
            result = response.json()

            # 检查响应结构
            if 'choices' not in result or not result['choices']:
                smart_data_logger.error("❌ API响应格式异常: 缺少choices字段")
                return "API响应格式异常"

            content = result['choices'][0]['message']['content']
            smart_data_logger.info(f"✅ API调用成功，返回内容长度: {len(content)} 字符")
            smart_data_logger.debug(f"📄 API返回内容预览: {content[:100]}...")

            return content

        except requests.exceptions.Timeout:
            smart_data_logger.error("⏰ API调用超时 (30秒)")
            return "API调用超时，请稍后重试"
        except requests.exceptions.RequestException as e:
            smart_data_logger.error(f"🌐 API网络请求失败: {e}")
            return f"网络请求失败: {str(e)}"
        except Exception as e:
            smart_data_logger.error(f"❌ API调用异常: {e}")
            return f"API调用失败: {str(e)}"

    def generate_data_summary(self):
        """生成数据摘要"""
        if self.df is None:
            return "数据未加载"
            
        summary = f"""
数据集基本信息：
- 总记录数：{len(self.df)}
- 时间范围：{self.df['date'].min().strftime('%Y-%m-%d')} 到 {self.df['date'].max().strftime('%Y-%m-%d')}
- 菜品种类：{self.df['variety'].nunique()}种菜品

主要菜品：{', '.join(self.df['variety'].value_counts().head(10).index.tolist())}

价格范围：
- 最高价格：{self.df['max_price'].max():.2f}元
- 最低价格：{self.df['min_price'].min():.2f}元
- 平均最高价：{self.df['max_price'].mean():.2f}元
- 平均最低价：{self.df['min_price'].mean():.2f}元
"""
        return summary

    def analyze_question(self, user_question):
        """分析用户问题并生成回答"""
        analysis_start_time = datetime.now()
        smart_data_logger.info(f"🔍 开始智能问数分析")
        smart_data_logger.info(f"❓ 用户问题: {user_question}")

        try:
            # 安全校验
            smart_data_logger.info("🔒 开始安全内容校验...")
            safety_check = sensitive_filter.check_sensitive_content(user_question)
            if not safety_check['is_safe']:
                smart_data_logger.warning(f"⚠️ 安全校验失败: {safety_check['message']}")
                return {
                    'success': False,
                    'error': safety_check['message'],
                    'analysis_report': safety_check['message'],
                    'charts': [],
                    'data_summary': ''
                }
            smart_data_logger.info("✅ 安全校验通过")

            # 根据用户问题提取相关数据
            smart_data_logger.info("📊 开始提取相关数据...")
            relevant_data_df = self.extract_relevant_data(user_question)
            smart_data_logger.info(f"📈 提取到相关数据: {len(relevant_data_df)} 条记录")

            # 将数据格式化为LLM可理解的文本
            smart_data_logger.info("🔄 格式化数据为LLM输入...")
            formatted_data = self.format_data_for_llm(relevant_data_df)
            smart_data_logger.debug(f"📝 格式化数据长度: {len(formatted_data)} 字符")

            # 调用DeepSeek API
            smart_data_logger.info("🤖 调用DeepSeek API进行智能分析...")
            analysis_report = self.call_deepseek_api(user_question, formatted_data)
            smart_data_logger.info(f"📋 分析报告生成完成，长度: {len(analysis_report)} 字符")

            # 生成数据摘要
            smart_data_logger.info("📊 生成数据摘要...")
            data_summary = self.generate_data_summary()

            # 基于用户问题和相关数据智能生成图表
            smart_data_logger.info("📈 开始生成智能图表...")
            charts = self.create_intelligent_chart(user_question, relevant_data_df)
            smart_data_logger.info(f"📊 图表生成完成，共 {len(charts)} 个图表")

            # 计算处理时间
            processing_time = (datetime.now() - analysis_start_time).total_seconds()
            smart_data_logger.info(f"⏱️ 分析完成，总耗时: {processing_time:.2f} 秒")

            return {
                'success': True,
                'analysis_report': analysis_report,
                'charts': charts,
                'data_summary': data_summary
            }

        except Exception as e:
            processing_time = (datetime.now() - analysis_start_time).total_seconds()
            smart_data_logger.error(f"❌ 智能问数分析失败: {e}")
            smart_data_logger.error(f"⏱️ 失败前耗时: {processing_time:.2f} 秒")
            return {
                'success': False,
                'error': f'分析失败: {str(e)}',
                'analysis_report': f'分析失败: {str(e)}',
                'charts': [],
                'data_summary': ''
            }

    def create_intelligent_chart(self, user_question, relevant_data_df):
        """基于用户问题和相关数据智能生成图表"""
        smart_data_logger.info("📈 开始智能图表生成...")
        charts = []

        if len(relevant_data_df) == 0:
            smart_data_logger.warning("⚠️ 相关数据为空，无法生成图表")
            return charts

        question_lower = user_question.lower()
        varieties_count = len(relevant_data_df['variety'].unique())
        data_count = len(relevant_data_df)

        smart_data_logger.info(f"📊 数据概况: {data_count} 条记录, {varieties_count} 种蔬菜")
        smart_data_logger.debug(f"🔍 问题关键词分析: {question_lower}")

        try:
            # 1. 趋势分析图表
            trend_keywords = ['趋势', '变化', '走势', '涨', '跌', '时间']
            if any(keyword in question_lower for keyword in trend_keywords):
                smart_data_logger.info("📈 检测到趋势分析需求，生成趋势图表...")
                chart = self.create_trend_chart(relevant_data_df, user_question)
                if chart:
                    charts.append(chart)
                    smart_data_logger.info("✅ 趋势图表生成成功")

            # 2. 排名对比图表
            ranking_keywords = ['排名', '最贵', '最便宜', '对比', '比较', '哪个']
            if any(keyword in question_lower for keyword in ranking_keywords):
                smart_data_logger.info("📊 检测到排名对比需求，生成排名图表...")
                chart = self.create_ranking_chart(relevant_data_df, user_question)
                if chart:
                    charts.append(chart)
                    smart_data_logger.info("✅ 排名图表生成成功")

            # 3. 分布分析图表
            distribution_keywords = ['分布', '范围', '差异', '波动']
            if any(keyword in question_lower for keyword in distribution_keywords):
                smart_data_logger.info("🍩 检测到分布分析需求，生成分布图表...")
                chart = self.create_distribution_chart(relevant_data_df, user_question)
                if chart:
                    charts.append(chart)
                    smart_data_logger.info("✅ 分布图表生成成功")

            # 4. 如果没有匹配到特定类型，生成默认的综合图表
            if not charts:
                smart_data_logger.info("🔄 未匹配到特定图表类型，生成默认图表...")
                # 根据数据特点选择合适的图表类型
                if varieties_count > 1:
                    smart_data_logger.info(f"📊 多品种数据 ({varieties_count} 种)，生成对比图表")
                    chart = self.create_ranking_chart(relevant_data_df, user_question)
                    if chart:
                        charts.append(chart)
                        smart_data_logger.info("✅ 默认对比图表生成成功")
                else:
                    smart_data_logger.info("📈 单品种数据，生成趋势图表")
                    chart = self.create_trend_chart(relevant_data_df, user_question)
                    if chart:
                        charts.append(chart)
                        smart_data_logger.info("✅ 默认趋势图表生成成功")

        except Exception as e:
            smart_data_logger.error(f"❌ 图表生成失败: {e}")

        smart_data_logger.info(f"📊 图表生成完成，共生成 {len(charts)} 个图表")
        return charts

    def create_trend_chart(self, data_df, user_question):
        """创建趋势图表"""
        try:
            # 按日期分组，计算平均价格
            trend_data = data_df.groupby(['date', 'variety']).agg({
                'max_price': 'mean',
                'min_price': 'mean'
            }).reset_index()

            # 限制显示的品种数量
            top_varieties = data_df['variety'].value_counts().head(5).index.tolist()
            trend_data = trend_data[trend_data['variety'].isin(top_varieties)]

            # 准备图表数据
            chart_data = {
                'labels': sorted(trend_data['date'].dt.strftime('%Y-%m-%d').unique()),
                'datasets': []
            }

            colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']

            for i, variety in enumerate(top_varieties):
                variety_data = trend_data[trend_data['variety'] == variety].sort_values('date')

                if len(variety_data) > 0:
                    chart_data['datasets'].append({
                        'label': f'{variety}最高价',
                        'data': variety_data['max_price'].tolist(),
                        'borderColor': colors[i % len(colors)],
                        'backgroundColor': colors[i % len(colors)] + '20',
                        'fill': False,
                        'tension': 0.1
                    })

            return {
                'type': 'line',
                'title': '蔬菜价格趋势分析',
                'data': chart_data,
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': '蔬菜价格趋势分析'
                        },
                        'legend': {
                            'display': True,
                            'position': 'top'
                        }
                    },
                    'scales': {
                        'x': {
                            'display': True,
                            'title': {
                                'display': True,
                                'text': '日期'
                            }
                        },
                        'y': {
                            'display': True,
                            'title': {
                                'display': True,
                                'text': '价格(元)'
                            }
                        }
                    }
                }
            }

        except Exception as e:
            print(f"❌ 趋势图表创建失败: {e}")
            return None

    def create_ranking_chart(self, data_df, user_question):
        """创建排名对比图表"""
        try:
            # 按品种分组，计算平均价格
            ranking_data = data_df.groupby('variety').agg({
                'max_price': 'mean',
                'min_price': 'mean'
            }).reset_index()

            # 按平均最高价排序
            ranking_data = ranking_data.sort_values('max_price', ascending=False).head(10)

            chart_data = {
                'labels': ranking_data['variety'].tolist(),
                'datasets': [
                    {
                        'label': '平均最高价',
                        'data': ranking_data['max_price'].round(2).tolist(),
                        'backgroundColor': '#FF6384',
                        'borderColor': '#FF6384',
                        'borderWidth': 1
                    },
                    {
                        'label': '平均最低价',
                        'data': ranking_data['min_price'].round(2).tolist(),
                        'backgroundColor': '#36A2EB',
                        'borderColor': '#36A2EB',
                        'borderWidth': 1
                    }
                ]
            }

            return {
                'type': 'bar',
                'title': '蔬菜价格排名对比',
                'data': chart_data,
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': '蔬菜价格排名对比'
                        },
                        'legend': {
                            'display': True,
                            'position': 'top'
                        }
                    },
                    'scales': {
                        'x': {
                            'display': True,
                            'title': {
                                'display': True,
                                'text': '蔬菜品种'
                            }
                        },
                        'y': {
                            'display': True,
                            'title': {
                                'display': True,
                                'text': '价格(元)'
                            }
                        }
                    }
                }
            }

        except Exception as e:
            print(f"❌ 排名图表创建失败: {e}")
            return None

    def create_distribution_chart(self, data_df, user_question):
        """创建分布分析图表"""
        try:
            # 计算价格分布
            price_ranges = ['0-2元', '2-4元', '4-6元', '6-8元', '8-10元', '10元以上']
            distribution_data = []

            for i, price_range in enumerate(price_ranges):
                if i == 0:
                    count = len(data_df[data_df['max_price'] < 2])
                elif i == len(price_ranges) - 1:
                    count = len(data_df[data_df['max_price'] >= 10])
                else:
                    lower = i * 2
                    upper = (i + 1) * 2
                    count = len(data_df[(data_df['max_price'] >= lower) & (data_df['max_price'] < upper)])

                distribution_data.append(count)

            chart_data = {
                'labels': price_ranges,
                'datasets': [{
                    'label': '记录数量',
                    'data': distribution_data,
                    'backgroundColor': [
                        '#FF6384', '#36A2EB', '#FFCE56',
                        '#4BC0C0', '#9966FF', '#FF9F40'
                    ],
                    'borderWidth': 1
                }]
            }

            return {
                'type': 'doughnut',
                'title': '蔬菜价格分布',
                'data': chart_data,
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': '蔬菜价格分布'
                        },
                        'legend': {
                            'display': True,
                            'position': 'right'
                        }
                    }
                }
            }

        except Exception as e:
            print(f"❌ 分布图表创建失败: {e}")
            return None
