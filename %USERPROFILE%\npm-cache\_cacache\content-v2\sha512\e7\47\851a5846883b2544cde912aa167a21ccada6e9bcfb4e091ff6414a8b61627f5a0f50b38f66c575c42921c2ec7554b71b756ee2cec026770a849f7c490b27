{"_id": "opener", "_rev": "67-c528eb41335559382d895981d5c27a06", "name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "dist-tags": {"latest": "1.5.2"}, "versions": {"1.0.0": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}, "license": "WTFPL", "repository": {"type": "git", "url": "git://github.com/domenic/opener.git"}, "bugs": {"url": "http://github.com/domenic/opener/issues"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "scripts": {"lint": "jshint opener.js --show-non-errors"}, "devDependencies": {"jshint": ">= 0.7.3"}, "_id": "opener@1.0.0", "dist": {"shasum": "355bcdf938f918b2723d2fd2293367976c0798c8", "tarball": "https://registry.npmjs.org/opener/-/opener-1.0.0.tgz", "integrity": "sha512-IGM6TivaF8eRMMssdq/2LTdplHOHFyXCKuEruvsR6N0PwkSzL5m3q/E6lQvNXXIdCBAHm1jMY6rmJ+gbrEGoZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9eqgR9IT+S9n9x6ctQ3jooXgsKGv6FdTMgqOUHNtscgIhAIxjmsYLY005ajbC+C0gGe3bBzCmzUTQnOxdRueu507b"}]}, "maintainers": [{"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.0.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}, "license": "WTFPL", "repository": {"type": "git", "url": "git://github.com/domenic/opener.git"}, "bugs": {"url": "http://github.com/domenic/opener/issues"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "scripts": {"lint": "jshint opener.js --show-non-errors"}, "devDependencies": {"jshint": ">= 0.7.3"}, "_id": "opener@1.0.1", "dist": {"shasum": "7d47b0c11ac1f4a5bc707f2a41d6784540c3fe98", "tarball": "https://registry.npmjs.org/opener/-/opener-1.0.1.tgz", "integrity": "sha512-BdqZiEgnIeGm6fCLxcLLH9cWFvyheBxWD9N0gV8y7elJv9IbnRqk+SpTFmi6fg+zQN2mh7KqdV0Yg76yDfCcZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICDSZYF+83sGig1s7BU18fMm5eAMgJ2MwypdBIq4j4rLAiEAmfPQ1QHTh+d+IMEIWjumWBko74koM8AdqZpoB5BsJOk="}]}, "maintainers": [{"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.1.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}, "license": "WTFPL", "repository": {"type": "git", "url": "git://github.com/domenic/opener.git"}, "bugs": {"url": "http://github.com/domenic/opener/issues"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "scripts": {"lint": "jshint opener.js --show-non-errors"}, "devDependencies": {"jshint": ">= 0.7.3"}, "_id": "opener@1.1.0", "dist": {"shasum": "824dd356148d853e8adac34945c4b36a0b028bdd", "tarball": "https://registry.npmjs.org/opener/-/opener-1.1.0.tgz", "integrity": "sha512-ROlldSjamBmzqJ6Kzhmg+yMnxFC2RAe4JoWCGK/Z9//FTG0VO6Ip1UGOBrd2RaVmghE4gDbiCoPWjeNEBVZj6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEipiBnux1pwgTyfrKuWON6ryIzQ5E75fXyI966P3TYlAiEAjFYI3t9pXT2N6ptOUxPVJU06bBTIs6cOlWndbVzXH58="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.2.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}, "license": "WTFPL", "repository": {"type": "git", "url": "git://github.com/domenic/opener.git"}, "bugs": {"url": "http://github.com/domenic/opener/issues"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "scripts": {"lint": "jshint opener.js --show-non-errors"}, "devDependencies": {"jshint": ">= 0.7.3"}, "_id": "opener@1.2.0", "dist": {"shasum": "4fabd0903b3c92226349cce816155a7cf9c1ff5c", "tarball": "https://registry.npmjs.org/opener/-/opener-1.2.0.tgz", "integrity": "sha512-SI3zCsXYE0uQ7hSP44uohsNMksdPRtPPoI/0RiH84tGDjGbcJRgNFVI35dWCkcFprp1teMyfUDQSHYMLfkTLbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnUKck8wce4BiZdKMAxgpuLzoKW5li7bOtxVZzCHhVFAIhAN1vHkMNPiAPGxsqGMUmi73iaKv+cdtbok6NkR3kW72j"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.3.0": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.3.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}, "license": "WTFPL", "repository": {"type": "git", "url": "git://github.com/domenic/opener.git"}, "bugs": {"url": "http://github.com/domenic/opener/issues"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "scripts": {"lint": "jshint opener.js"}, "devDependencies": {"jshint": ">= 0.9.0"}, "_id": "opener@1.3.0", "dist": {"shasum": "130ba662213fa842edb4cd0361d31a15301a43e2", "tarball": "https://registry.npmjs.org/opener/-/opener-1.3.0.tgz", "integrity": "sha512-sBqelaOnn8SA+zefIj/VMXgIACr6URhyysMn2DEx2fTcbmyDbPf8wWs5rPgoR2SVRBk1oNYl8YE7LwV84FCJCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAFLocyqO3b0IuQ5sdd/vmK9mBz2SspHubfuTGyjHBDXAiEAwwIbQZdDvPl4+CFNtGiOPYvCNvHWQomO+smj/RN/OuM="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dome<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.4.0": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.4.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenic.me/"}, "license": "WTFPL", "repository": {"type": "git", "url": "git://github.com/domenic/opener.git"}, "bugs": {"url": "http://github.com/domenic/opener/issues"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "scripts": {"lint": "jshint opener.js"}, "devDependencies": {"jshint": "^2.5.4"}, "gitHead": "b9d36d4f82c26560acdadbabbb10ddba46a30dc5", "homepage": "https://github.com/domenic/opener", "_id": "opener@1.4.0", "_shasum": "d11f86eeeb076883735c9d509f538fe82d10b941", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "d11f86eeeb076883735c9d509f538fe82d10b941", "tarball": "https://registry.npmjs.org/opener/-/opener-1.4.0.tgz", "integrity": "sha512-/fnSWlsKW3VxYs+N44uxUrjhD+pOMo+tptVK+Y0BUky6ECI6suCAKKunNGBgo0HAPY2dLO4DEf7YQuIid8izwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyoQPeqWwa9Hg8JYjSjavNofW5f1agYrSBLUjvDSjxtAIhAIGzHZsIuerhQVmXUKGfLxEtLVqEySekSMN6nWxlrnK0"}]}, "directories": {}}, "1.4.1": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.4.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "WTFPL", "repository": {"type": "git", "url": "https://github.com/domenic/opener"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "files": ["opener.js"], "scripts": {"lint": "jshint opener.js"}, "devDependencies": {"jshint": "^2.6.3"}, "gitHead": "d0ee95b19951703462fa593baa16e81fdff7827c", "bugs": {"url": "https://github.com/domenic/opener/issues"}, "homepage": "https://github.com/domenic/opener", "_id": "opener@1.4.1", "_shasum": "897590acd1aed3311b703b58bccb4d43f56f2895", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "1.5.1", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "897590acd1aed3311b703b58bccb4d43f56f2895", "tarball": "https://registry.npmjs.org/opener/-/opener-1.4.1.tgz", "integrity": "sha512-W6VXj038yaK7zMgSPJkzo3J4E6WhHpapSzbA/KkhTfLk7n9OKJmdXmcYNXrAVi+ZBnzfhOh8xF0Rh0Up/YzXbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBzGIcp2mQoKhXVlb23NwVd4R51qMOHzProumnzsV0TSAiAuJipWLxpJ24qlxeQg5hweTEwfCrfdBpXeWBJu6f5bfA=="}]}, "directories": {}}, "1.4.2": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.4.2", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "(WTFPL OR MIT)", "repository": {"type": "git", "url": "git+https://github.com/domenic/opener.git"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "files": ["opener.js"], "scripts": {"lint": "jshint opener.js"}, "devDependencies": {"jshint": "^2.6.3"}, "gitHead": "ef28bf7c20de6cea3ff0e9bf0294a78c237e716d", "bugs": {"url": "https://github.com/domenic/opener/issues"}, "homepage": "https://github.com/domenic/opener#readme", "_id": "opener@1.4.2", "_shasum": "b32582080042af8680c389a499175b4c54fff523", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"shasum": "b32582080042af8680c389a499175b4c54fff523", "tarball": "https://registry.npmjs.org/opener/-/opener-1.4.2.tgz", "integrity": "sha512-v3eJ0jIjGRZNHL/qdXAACJyfQ2w9IBfpNvGkev0p5ip59BUUu8Sd2qS6hKckJ3EgQLd4JiqyVD53feC5rCmnbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2Sc3sC9idZAmIRZYgqTweJUsbvBZi07pZK72V2odaRQIhAKzZ1XoV7Jk1VbvrHuZ8k/55JOkymzeZidH7iHXxcv6I"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/opener-1.4.2.tgz_1473551216353_0.850118016358465"}, "directories": {}}, "1.4.3": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.4.3", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "(WTFPL OR MIT)", "repository": {"type": "git", "url": "git+https://github.com/domenic/opener.git"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "files": ["opener.js"], "scripts": {"lint": "jshint opener.js"}, "devDependencies": {"jshint": "^2.6.3"}, "gitHead": "51a4058b9e5172d9b57fb5e5205bc3f7fb4ace6a", "bugs": {"url": "https://github.com/domenic/opener/issues"}, "homepage": "https://github.com/domenic/opener#readme", "_id": "opener@1.4.3", "_shasum": "5c6da2c5d7e5831e8ffa3964950f8d6674ac90b8", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.2.1", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"shasum": "5c6da2c5d7e5831e8ffa3964950f8d6674ac90b8", "tarball": "https://registry.npmjs.org/opener/-/opener-1.4.3.tgz", "integrity": "sha512-4Im9TrPJcjAYyGR5gBe3yZnBzw5n3Bfh1ceHHGNOpMurINKc6RdSIPXMyon4BZacJbJc36lLkhipioGbWh5pwg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDskOf0hyJWMFMyKKkNg0zxZRU6N52X8uGcAgyef7VYXAiAjDc7G52R8HMqPoUN02D/dJA71bnpv/2Z3v4HUliluAQ=="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/opener-1.4.3.tgz_1487118807602_0.517438261769712"}, "directories": {}}, "1.5.0": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.5.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "(WTFPL OR MIT)", "repository": {"type": "git", "url": "git+https://github.com/domenic/opener.git"}, "main": "lib/opener.js", "bin": {"opener": "bin/opener-bin.js"}, "files": ["lib/", "bin/"], "scripts": {"lint": "eslint ."}, "devDependencies": {"eslint": "^5.3.0"}, "gitHead": "b9ac3d0d0c6f066e9429889cd54106b8f50785da", "bugs": {"url": "https://github.com/domenic/opener/issues"}, "homepage": "https://github.com/domenic/opener#readme", "_id": "opener@1.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MD4s/o61y2slS27zm2s4229V2gAUHX0/e3/XOmY/jsXwhysjjCIHN8lx7gqZCrZk19ym+HjCUWHeMKD7YJtKCQ==", "shasum": "24222fb4ad423ba21f5bf38855cebe44220f6531", "tarball": "https://registry.npmjs.org/opener/-/opener-1.5.0.tgz", "fileCount": 5, "unpackedSize": 6194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcMnHCRA9TVsSAnZWagAAOMcP+QFqN/hIp1/J9K/oIDzv\n2eVmWCX6xGKuMjbZbK6Ca06hPXX2c254erhgeJdPRC55UqnT75w+9IONOn9q\nY/sUDrT2/8eoL/Q2G3CTQNfQE0p1eq2e4x6k8QpA4txjqKGXsGBTjesuQ0Bd\nRpZAIRtOigh5twGXFmCF2iYLOQ2x3Uh7jpVQPqmbspLJIbGfpGmuTbvjOXlV\naPIarlvrgydXJBZgO3aL/8WTT+9nUhysIca6c8rhhLuRLfOXx/wfDnlHWjIq\nbGv9nMx9mtqIgH5JNYo+QDI3x8bL8/7HP3rkoNzGfnvEYTkca10C3NJx7QU/\nR5uatdQPzLkBVpoBQ9HrRTJoTFQuyUij7ruD2vrIQaRQbdbD0sNL2iiJ71yb\nNVaNEKEd5boWmQ8dlxOCKHFYwtp5IQ9nYmWg4UsiV5CJR4fVDbU+z/AHJPWn\nny0sZ1Wwz/j+uXZ3epr+UcEbRrkhJYQk27FQncLInbOSDqBmo+BwisI895+J\npWHKhmSzXUhHyaBEq46P2uVO9Tjodh232rbc81tsHfVYvk0ScC4/J2l3A7hM\n+sYHLcnzhFIT5aaynWAZnYwCW2NjI9xsaOpxqu6COaCYz48aiMORfDHotM1f\nwDcll+MdIBQBGnD61p/F5DUWg14JNpc0+Pt/wibukOdX2M4vamxLTRFzX4E9\nMMEU\r\n=ne1o\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkuv3lwHjVZr0REiYs+tAIqSPaKAM1kbC+E4hIJWrEYwIhAP8iIkUUzzN7Ol/gPZwkYPMYKerjk2jGXl71f1S/lBIU"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opener_1.5.0_1534118343196_0.47769097545898975"}, "_hasShrinkwrap": false}, "1.5.1": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.5.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "(WTFPL OR MIT)", "repository": {"type": "git", "url": "git+https://github.com/domenic/opener.git"}, "main": "lib/opener.js", "bin": {"opener": "bin/opener-bin.js"}, "files": ["lib/", "bin/"], "scripts": {"lint": "eslint ."}, "devDependencies": {"eslint": "^5.3.0"}, "gitHead": "3deb561d900a268173b79eec0126af30e16d4b4b", "bugs": {"url": "https://github.com/domenic/opener/issues"}, "homepage": "https://github.com/domenic/opener#readme", "_id": "opener@1.5.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-goYSy5c2UXE4Ra1xixabeVh1guIX/ZV/YokJksb6q2lubWu6UbvPQ20p542/sFIll1nl8JnCyK9oBaOcCWXwvA==", "shasum": "6d2f0e77f1a0af0032aca716c2c1fbb8e7e8abed", "tarball": "https://registry.npmjs.org/opener/-/opener-1.5.1.tgz", "fileCount": 5, "unpackedSize": 6198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhMD6CRA9TVsSAnZWagAAlmgQAIzenBHoSpdrU/50aoHj\nDLk8Mrn2hO89/YKl31vNpzCVu6qBpP7WUbHM149TIt/UkDYhKsnjQUUZT+9M\nzOKnOb0EEu3QNl0P1DGUlX5vWJ61j2qbPAPplcQcWJIryjFbWD9CRkH2P89J\nMBqqH4I1nVBEWB7TJOS7kLAPmOvUCD/1U83008AVASCZ8ijeEyV73xTmPpBu\nJsZbOpBbgQCEeXAYf5mpCxcYoGOet8Ip1FFTSXFWfGaakvqpFvMiVKvPoRFK\nKmuc/mxDkqlohu0AxV+vKypI3zU52dh0g+Z8zqYYVudyayE4Cn+5DjqKwMIc\nRYWW2sX7T/GDOerON/G5nbtJhX7nvcn5EFN/Oq1uMd9Y+U7oZ1kkNviVOtDR\nIz192XhqFOU9igyB9TTsE6oNCr8VyG/2dCV1RSUw0ltetveFg3K4wb7D0u9d\nG5BnOqoVIbWVgryixVhA4lmLQRVz1Hj793fDcjW8GAfXSXMT7x/H0VO2bo3C\nPm8XcGOM0IQnMhA9kIKdtu8jV+8tJ8oySJwqoBekf204/vr4JUId+pPveK8T\n2NfBWw+JzdC7nIL9P2knvuBWlNylwke43FlEAz5DDPdoJimA8fY0I8bwtPHZ\nK9bvspQkkGI2ZRyOf0uuMOTc45oAer4oJV9IeNCY52bnhkOm/Yv7vOBpaHax\np2Rh\r\n=5aKy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICF9izDHtjJJX9O/syl0Aa92iOuyb51AzL16zlXo0mSGAiEAtu+caTnpjOWC1P9FhEz+AKsvjZa4Qu+r3D/EQc0WfI8="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opener_1.5.1_1535426810163_0.09801673221093954"}, "_hasShrinkwrap": false}, "1.5.2": {"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.5.2", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "(WTFPL OR MIT)", "repository": {"type": "git", "url": "git+https://github.com/domenic/opener.git"}, "main": "lib/opener.js", "bin": {"opener": "bin/opener-bin.js"}, "scripts": {"lint": "eslint ."}, "devDependencies": {"eslint": "^7.7.0"}, "gitHead": "24edf48a38d1e23bbc5ffbeb079c206d5565f062", "bugs": {"url": "https://github.com/domenic/opener/issues"}, "homepage": "https://github.com/domenic/opener#readme", "_id": "opener@1.5.2", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==", "shasum": "5d37e1f35077b9dcac4301372271afdeb2a13598", "tarball": "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz", "fileCount": 5, "unpackedSize": 6213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh216aCRA9TVsSAnZWagAAI7YP+wagbQLsJ3d5swJZM4xR\nkDpFLZZkoKl5JwCHMyAee1e/aJdDh6sQ1W1iAn4dWS5WjPHyrj3LcKkaw4K3\nKHzR6hW0lQR057duxX1zAtR/fmGq/XSmXA37nQfDjlZ9kVxCd0Bv789XPyE5\n+KkHpNgW5mRBg7r2wRl+rhSNg9CW3fLNU8rCULulngZK0Ndm9DpYcQgvnzvz\nOgCZEzlxkHwI41qwSeLXviZDBkdwua3bD7CURzaKbTOI1ZMborK2NQ+dLAmO\nYeOMKweHD7ZpsUatgz7puRm8YWFM8/3VCXQ06lfu1FUKcm1mNX/txklt/wVb\nESZGmSMwHaS5d9OiHUg+t7ee+VwKY+e8cZ5mpn1BaBAxdgqBRzEEuSh8YpaW\ns5hG9xyO14BzMSqzguXd2q4vnC2OgeCtskIoSAm8IlU2MWp8UHtSnUFozhUf\n9qjcqt9fqVoJPviMPaCJEYtjSzH926FHngwjmckpjbkwzvHu7wmMRi8Fp6JO\n1t6Hwl3/KTPkBe6hvRMqP1uwfovZq+BbZzSaGcj1DGg4jYg0q1mqCu5VshJX\nrzcYpnF5bpgpD9F8ajqZmHKXT0TLfO6gWVsmr0E67cVeiKYRnM9vHnmHaPoD\nYPDi4y2fjkYNjmwFdcLTctSdPNbCX6gO7+bUGPpyU8hVXpT8IQoywgTKe5eg\ndcyU\r\n=qouJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGYz+JZcuJQpHG4v9gryk6Sn8tz6oemQjG+WudbJvNewAiBr3RxS6VLOC9CmaN9LIzb1CxDBlSAGhF5Eem6Ulf9gDw=="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opener_1.5.2_1598732713676_0.9320761526342078"}, "_hasShrinkwrap": false}}, "readme": "# It Opens Stuff\n\nThat is, in your desktop environment. This will make *actual windows pop up*, with stuff in them:\n\n```bash\nnpm install opener -g\n\nopener http://google.com\nopener ./my-file.txt\nopener firefox\nopener npm run lint\n```\n\nAlso if you want to use it programmatically you can do that too:\n\n```js\nvar opener = require(\"opener\");\n\nopener(\"http://google.com\");\nopener(\"./my-file.txt\");\nopener(\"firefox\");\nopener(\"npm run lint\");\n```\n\nPlus, it returns the child process created, so you can do things like let your script exit while the window stays open:\n\n```js\nvar editor = opener(\"documentation.odt\");\neditor.unref();\n// These other unrefs may be necessary if your OS's opener process\n// exits before the process it started is complete.\neditor.stdin.unref();\neditor.stdout.unref();\neditor.stderr.unref();\n```\n\n## Use It for Good\n\nLike opening the user's browser with a test harness in your package's test script:\n\n```json\n{\n    \"scripts\": {\n        \"test\": \"opener ./test/runner.html\"\n    },\n    \"devDependencies\": {\n        \"opener\": \"*\"\n    }\n}\n```\n\n## Why\n\nBecause Windows has `start`, Macs have `open`, and *nix has `xdg-open`. At least [according to some person on StackOverflow](http://stackoverflow.com/q/1480971/3191). And I like things that work on all three. Like Node.js. And Opener.\n", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:09:10.452Z", "created": "2012-08-06T10:58:56.840Z", "1.0.0": "2012-08-06T10:58:57.376Z", "1.0.1": "2012-08-06T11:01:23.485Z", "1.1.0": "2012-08-23T15:35:31.789Z", "1.2.0": "2012-08-24T20:22:23.649Z", "1.3.0": "2012-09-13T14:16:07.936Z", "1.4.0": "2014-08-21T04:05:32.924Z", "1.4.1": "2015-03-24T11:38:20.536Z", "1.4.2": "2016-09-10T23:46:58.033Z", "1.4.3": "2017-02-15T00:33:29.546Z", "1.5.0": "2018-08-12T23:59:03.305Z", "1.5.1": "2018-08-28T03:26:50.275Z", "1.5.2": "2020-08-29T20:25:13.795Z"}, "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "repository": {"type": "git", "url": "git+https://github.com/domenic/opener.git"}, "users": {"hughsk": true, "kriskowal": true, "fiveisprime": true, "baishuiz": true, "mysticatea": true, "jden": true, "danielpacak": true, "jclo": true, "jian263994241": true, "monsterkodi": true, "bojand": true, "wangnan0610": true, "lgomez": true, "jsdnxx": true, "scottfreecode": true, "uid-11222": true, "gggauravgandhi": true, "shanewholloway": true, "pixelcraft": true, "azertypow": true, "wuuashen": true, "soulchainer": true, "knownasilya": true, "vdsabev": true, "n3u3w3lt": true, "d-band": true, "edwardxyt": true, "ackerapple": true, "flumpus-dev": true}, "homepage": "https://github.com/domenic/opener#readme", "bugs": {"url": "https://github.com/domenic/opener/issues"}, "license": "(WTFPL OR MIT)", "readmeFilename": "README.md"}