"""
智能问策服务模块
提供基于阿里云百炼平台的智能问策功能
支持政策链接解析和政策问答
"""

from http import HTTPStatus
from dashscope import Application
import re


class SmartPolicyService:
    """智能问策服务"""

    def __init__(self):
        # 阿里云百炼配置
        self.api_key = "sk-8a9d2ac53b5448c5856c65f51872fd05"
        # 智能问策专用智能体ID
        self.app_id = "c0a9b3cbf9134a85959eb83860a5c7d5"

    def analyze_policy(self, question):
        """
        调用阿里云百炼智能问策服务
        
        Args:
            question (str): 用户问题，可能包含政策链接
            
        Returns:
            dict: 包含success、answer、error等字段的结果字典
        """
        try:
            print(f"🔍 调用智能问策服务: {question}")

            # 检查是否包含政策链接
            has_policy_link = self._check_policy_link(question)
            if has_policy_link:
                print(f"📄 检测到政策链接，进行深度分析")

            response = Application.call(
                api_key=self.api_key,
                app_id=self.app_id,
                prompt=question
            )

            if response.status_code != HTTPStatus.OK:
                print(f"❌ 阿里云智能问策API调用失败:")
                print(f"request_id={response.request_id}")
                print(f"code={response.status_code}")
                print(f"message={response.message}")

                return {
                    'success': False,
                    'error': f'智能问策服务暂时不可用 (错误码: {response.status_code})',
                    'error_details': {
                        'request_id': response.request_id,
                        'status_code': response.status_code,
                        'message': response.message
                    }
                }
            else:
                print(f"✅ 智能问策分析成功")

                # 提取文档引用信息
                doc_references = []
                try:
                    if hasattr(response.output, 'doc_references') and response.output.doc_references:
                        print(f"🔍 发现 {len(response.output.doc_references)} 个政策文档引用")
                        for i, doc_ref in enumerate(response.output.doc_references):
                            try:
                                doc_info = {}
                                # 安全地获取各个属性
                                for attr_name in ['doc_id', 'doc_name', 'text', 'title', 'url', 'bizId', 'docUuid']:
                                    try:
                                        doc_info[attr_name] = getattr(doc_ref, attr_name, '')
                                    except Exception as attr_error:
                                        print(f"⚠️ 获取属性 {attr_name} 失败: {attr_error}")
                                        doc_info[attr_name] = ''

                                doc_references.append(doc_info)
                                print(f"✅ 政策文档引用 {i+1}: {doc_info.get('doc_name', 'Unknown')} - {doc_info.get('title', 'No title')}")
                            except Exception as doc_error:
                                print(f"❌ 处理政策文档引用 {i+1} 时出错: {doc_error}")
                                continue
                    else:
                        print("📄 没有找到政策文档引用信息")
                except Exception as ref_error:
                    print(f"❌ 处理政策文档引用时出错: {ref_error}")
                    doc_references = []

                # 清理答案中的引用标记 [1], [2] 等
                clean_answer = response.output.text
                if clean_answer:
                    # 移除引用标记
                    clean_answer = re.sub(r'\[\d+\]', '', clean_answer)
                    # 清理多余的空格和换行
                    clean_answer = re.sub(r'\n\s*\n', '\n\n', clean_answer.strip())

                # 添加智能问策标识
                if has_policy_link:
                    clean_answer = f" **政策解读结果**\n\n{clean_answer}"
                else:
                    clean_answer = f" **智能问策回答**\n\n{clean_answer}"

                return {
                    'success': True,
                    'answer': clean_answer,
                    'request_id': response.request_id,
                    'doc_references': doc_references,
                    'has_policy_link': has_policy_link
                }

        except Exception as e:
            print(f"❌ 智能问策服务调用异常: {e}")
            return {
                'success': False,
                'error': f'智能问策服务异常: {str(e)}'
            }

    def _check_policy_link(self, text):
        """
        检查文本中是否包含政策链接
        
        Args:
            text (str): 要检查的文本
            
        Returns:
            bool: 是否包含政策链接
        """
        # 常见的政策网站域名模式
        policy_domains = [
            r'gov\.cn',
            r'www\.gov\.cn',
            r'[a-zA-Z0-9-]+\.gov\.cn',
            r'[a-zA-Z0-9-]+\.gov\.com',
            r'policy\.',
            r'zhengce\.',
            r'fgw\.',
            r'mof\.',
            r'miit\.',
            r'ndrc\.',
            r'mohurd\.',
            r'mee\.',
            r'moa\.',
            r'nhc\.',
            r'moe\.',
            r'mohrss\.',
            r'mps\.',
            r'moj\.',
            r'most\.',
            r'mct\.',
            r'nrta\.',
            r'safe\.',
            r'customs\.',
            r'chinatax\.',
            r'samr\.',
            r'stats\.',
            r'pbc\.',
            r'cbirc\.',
            r'csrc\.',
            r'cac\.',
            r'nea\.',
            r'forestry\.',
            r'nsa\.',
            r'ems\.',
            r'npc\.',
            r'cppcc\.',
            r'court\.',
            r'spp\.',
            r'audit\.'
        ]
        
        # URL模式
        url_patterns = [
            r'https?://[^\s]+',
            r'www\.[^\s]+',
            r'[a-zA-Z0-9-]+\.[a-zA-Z]{2,}[^\s]*'
        ]
        
        text_lower = text.lower()
        
        # 检查是否包含URL
        for pattern in url_patterns:
            if re.search(pattern, text_lower):
                # 进一步检查是否是政策相关域名
                for domain_pattern in policy_domains:
                    if re.search(domain_pattern, text_lower):
                        return True
        
        # 检查是否包含政策相关关键词
        policy_keywords = [
            '政策', '法规', '条例', '办法', '规定', '通知', '公告', '意见',
            '方案', '规划', '标准', '指导', '实施', '管理', '监督', '执行',
            '发布', '印发', '颁布', '施行', '生效', '废止', '修订', '解释'
        ]
        
        # 如果包含URL且包含政策关键词，认为是政策链接
        has_url = any(re.search(pattern, text_lower) for pattern in url_patterns)
        has_policy_keyword = any(keyword in text for keyword in policy_keywords)
        
        return has_url and has_policy_keyword

    def format_policy_response(self, answer, doc_references=None):
        """
        格式化政策问答响应
        
        Args:
            answer (str): 原始回答
            doc_references (list): 文档引用列表
            
        Returns:
            str: 格式化后的回答
        """
        formatted_answer = answer
        
        # 添加文档引用信息
        if doc_references and len(doc_references) > 0:
            formatted_answer += "\n\n📚 **参考文档**："
            for i, doc_ref in enumerate(doc_references[:3], 1):  # 最多显示3个引用
                doc_name = doc_ref.get('doc_name', '未知文档')
                doc_title = doc_ref.get('title', '')
                if doc_title and doc_title != doc_name:
                    formatted_answer += f"\n{i}. {doc_name} - {doc_title}"
                else:
                    formatted_answer += f"\n{i}. {doc_name}"
        
        # 添加使用提示
        formatted_answer += "\n\n💡 **使用提示**：您可以继续发送政策链接或政策相关问题，我会为您提供专业的政策解读和分析。"
        
        return formatted_answer
