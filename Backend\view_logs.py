#!/usr/bin/env python3
"""
智能问数日志查看工具
用于查看和分析智能问数模块的详细日志
"""

import os
import sys
from datetime import datetime, timedelta
import re

def get_log_files():
    """获取所有日志文件"""
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    if not os.path.exists(log_dir):
        print("❌ 日志目录不存在")
        return []
    
    log_files = []
    for file in os.listdir(log_dir):
        if file.endswith('.log'):
            file_path = os.path.join(log_dir, file)
            log_files.append({
                'name': file,
                'path': file_path,
                'size': os.path.getsize(file_path),
                'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
            })
    
    return sorted(log_files, key=lambda x: x['modified'], reverse=True)

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"

def show_log_files():
    """显示所有日志文件"""
    log_files = get_log_files()
    
    if not log_files:
        print("📁 没有找到日志文件")
        return
    
    print("📋 可用的日志文件:")
    print("-" * 80)
    for i, log_file in enumerate(log_files, 1):
        print(f"{i}. {log_file['name']}")
        print(f"   大小: {format_file_size(log_file['size'])}")
        print(f"   修改时间: {log_file['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
        print()

def read_log_file(file_path, lines=50, filter_keyword=None, level_filter=None):
    """读取日志文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
        
        # 过滤日志级别
        if level_filter:
            level_pattern = f"- {level_filter.upper()} -"
            all_lines = [line for line in all_lines if level_pattern in line]
        
        # 过滤关键词
        if filter_keyword:
            all_lines = [line for line in all_lines if filter_keyword.lower() in line.lower()]
        
        # 获取最后N行
        if lines > 0:
            selected_lines = all_lines[-lines:]
        else:
            selected_lines = all_lines
        
        return selected_lines
    
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")
        return []

def analyze_smart_data_logs(file_path):
    """分析智能问数日志"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计各种操作
        stats = {
            'total_requests': len(re.findall(r'开始智能问数分析', content)),
            'successful_requests': len(re.findall(r'分析完成，总耗时', content)),
            'failed_requests': len(re.findall(r'智能问数分析失败', content)),
            'api_calls': len(re.findall(r'调用DeepSeek API', content)),
            'chart_generations': len(re.findall(r'图表生成完成', content)),
            'safety_failures': len(re.findall(r'安全校验失败', content))
        }
        
        # 提取处理时间
        time_matches = re.findall(r'分析完成，总耗时: ([\d.]+) 秒', content)
        processing_times = [float(t) for t in time_matches]
        
        # 提取常见错误
        error_patterns = [
            r'❌.*?:(.*?)(?=\n|$)',
            r'ERROR.*?:(.*?)(?=\n|$)',
            r'失败.*?:(.*?)(?=\n|$)'
        ]
        
        errors = []
        for pattern in error_patterns:
            errors.extend(re.findall(pattern, content))
        
        return {
            'stats': stats,
            'processing_times': processing_times,
            'errors': errors[:10]  # 只显示前10个错误
        }
    
    except Exception as e:
        print(f"❌ 分析日志失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 智能问数日志查看工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "list":
            show_log_files()
            return
        
        elif command == "tail":
            lines = int(sys.argv[2]) if len(sys.argv) > 2 else 50
            log_files = get_log_files()
            if not log_files:
                print("❌ 没有找到日志文件")
                return
            
            # 默认查看最新的日志文件
            latest_log = log_files[0]
            print(f"📄 查看最新日志: {latest_log['name']} (最后 {lines} 行)")
            print("-" * 80)
            
            log_lines = read_log_file(latest_log['path'], lines)
            for line in log_lines:
                print(line.rstrip())
        
        elif command == "analyze":
            log_files = get_log_files()
            smart_data_logs = [f for f in log_files if 'smart_data' in f['name']]
            
            if not smart_data_logs:
                print("❌ 没有找到智能问数日志文件")
                return
            
            latest_log = smart_data_logs[0]
            print(f"📊 分析智能问数日志: {latest_log['name']}")
            print("-" * 80)
            
            analysis = analyze_smart_data_logs(latest_log['path'])
            if analysis:
                stats = analysis['stats']
                times = analysis['processing_times']
                
                print("📈 统计信息:")
                print(f"  总请求数: {stats['total_requests']}")
                print(f"  成功请求: {stats['successful_requests']}")
                print(f"  失败请求: {stats['failed_requests']}")
                print(f"  API调用: {stats['api_calls']}")
                print(f"  图表生成: {stats['chart_generations']}")
                print(f"  安全校验失败: {stats['safety_failures']}")
                
                if times:
                    print(f"\n⏱️ 处理时间统计:")
                    print(f"  平均时间: {sum(times)/len(times):.2f} 秒")
                    print(f"  最快时间: {min(times):.2f} 秒")
                    print(f"  最慢时间: {max(times):.2f} 秒")
                
                if analysis['errors']:
                    print(f"\n❌ 常见错误 (前10个):")
                    for i, error in enumerate(analysis['errors'], 1):
                        print(f"  {i}. {error.strip()}")
        
        elif command == "filter":
            if len(sys.argv) < 3:
                print("❌ 请提供过滤关键词")
                return
            
            keyword = sys.argv[2]
            lines = int(sys.argv[3]) if len(sys.argv) > 3 else 50
            
            log_files = get_log_files()
            if not log_files:
                print("❌ 没有找到日志文件")
                return
            
            latest_log = log_files[0]
            print(f"🔍 过滤日志: {latest_log['name']} (关键词: {keyword})")
            print("-" * 80)
            
            log_lines = read_log_file(latest_log['path'], lines, filter_keyword=keyword)
            for line in log_lines:
                print(line.rstrip())
        
        else:
            print(f"❌ 未知命令: {command}")
            show_usage()
    
    else:
        show_usage()

def show_usage():
    """显示使用说明"""
    print("""
📖 使用说明:

python view_logs.py list                    # 列出所有日志文件
python view_logs.py tail [行数]              # 查看最新日志的最后N行 (默认50行)
python view_logs.py analyze                 # 分析智能问数日志统计信息
python view_logs.py filter <关键词> [行数]   # 过滤包含关键词的日志行

示例:
python view_logs.py tail 100               # 查看最后100行日志
python view_logs.py filter "API调用"       # 过滤包含"API调用"的日志
python view_logs.py filter "错误" 20       # 过滤包含"错误"的最后20行日志
""")

if __name__ == "__main__":
    main()
