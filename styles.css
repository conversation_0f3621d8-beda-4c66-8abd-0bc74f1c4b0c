/* 政务问答网站样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #e3f2fd 0%, #f8fffe 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    flex-shrink: 0;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 70px;
    position: relative;
}

.logo-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 18px;
    flex: none;
}

.logo-divider {
    width: 1px;
    height: 40px;
    background: #e0e0e0;
    margin: 0 10px;
}

.logo {
    /* 移除宽高和圆角限制，显示原始尺寸 */
    width: 270px;
    height: 60px;
    border-radius: 0;
}

.site-title {
    color: #1976d2;
    font-size: 28px;
    font-weight: 600;
}

.ai-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
}

.ai-label {
    background: rgba(255,255,255,0.2);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
}

.nav-links {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 20px;
}

.nav-link {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #2196f3;
}

/* 子导航 */
.sub-nav {
    background: #f5f5f5;
    padding: 10px 0;
    font-size: 12px;
    flex-shrink: 0;
}

.sub-nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.breadcrumb a {
    color: #2196f3;
    text-decoration: none;
}

.breadcrumb .current {
    color: #666;
}

.theme-tag {
    background: #ff5722;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    margin-left: 10px;
}

/* 主要内容区域布局调整 */
.main-content {
    display: grid;
    grid-template-columns: 340px 1000px;
    gap: 0;
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
    height: calc(100vh - 200px); /* Adjust height based on header/footer */
}

/* 中央对话区域扩展 */
.chat-area {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    height: 110%;
    border: 1px solid #e8e8e8;
}

.chat-messages {
    flex: 1 1 0;
    min-height: 0;
    overflow-y: auto;
    padding: 20px;
    scroll-behavior: smooth;
}

/* 其它栏内容溢出隐藏 */
.ai-assistant, .sidebar {
    overflow: hidden;
}

.message {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.user-message {
    justify-content: flex-end;
}

.user-message .message-content {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 70%;
}

.ai-message {
    justify-content: flex-start;
}

.message-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #2196f3, #1976d2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.ai-message .message-content {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px 18px 18px 18px;
    max-width: 80%;
}

.message-options {
    margin-top: 10px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.option-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    border: 1px solid #bbdefb;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 5px;
    align-self: flex-end;
}

.detailed .message-content {
    max-width: 90%;
}

.answer-section h4 {
    color: #333;
    margin: 15px 0 8px 0;
    font-size: 14px;
}

.answer-section ol {
    margin: 10px 0;
    padding-left: 20px;
}

.answer-section li {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.5;
}

.contact-info {
    background: #f0f7ff;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    border-left: 4px solid #2196f3;
}

.contact-info p {
    margin-bottom: 8px;
    font-size: 13px;
}

/* 输入区域 */
.chat-input-area {
    border-top: 1px solid #e0e0e0;
    padding: 20px;
}

.input-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.action-btn {
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    background: white;
    border-radius: 16px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s;
}

.action-btn:hover {
    background: #f5f5f5;
    border-color: #2196f3;
    color: #2196f3;
}

.action-btn.active {
    background: #4caf50;
    border-color: #4caf50;
    color: white;
}

.action-btn.active:hover {
    background: #45a049;
    border-color: #45a049;
    color: white;
}

.input-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ff9800;
    font-size: 12px;
    margin-bottom: 10px;
}

.input-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 24px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s;
}

.chat-input:focus {
    border-color: #2196f3;
}

.send-btn {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #2196f3, #1976d2);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s;
}

.send-btn:hover {
    transform: scale(1.05);
}

/* 输入区域高频办事板块样式 - 简约风格 */
.service-section {
    background: transparent;
    border-radius: 0;
    padding: 8px 0;
    margin-bottom: 12px;
    border: none;
    display: none; /* 默认隐藏，只有在智能导办激活时才显示 */
}

.service-section .section-title {
    font-size: 12px;
    font-weight: 500;
    color: #666;
    margin: 0 0 12px 0;
    text-align: center;
}

.service-section .section-title::before {
    display: none;
}

.service-section .service-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 12px;
    margin-bottom: 12px;
}

.service-section .service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0;
    background: transparent;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-align: center;
    min-width: 0;
}

.service-section .service-item:hover {
    transform: translateY(-2px);
    box-shadow: none;
    border-color: transparent;
}

.service-section .service-item:hover .service-icon {
    transform: scale(1.1);
}

.service-section .service-item:hover span {
    color: #2196f3;
}

.service-section .service-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
    font-size: 14px;
    color: white;
    transition: transform 0.2s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.service-section .service-item span {
    font-size: 11px;
    color: #666;
    font-weight: 400;
    transition: color 0.2s ease;
    line-height: 1.2;
}

/* 服务图标颜色主题 */
.service-icon.pink { background: linear-gradient(135deg, #e91e63, #f06292); }
.service-icon.blue { background: linear-gradient(135deg, #2196f3, #64b5f6); }
.service-icon.green { background: linear-gradient(135deg, #4caf50, #81c784); }
.service-icon.orange { background: linear-gradient(135deg, #ff9800, #ffb74d); }
.service-icon.purple { background: linear-gradient(135deg, #9c27b0, #ba68c8); }
.service-icon.cyan { background: linear-gradient(135deg, #00bcd4, #4dd0e1); }
.service-icon.teal { background: linear-gradient(135deg, #009688, #4db6ac); }
.service-icon.indigo { background: linear-gradient(135deg, #3f51b5, #7986cb); }
.service-icon.red { background: linear-gradient(135deg, #f44336, #ef5350); }
.service-icon.brown { background: linear-gradient(135deg, #795548, #a1887f); }

/* 翻页按钮样式 */
.pagination-item {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border: 1px dashed #dee2e6;
    transition: all 0.3s ease;
}

.pagination-item:hover {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;
    border-color: #2196f3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.pagination-icon {
    background: linear-gradient(135deg, #6c757d, #adb5bd) !important;
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
}

.pagination-item:hover .pagination-icon {
    background: linear-gradient(135deg, #2196f3, #64b5f6) !important;
    transform: scale(1.1);
}

.pagination-item span {
    color: #6c757d;
    font-weight: 500;
    transition: color 0.3s ease;
}

.pagination-item:hover span {
    color: #2196f3;
}

/* 右侧功能区域 */
.sidebar {
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sidebar-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.sidebar-section .quick-items,
.sidebar-section .service-grid,
.sidebar-section .faq-items {
    flex: 1;
}

.section-title {
    color: #333;
    font-size: 14px;
    margin-bottom: 12px;
    font-weight: 600;
}

.quick-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.quick-item {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-item:hover {
    background: #e3f2fd;
    border-left-color: #2196f3;
    color: #1976d2;
}

.quick-item i {
    color: #2196f3;
    font-size: 12px;
}

.quick-item span {
    font-size: 11px;
}

.service-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    flex: 1;
    align-content: start;
}

.service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    background: #fafafa;
}

.service-item:hover {
    /* 去掉蓝色背景，保留悬浮效果 */
    transform: translateY(-30px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.service-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.service-icon.pink {
    background: linear-gradient(135deg, #e91e63, #c2185b);
}

.service-icon.orange {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.service-icon.blue {
    background: linear-gradient(135deg, #2196f3, #1976d2);
}

.service-icon.heart {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.service-item span {
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
}

/* 个性卡片 */
.info-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 15px;
}

.info-card {
    background: linear-gradient(135deg, #e3f2fd, #f8fffe);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e1f5fe;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.info-card h4 {
    color: #1976d2;
    font-size: 13px;
    margin-bottom: 5px;
}

.info-card p {
    color: #666;
    font-size: 11px;
}

/* 常见问题 */
.faq-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.faq-item {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    border-left: 2px solid transparent;
}

.faq-item:hover {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

/* PC端专用布局 - 移除响应式代码 */

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 - 优化性能 */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 为使用动画的元素添加性能优化 */
.message {
    will-change: transform, opacity;
}

.thinking-message {
    will-change: transform, opacity;
}

.guide-question-item {
    will-change: transform;
}

.service-item {
    will-change: transform;
}

.quick-item {
    will-change: transform;
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.message {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载动画 */
.loading-dots::after {
    content: '...';
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% {
        content: '.';
    }
    40% {
        content: '..';
    }
    60%, 100% {
        content: '...';
    }
}

/* 底部样式 */
.footer {
    background: #ffffff;
    color: #333333;
    padding: 10px 0;
    margin-top: auto;
    border-top: 1px solid #e8e8e8;
}

.footer-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 30px;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: #333333;
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: #1976d2;
}

.footer-info {
    text-align: center;
}

.footer-info p {
    color: #333333;
    font-size: 12px;
    margin-bottom: 5px;
    line-height: 1.4;
}

.footer-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.footer-btn {
    background: linear-gradient(135deg, #ff6f00, #ff8f00);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
}

.footer-btn:link,
.footer-btn:visited,
.footer-btn:hover,
.footer-btn:active {
    text-decoration: none;
    color: white;
}

.qr-codes {
    display: flex;
    gap: 10px;
}

.qr-code {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
}

.qr-code:hover {
    background: rgba(255, 255, 255, 0.2);
}

.qr-code i {
    font-size: 20px;
    color: #81c784;
}

.qr-code span {
    font-size: 10px;
    color: #666666;
}

@media (max-width: 768px) {
    .footer-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }
    
    .footer-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
}

.suggest-question {
    color: #1976d2;
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s, color 0.2s, transform 0.1s;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}
.suggest-question:hover {
    background: #e3f2fd;
    color: #0d47a1;
    transform: scale(1.06);
    text-decoration: underline;
}

/* 校验流程步骤样式 - 美化版本 */
.process-steps {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px 15px;
    margin: 12px 0;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    width: 100%;
    max-width: 100%;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    padding: 0 16px;
    min-width: 100px;
    max-width: 160px;
}

.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 18px;
    left: calc(50% + 18px);
    width: calc(100% - 36px);
    height: 3px;
    background: linear-gradient(90deg, #dee2e6 0%, #adb5bd 100%);
    z-index: 1;
    transition: all 0.4s ease;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
}

.step-item.completed:not(:last-child)::after {
    background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.step-item.failed:not(:last-child)::after {
    background: linear-gradient(90deg, #f44336 0%, #ef5350 100%);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.step-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
    position: relative;
    border: 2px solid #fff;
    box-shadow: 0 3px 10px rgba(0,0,0,0.12);
}

.step-item.processing .step-icon {
    background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.4);
    animation: pulseBlue 2s infinite;
}

.step-item.completed .step-icon {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
    animation: checkmarkAppear 0.6s ease-out;
}

.step-item.failed .step-icon {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
    box-shadow: 0 4px 16px rgba(244, 67, 54, 0.4);
    animation: errorShake 0.6s ease-out;
}

.step-icon .fa-spin {
    color: #fff;
    font-size: 12px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
}

.step-icon .fa-check {
    color: #fff;
    font-size: 12px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
}

.step-icon .fa-times {
    color: #fff;
    font-size: 12px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
}

.step-text {
    font-size: 9px;
    color: #495057;
    font-weight: 600;
    text-align: center;
    line-height: 1.1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    transition: all 0.3s ease;
    min-height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 2px;
    text-shadow: 0 1px 2px rgba(255,255,255,0.8);
}

.step-item.processing .step-text {
    color: #1976d2;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(25, 118, 210, 0.3);
    transform: scale(1.02);
}

.step-item.failed .step-text {
    color: #d32f2f;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(211, 47, 47, 0.3);
}

.step-item.completed .step-text {
    color: #2e7d32;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(46, 125, 50, 0.3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulseBlue {
    0%, 100% {
        transform: scale(1.05);
        box-shadow: 0 4px 16px rgba(33, 150, 243, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
    }
}

@keyframes checkmarkAppear {
    0% { 
        opacity: 0;
        transform: scale(0.3) rotate(-180deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(0deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* 响应式设计 - 校验流程 */
@media (max-width: 768px) {
    .process-steps {
        padding: 15px 8px;
        margin: 10px 0;
    }
    
    .step-item {
        padding: 0 8px;
        min-width: 80px;
        max-width: 120px;
    }
    
    .step-icon {
        width: 30px;
        height: 30px;
        margin-bottom: 6px;
    }
    
    .step-icon .fa-spin,
    .step-icon .fa-check,
    .step-icon .fa-times {
        font-size: 10px;
    }
    
    .step-text {
        font-size: 8px;
    }
    
    .step-item:not(:last-child)::after {
        height: 2px;
        top: 15px;
        left: calc(50% + 15px);
        width: calc(100% - 30px);
    }
}

@media (max-width: 480px) {
    .process-steps {
        padding: 12px 6px;
    }
    
    .step-item {
        padding: 0 6px;
        min-width: 70px;
        max-width: 90px;
    }
    
    .step-icon {
        width: 26px;
        height: 26px;
        margin-bottom: 5px;
    }
    
    .step-icon .fa-spin,
    .step-icon .fa-check,
    .step-icon .fa-times {
        font-size: 9px;
    }
    
    .step-text {
        font-size: 7px;
        min-height: 10px;
    }
    
    .step-item:not(:last-child)::after {
        height: 2px;
        top: 13px;
        left: calc(50% + 13px);
        width: calc(100% - 26px);
    }
}

.step-item.processing .step-icon {
    animation: pulse 1.5s ease-in-out infinite;
}

/* 打字机特效样式 */
.typing-text {
    position: relative;
}

.typing-text::after {
    content: '|';
    position: relative;
    color: #2196f3;
    animation: blink 1s infinite;
    margin-left: 2px;
}

@keyframes blink {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

.typing-text.completed::after {
    display: none;
}

/* 反馈按钮样式 */
.feedback-buttons {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.feedback-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border: none;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e0e0e0;
    min-width: 60px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.feedback-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.feedback-btn.active {
    transform: scale(0.95);
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    animation: activeClick 0.3s ease;
}

@keyframes activeClick {
    0% { transform: scale(1); }
    50% { transform: scale(0.9); }
    100% { transform: scale(0.95); }
}

.like-btn:hover {
    background: #e8f5e8;
    color: #2e7d32;
    border-color: #4caf50;
}

.like-btn.active {
    background: #4caf50;
    color: white;
    border-color: #4caf50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.dislike-btn:hover {
    background: #ffebee;
    color: #c62828;
    border-color: #f44336;
}

.dislike-btn.active {
    background: #f44336;
    color: white;
    border-color: #f44336;
    box-shadow: 0 0 20px rgba(244, 67, 54, 0.3);
}

.correction-btn:hover {
    background: #fff3e0;
    color: #f57c00;
    border-color: #ff9800;
}

.correction-btn.active {
    background: #ff9800;
    color: white;
    border-color: #ff9800;
    box-shadow: 0 0 20px rgba(255, 152, 0, 0.3);
}

.feedback-btn i {
    font-size: 10px;
    transition: transform 0.2s ease;
}

.feedback-btn.active i {
    transform: scale(1.2);
}

/* 按钮点击效果 */
.feedback-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

/* 操作标签可点击样式 */
.option-tag {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 4px;
}

.option-tag:hover {
    background: #e3f2fd;
    color: #1976d2;
    transform: translateY(-1px);
}

/* 自定义弹窗样式 - 简约风格 */
.custom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.2s ease;
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
}

.custom-modal.show {
    opacity: 1;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    cursor: pointer;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 320px;
    max-width: 480px;
    animation: slideIn 0.2s ease;
    border: 1px solid #e8eaed;
}

@keyframes slideIn {
    from {
        transform: translate(-50%, -60%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e8eaed;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    color: #1a73e8;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-header h3 i {
    font-size: 16px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #5f6368;
    cursor: pointer;
    padding: 0;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #3c4043;
}

.modal-body {
    padding: 20px 24px;
    text-align: center;
}

.modal-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 20px;
}

.modal-icon.info {
    background: #e8f0fe;
    color: #1a73e8;
}

.modal-icon.success {
    background: #e8f5e8;
    color: #34a853;
}

.modal-icon.warning {
    background: #fef7e0;
    color: #fbbc04;
}

.modal-icon.phone {
    background: #e8f5e8;
    color: #34a853;
}

.modal-body p {
    margin: 0;
    font-size: 14px;
    color: #5f6368;
    line-height: 1.5;
}

.modal-footer {
    padding: 16px 24px 20px;
    text-align: center;
}

.modal-btn {
    background: #1a73e8;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modal-btn:hover {
    background: #1557b0;
    transform: translateY(-1px);
}

/* 联网搜索协议弹窗样式 - 简约风格 */
.agreement-modal .modal-content {
    max-width: 600px;
    max-height: 85vh;
    width: 90vw;
}

.agreement-content {
    max-height: 85vh;
    overflow-y: auto;
}

.agreement-text {
    text-align: left;
}

.agreement-text h4 {
    margin: 0 0 16px 0;
    color: #ea8600;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.agreement-content-text {
    font-size: 13px;
    line-height: 1.6;
    color: #3c4043;
}

.agreement-content-text p {
    margin: 14px 0 6px 0;
    font-weight: 500;
    color: #1a73e8;
}

.agreement-content-text ul {
    margin: 6px 0 14px 0;
    padding-left: 18px;
}

.agreement-content-text li {
    margin: 4px 0;
    color: #5f6368;
}

.text-danger {
    color: #d93025 !important;
    font-weight: 500;
}

.text-warning {
    color: #ea8600;
}

.agreement-highlight {
    background: #f8f9fa;
    border: 1px solid #e8eaed;
    border-left: 3px solid #1a73e8;
    padding: 12px 16px;
    margin: 16px 0;
    border-radius: 6px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    color: #3c4043;
}

.agreement-highlight i {
    color: #1a73e8;
    margin-top: 2px;
    font-size: 14px;
}

.agreement-footer {
    display: flex;
    justify-content: center;
    gap: 10px;
    border-top: 1px solid #e8eaed;
    margin-top: 0;
    padding-top: 16px;
}

.modal-cancel {
    background: #5f6368;
    color: white;
    border: 1px solid #5f6368;
}

.modal-cancel:hover {
    background: #4a4d52;
    border-color: #4a4d52;
    transform: translateY(-1px);
}

.modal-agree {
    background: #34a853;
    color: white;
    border: 1px solid #34a853;
}

.modal-agree:hover {
    background: #2d8f47;
    border-color: #2d8f47;
    transform: translateY(-1px);
}

/* 协议弹窗响应式 */
@media (max-width: 768px) {
    .agreement-modal .modal-content {
        max-width: 95vw;
        margin: 20px auto;
        max-height: 85vh;
    }
    
    .agreement-content-text {
        font-size: 13px;
    }
    
    .agreement-footer {
        flex-direction: column;
        gap: 8px;
    }
    
    .modal-btn {
        padding: 12px 24px;
        font-size: 14px;
    }
}



/* 文档引用样式 - 优化版 */
.doc-references {
    margin-top: 12px;
    padding: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px;
    border-left: 3px solid #007bff;
    font-size: 13px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.doc-references-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 12px;
}

.doc-references-header i {
    font-size: 14px;
    color: #007bff;
}

.doc-references-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.doc-reference-item {
    background: white;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 8px 10px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.doc-reference-item:hover {
    box-shadow: 0 2px 12px rgba(0,123,255,0.15);
    border-color: #007bff;
    transform: translateY(-1px);
}

.doc-reference-header {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 20px;
}

.doc-reference-index {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.doc-reference-title {
    font-weight: 500;
    color: #2c3e50;
    font-size: 12px;
    line-height: 1.3;
    flex: 1;
}

.doc-reference-content-wrapper {
    margin-top: 4px;
}

.doc-reference-content {
    color: #495057;
    line-height: 1.4;
    font-size: 11px;
    padding: 6px 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 2px solid #dee2e6;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 80px;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.doc-reference-content.collapsed {
    max-height: 80px;
}

.doc-reference-content.expanded {
    max-height: none;
}

.doc-expand-btn {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #90caf9;
    color: #1976d2;
    cursor: pointer;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    display: inline-flex;
    align-items: center;
    gap: 3px;
    transition: all 0.2s ease;
    font-weight: 500;
    margin-left: 4px;
}

.doc-expand-btn:hover {
    background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
    color: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(25,118,210,0.2);
}

.doc-expand-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(25,118,210,0.2);
}

.doc-expand-btn i {
    font-size: 8px;
    transition: transform 0.2s ease;
}

.doc-reference-link {
    margin-top: 8px;
}

.doc-reference-link a {
    color: #1976d2;
    text-decoration: none;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: color 0.2s ease;
}

.doc-reference-link a:hover {
    color: #1565c0;
    text-decoration: underline;
}

.doc-reference-link i {
    font-size: 12px;
}

/* 网页标题链接样式 */
.doc-reference-web-title {
    color: #1976d2 !important;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: color 0.3s ease;
    cursor: pointer;
}

.doc-reference-web-title:hover {
    color: #1565c0 !important;
    text-decoration: underline;
}

.doc-reference-web-title i.fa-globe {
    color: #28a745;
}

.doc-reference-web-title i.fa-external-link-alt {
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .doc-references {
        padding: 10px;
        font-size: 12px;
    }

    .doc-reference-item {
        padding: 6px 8px;
    }

    .doc-reference-title {
        font-size: 11px;
    }

    .doc-reference-content {
        font-size: 10px;
        padding: 5px 6px;
        max-height: 60px;
    }

    .doc-expand-btn {
        font-size: 9px;
        padding: 1px 4px;
    }

    .doc-reference-index {
        width: 14px;
        height: 14px;
        font-size: 9px;
    }
}

/* 左侧AI助手区域 */
.ai-assistant {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    height: 110%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: space-between;
    border: 1px solid #e8e8e8;
    margin-right: 20px;
    overflow: hidden;
}

.ai-greeting {
    flex-shrink: 0;
    margin-bottom: 5px;
    text-align: left;
    padding-left: 8px;
}

.ai-avatar-center {
    flex: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 150px;
    padding-top: 30px;
    margin-bottom: 10px;
}

.ai-greeting h2 {
    color: #1976d2;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 5px;
    letter-spacing: 0.5px;
    word-break: break-all;
    white-space: pre-line;
}

.ai-hi {
    font-size: 28px;
    color: #1976d2;
    font-weight: 800;
    line-height: 2.2;
    display: block;
}

.ai-desc {
    font-size: 16.5px;
    color: #1976d2;
    font-weight: 700;
    line-height: 2.1;
    display: block;
}

.ai-subtitle {
    color: #2196f3;
    font-size: 12px;
    margin-top: 18px;
    margin-bottom: 0;
    font-weight: 400;
    letter-spacing: 0.5px;
    line-height: 1.9;
    text-align: left;
    padding-left: 2px;
}

.ai-disclaimer {
    color: #1976d2;
    font-size: 12px;
    margin-top: 12px;
    margin-bottom: 0;
    font-weight: 600;
    letter-spacing: 0.3px;
    line-height: 1.6;
    text-align: left;
    padding: 8px 10px;
    background: rgba(25, 118, 210, 0.08);
    border-radius: 6px;
    border-left: 3px solid #1976d2;
    font-style: normal;
    position: relative;
}

.ai-disclaimer::before {
    content: "💡";
    margin-right: 5px;
    font-size: 11px;
}

/* 个性卡片样式 */
.personality-cards {
    margin: 0 0 10px 0;
    padding: 5px 8px 0 0px;
    flex: none;
}

.cards-title {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    text-align: center;
}

.cards-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px 12px;
    row-gap: 15px;
}

.personality-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    min-height: 65px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.personality-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.16);
    background: rgba(255, 255, 255, 1);
}

.card-icon {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.card-icon.purple {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.blue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.pink {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.green {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content {
    flex: 1;
    min-width: 0;
}

.card-content h4 {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin: 0 0 3px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-content p {
    font-size: 11px;
    color: #666;
    margin: 0;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ai-info {
    flex: none;
    padding-top: 10px;
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 10px;
}

.info-item {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.info-item i {
    color: #2196f3;
    margin-top: 2px;
    flex-shrink: 0;
}

.service-next-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-width: 0;
    min-height: 0;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 1px dashed #90caf9;
    border-radius: 8px;
    color: #1976d2;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(33,150,243,0.06);
    outline: none;
    gap: 6px;
    margin: 0;
    grid-column: auto;
    grid-row: auto;
}
.service-next-btn i {
    font-size: 18px;
}
.service-next-btn:hover, .service-next-btn:focus {
    /* 去掉蓝色背景渐变，保留其他悬浮效果 */
    color: #0d47a1;
    border-color: #1976d2;
    box-shadow: 0 4px 16px rgba(33,150,243,0.12);
    transform: translateY(-1px);
}
.service-next-btn .service-icon.gray {
    background: linear-gradient(135deg, #e0e0e0, #bdbdbd) !important;
    color: #757575;
}

/* 智能问策模式特殊样式 - 简约版 */
.smart-policy-mode .service-item[data-label="智能问策"] {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    border: none;
    border-radius: 8px;
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.25);
    transition: all 0.3s ease;
}

.smart-policy-mode .service-item[data-label="智能问策"] span {
    color: white;
    font-weight: 600;
}

.smart-policy-mode .service-item[data-label="智能问策"] .service-icon {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: none;
}

.smart-policy-mode .service-item[data-label="智能问策"]:hover {
    transform: scale(1.12);
    box-shadow: 0 12px 32px rgba(33, 150, 243, 0.3);
}

/* 智能问数模式特殊样式 - 简约版 */
.smart-data-mode .service-item[data-label="智能问数"] {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    border: none;
    border-radius: 8px;
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(76, 175, 80, 0.25);
    transition: all 0.3s ease;
}

.smart-data-mode .service-item[data-label="智能问数"] span {
    color: white;
    font-weight: 600;
}

.smart-data-mode .service-item[data-label="智能问数"] .service-icon {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: none;
}

.smart-data-mode .service-item[data-label="智能问数"]:hover {
    transform: scale(1.12);
    box-shadow: 0 12px 32px rgba(76, 175, 80, 0.3);
}

/* 思考动画样式 */
.thinking-message {
    opacity: 0;
    animation: fadeInThinking 0.5s ease-in-out forwards;
}

.thinking-animation {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 16px;
    border: 2px solid #dee2e6;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.thinking-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
    will-change: transform;
}

.thinking-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 16px;
}

.thinking-icon {
    color: #007bff;
    animation: pulse 1.5s ease-in-out infinite;
}

.thinking-dots {
    display: inline-flex;
    gap: 2px;
}

.thinking-dots .dot {
    display: inline-block;
    width: 4px;
    height: 4px;
    background: #007bff;
    border-radius: 50%;
    animation: wave 1.4s ease-in-out infinite;
}

.thinking-dots .dot:nth-child(1) { animation-delay: 0s; }
.thinking-dots .dot:nth-child(2) { animation-delay: 0.2s; }
.thinking-dots .dot:nth-child(3) { animation-delay: 0.4s; }

.thinking-progress {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3, #007bff);
    border-radius: 2px;
    animation: progress 3s ease-in-out infinite;
}

/* 动画关键帧 */
@keyframes fadeInThinking {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes wave {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 1;
    }
    30% {
        transform: translateY(-10px);
        opacity: 0.7;
    }
}

@keyframes progress {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 教育入学智能导办样式 */
.education-guide {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e8eaed;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.education-guide h4 {
    color: #1a73e8;
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.education-guide h4 i {
    font-size: 16px;
    color: #1a73e8;
}

.education-guide p {
    color: #5f6368;
    margin: 0 0 16px 0;
    font-size: 14px;
    line-height: 1.4;
}



.guide-question-item {
    background: #ffffff;
    border: 1px solid #dadce0;
    border-radius: 8px;
    padding: 12px 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    min-height: 44px;
}

.guide-question-item:hover {
    /* 去掉背景色，只保留边框和阴影的微妙变化 */
    border-color: #dadce0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}



.guide-question-item i {
    color: #5f6368;
    font-size: 14px;
    transition: color 0.2s ease;
    flex-shrink: 0;
}

.guide-question-item:hover i {
    color: #1a73e8;
}

.guide-question-item span {
    color: #3c4043;
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
    font-weight: 400;
}

.guide-question-item:hover span {
    color: #1a73e8;
}

/* 点击效果 */
.guide-question-item:active {
    /* 去掉蓝色背景，只保留轻微的视觉反馈 */
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 教育导办移动端响应式设计 */
@media (max-width: 768px) {
    .education-guide {
        padding: 20px 16px;
        border-radius: 8px;
    }

    .education-guide h4 {
        font-size: 15px;
    }

    .education-guide p {
        font-size: 13px;
        margin-bottom: 12px;
    }



    .guide-question-item {
        padding: 10px 12px;
        min-height: 40px;
        border-radius: 6px;
    }

    .guide-question-item i {
        font-size: 13px;
    }

    .guide-question-item span {
        font-size: 13px;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {


    .guide-question-item {
        min-height: 42px;
    }
}

/* Markdown 渲染内容样式 */
.message-content .markdown-content {
    line-height: 1.6;
}

.message-content .markdown-content h1,
.message-content .markdown-content h2,
.message-content .markdown-content h3,
.message-content .markdown-content h4,
.message-content .markdown-content h5,
.message-content .markdown-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #333;
}

.message-content .markdown-content h1 { font-size: 1.5em; }
.message-content .markdown-content h2 { font-size: 1.3em; }
.message-content .markdown-content h3 { font-size: 1.2em; }
.message-content .markdown-content h4 { font-size: 1.1em; }

.message-content .markdown-content p {
    margin: 8px 0;
}

.message-content .markdown-content ul,
.message-content .markdown-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content .markdown-content li {
    margin: 4px 0;
}

.message-content .markdown-content blockquote {
    border-left: 4px solid #2196f3;
    margin: 12px 0;
    padding: 8px 16px;
    background: #f8f9fa;
    color: #666;
    font-style: italic;
}

.message-content .markdown-content code {
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #d63384;
}

.message-content .markdown-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
}

.message-content .markdown-content pre code {
    background: none;
    padding: 0;
    color: #333;
}

.message-content .markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    font-size: 14px;
}

.message-content .markdown-content th,
.message-content .markdown-content td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
}

.message-content .markdown-content th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.message-content .markdown-content tr:nth-child(even) {
    background: #f9f9f9;
}

.message-content .markdown-content a {
    color: #2196f3;
    text-decoration: none;
}

.message-content .markdown-content a:hover {
    text-decoration: underline;
}

.message-content .markdown-content strong {
    font-weight: 600;
    color: #333;
}

.message-content .markdown-content em {
    font-style: italic;
    color: #666;
}

/* 图片样式 */
.message-content .markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1em 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: block;
    margin-left: auto;
    margin-right: auto;
}

/* 反馈弹窗样式 */
.feedback-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
}

.feedback-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.feedback-modal.hiding {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeOut 0.2s ease;
}

.feedback-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
}

.feedback-modal.show .feedback-modal-content {
    animation: slideIn 0.3s ease;
}

.feedback-modal.hiding .feedback-modal-content {
    animation: slideOut 0.2s ease;
}

.feedback-modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.feedback-modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
}

.feedback-modal-body {
    padding: 20px 24px;
}

.feedback-reasons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.feedback-reason-btn {
    padding: 10px 16px;
    border: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.feedback-reason-btn:hover {
    border-color: #1976d2;
    color: #1976d2;
    background: #e3f2fd;
}

.feedback-reason-btn.selected {
    border-color: #1976d2;
    background: #1976d2;
    color: white;
}

.feedback-textarea-container {
    position: relative;
}

.feedback-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    outline: none;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.feedback-textarea:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.feedback-char-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: #999;
    background: white;
    padding: 2px 4px;
}

.feedback-modal-footer {
    padding: 16px 24px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #f0f0f0;
}

.feedback-btn-cancel,
.feedback-btn-confirm {
    padding: 8px 24px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.feedback-btn-cancel {
    background: #f5f5f5;
    color: #666;
    border-color: #e0e0e0;
}

.feedback-btn-cancel:hover {
    background: #e0e0e0;
    color: #333;
}

.feedback-btn-confirm {
    background: #1976d2;
    color: white;
}

.feedback-btn-confirm:hover {
    background: #1565c0;
}

.feedback-btn-confirm:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .feedback-reasons {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .feedback-reason-btn {
        padding: 8px 12px;
        font-size: 13px;
    }

    .feedback-modal-content {
        width: 95%;
        margin: 20px;
    }

    .feedback-modal-header,
    .feedback-modal-body,
    .feedback-modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
}

/* 内容纠错弹窗样式 */
.correction-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
}

.correction-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.correction-modal.hiding {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeOut 0.2s ease;
}

.correction-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
}

.correction-modal.show .correction-modal-content {
    animation: slideIn 0.3s ease;
}

.correction-modal.hiding .correction-modal-content {
    animation: slideOut 0.2s ease;
}

.correction-modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.correction-modal-header h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.correction-subtitle {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.correction-modal-body {
    padding: 20px 24px;
}

.correction-form-group {
    margin-bottom: 20px;
    position: relative;
}

.correction-form-group:last-child {
    margin-bottom: 0;
}

.correction-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.correction-label .required {
    color: #f44336;
    margin-left: 2px;
}

.correction-textarea {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    outline: none;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
    line-height: 1.5;
}

.correction-textarea:focus {
    border-color: #ff9800;
    box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.1);
}

.correction-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    outline: none;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.correction-input:focus {
    border-color: #ff9800;
    box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.1);
}

.correction-char-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: #999;
    background: white;
    padding: 2px 4px;
    border-radius: 2px;
}

.correction-contact-group {
    margin-top: 16px;
}

.correction-modal-footer {
    padding: 16px 24px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #f0f0f0;
}

.correction-btn-cancel,
.correction-btn-confirm {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    font-weight: 500;
}

.correction-btn-cancel {
    background: #f5f5f5;
    color: #666;
    border-color: #e0e0e0;
}

.correction-btn-cancel:hover {
    background: #e0e0e0;
    color: #333;
}

.correction-btn-confirm {
    background: #ff9800;
    color: white;
}

.correction-btn-confirm:hover {
    background: #f57c00;
}

.correction-btn-confirm:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .correction-modal-content {
        width: 95%;
        margin: 20px;
        max-height: 90vh;
    }

    .correction-modal-header,
    .correction-modal-body,
    .correction-modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }

    .correction-textarea {
        min-height: 60px;
    }
}

/* ==================== 市情概况样式 ==================== */
.city-info-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e3f2fd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.city-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e3f2fd;
}

.city-info-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #2196f3, #1976d2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: white;
    font-size: 14px;
}

.city-info-header h3 {
    margin: 0;
    color: #1976d2;
    font-size: 18px;
    font-weight: 600;
}

.city-info-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    font-size: 14px;
}

.city-info-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.city-option-item {
    flex: 1;
    min-width: 140px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #e3f2fd;
    border-radius: 6px;
    padding: 8px 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 8px;
    height: 40px;
}

.city-option-item:hover {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #2196f3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.city-option-item .option-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #2196f3, #1976d2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    flex-shrink: 0;
}

.city-option-item span {
    color: #333;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
}

.city-info-content {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-top: 10px;
}

.city-info-content h4 {
    color: #1976d2;
    margin: 0 0 15px 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.city-intro-section p {
    margin-bottom: 12px;
    line-height: 1.6;
    color: #333;
}

.city-intro-section ul {
    margin: 10px 0;
    padding-left: 20px;
}

.city-intro-section li {
    margin-bottom: 6px;
    color: #555;
    line-height: 1.5;
}

.leadership-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.leader-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #2196f3;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.leader-info h5 {
    color: #1976d2;
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.leader-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.leader-desc {
    color: #666;
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
}

.leadership-note {
    background: #e3f2fd;
    padding: 12px;
    border-radius: 8px;
    color: #1976d2;
    font-size: 13px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.attractions-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.attraction-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #4caf50;
}

.attraction-item h5 {
    color: #2e7d32;
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.attraction-item ul {
    margin: 0;
    padding-left: 20px;
}

.attraction-item li {
    margin-bottom: 8px;
    color: #555;
    line-height: 1.5;
    font-size: 13px;
}

.attractions-note {
    background: #e8f5e8;
    padding: 12px;
    border-radius: 8px;
    color: #2e7d32;
    font-size: 13px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 市情概况响应式设计 */
@media (max-width: 768px) {
    .city-info-options {
        flex-direction: column;
        gap: 8px;
    }

    .city-option-item {
        min-width: auto;
        padding: 6px 12px;
        gap: 6px;
        height: 36px;
    }

    .city-option-item .option-icon {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .city-option-item span {
        font-size: 13px;
        font-weight: 600;
    }

    .city-info-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
        margin-right: 8px;
    }

    .city-info-header h3 {
        font-size: 16px;
    }

    .city-info-content {
        padding: 15px;
    }

    .city-info-content h4 {
        font-size: 16px;
    }

    .leadership-section {
        gap: 12px;
    }

    .leader-card {
        padding: 12px;
    }

    .attraction-item {
        padding: 12px;
    }
}

/* 图表样式 */
.charts-container {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.charts-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.charts-header i {
    color: #007bff;
    font-size: 16px;
}

.charts-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chart-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
    font-size: 16px;
}

.chart-canvas-container {
    position: relative;
    height: 400px;
    width: 100%;
}

.chart-canvas {
    max-width: 100%;
    max-height: 100%;
}

.chart-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #dc3545;
    font-size: 14px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dc3545;
}

/* 响应式图表 */
@media (max-width: 768px) {
    .chart-canvas-container {
        height: 300px;
    }

    .charts-container {
        margin: 10px 0;
        padding: 10px;
    }

    .chart-item {
        padding: 10px;
    }

    .chart-title {
        font-size: 14px;
        margin-bottom: 10px;
    }
}
