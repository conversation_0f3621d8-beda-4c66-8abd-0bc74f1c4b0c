"""
问答数据库服务模块
提供Excel数据加载、问题匹配等功能
"""

import openpyxl
import os


class QADatabase:
    """问答数据库服务"""
    
    def __init__(self, excel_path=None):
        if excel_path is None:
            # 自动检测Excel文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 如果在Backend目录下，向上一级查找DB目录
            project_root = os.path.dirname(current_dir)
            excel_path = os.path.join(project_root, 'DB', 'QAlist.xlsx')
            
            # 如果在项目根目录下，直接查找DB目录
            if not os.path.exists(excel_path):
                excel_path = os.path.join(current_dir, '..', 'DB', 'QAlist.xlsx')
            
            # 如果还是没找到，使用相对路径
            if not os.path.exists(excel_path):
                excel_path = 'DB/QAlist.xlsx'
        
        self.excel_path = excel_path
        self.qa_data = []
        self.load_qa_data()
    
    def load_qa_data(self):
        """从Excel文件加载问答数据 - 支持8列格式"""
        try:
            if not os.path.exists(self.excel_path):
                print(f"警告: Excel文件 {self.excel_path} 不存在")
                return
            
            workbook = openpyxl.load_workbook(self.excel_path)
            
            if 'Sheet2' not in workbook.sheetnames:
                print("警告: Excel文件中没有找到Sheet2工作表")
                return
            
            sheet = workbook['Sheet2']
            
            # 跳过标题行，从第2行开始读取数据
            for row_num in range(2, sheet.max_row + 1):
                qa_id = sheet.cell(row=row_num, column=1).value       # A列：问题ID
                type_col = sheet.cell(row=row_num, column=2).value    # B列：类型
                question = sheet.cell(row=row_num, column=3).value    # C列：问题
                answer = sheet.cell(row=row_num, column=4).value      # D列：答案
                keywords = sheet.cell(row=row_num, column=5).value    # E列：关键词
                doit = sheet.cell(row=row_num, column=6).value        # F列：是否显示立刻办理
                url = sheet.cell(row=row_num, column=7).value         # G列：链接
                phone = sheet.cell(row=row_num, column=8).value       # H列：电话
                
                if question and answer:  # 确保问题和答案都不为空
                    # 清理答案中的HTML实体
                    clean_answer = answer.replace('&#10;', '\n') if answer else ''
                    
                    # 处理关键词（使用英文分号分隔）
                    keyword_list = []
                    if keywords:
                        # 只使用英文分号分隔
                        if ';' in str(keywords):
                            keyword_list = [kw.strip() for kw in str(keywords).split(';') if kw.strip()]
                        else:
                            # 如果没有分号，整个字符串作为一个关键词
                            keyword_list = [str(keywords).strip()]
                    
                    self.qa_data.append({
                        'id': int(qa_id) if qa_id else row_num - 1,
                        'type': str(type_col).strip() if type_col else '',
                        'question': question.strip(),
                        'answer': clean_answer.strip(),
                        'keywords': keyword_list,
                        'doit': str(doit).strip() if doit else '否',
                        'url': str(url).strip() if url and str(url) != 'None' else '',
                        'phone': str(phone).strip() if phone else '010-88888888'
                    })
            
            print(f"成功加载 {len(self.qa_data)} 条问答数据")

            # 添加系统介绍数据
            self._add_system_data()
            print(f"📝 数据库中现在共有 {len(self.qa_data)} 条记录")

        except Exception as e:
            print(f"加载Excel数据时出错: {e}")
    
    def _add_system_data(self):
        """添加系统介绍等基础数据"""
        system_data = {
            'id': 9999,
            'type': '系统介绍',
            'question': '你是谁？',
            'answer': '您好！我是智能政务助手，专门为您提供各类政务服务咨询和办事指导。\n\n我可以帮助您：\n• 查询各类政务服务办理流程\n• 提供政策解读和咨询\n• 指导在线办事操作\n• 解答常见问题\n\n如需人工服务，请拨打咨询电话。',
            'keywords': ['你是谁', '介绍', '助手', '机器人'],
            'doit': '否',
            'url': '',
            'phone': '010-88888888'
        }

        # 检查是否已经有相同问题的数据
        has_data = any(
            qa.get('question', '').strip() == system_data['question']
            for qa in self.qa_data
        )

        if not has_data:
            self.qa_data.append(system_data)
            print(f"📝 添加了系统数据: {system_data['question']}")
    
    def remove_punctuation(self, text):
        """去除文本中的标点符号"""
        punctuation_chars = '！？｡。，、；：（）【】《》""''…—!?.,;:()[]<>"\'`~@#$%^&*+=|\\_-'
        clean_text = text
        for char in punctuation_chars:
            clean_text = clean_text.replace(char, '')
        return clean_text

    def is_cross_domain_mismatch(self, user_question, qa_question):
        """检查是否存在跨领域匹配问题"""
        user_question_lower = user_question.lower()
        qa_question_lower = qa_question.lower()

        # 定义不同领域的关键词
        tourism_keywords = ['旅游', '景点', '游玩', '观光', '风景', '名胜', '古迹', '公园']
        policy_keywords = ['政策', '人才', '奖励', '补贴', '落户', '户口', '引进', '待遇']
        business_keywords = ['营业', '执照', '注册', '公司', '企业', '工商', '税务']
        education_keywords = ['学校', '教育', '入学', '学区', '招生', '考试']

        # 检查用户问题的领域
        user_tourism = any(kw in user_question_lower for kw in tourism_keywords)
        user_policy = any(kw in user_question_lower for kw in policy_keywords)
        user_business = any(kw in user_question_lower for kw in business_keywords)
        user_education = any(kw in user_question_lower for kw in education_keywords)

        # 检查数据库问题的领域
        qa_tourism = any(kw in qa_question_lower for kw in tourism_keywords)
        qa_policy = any(kw in qa_question_lower for kw in policy_keywords)
        qa_business = any(kw in qa_question_lower for kw in business_keywords)
        qa_education = any(kw in qa_question_lower for kw in education_keywords)

        # 检查是否存在跨领域匹配
        return (
            (user_tourism and (qa_policy or qa_business or qa_education)) or
            (user_policy and (qa_tourism or qa_business or qa_education)) or
            (user_business and (qa_tourism or qa_policy or qa_education)) or
            (user_education and (qa_tourism or qa_policy or qa_business))
        )

    def find_answer(self, question):
        """根据问题查找答案 - 支持精确匹配和关键词匹配"""
        question_clean = question.strip()
        question_lower = question_clean.lower()
        # 去掉标点符号的用户问题（用于关键词匹配）
        question_no_punct = self.remove_punctuation(question_lower)

        print(f"🔍 查找问题: '{question_clean}'")
        print(f"🔍 数据库中共有 {len(self.qa_data)} 条记录")

        # 1. 精确匹配（完全相同）
        for qa in self.qa_data:
            if qa['question'].strip() == question_clean:
                print(f"✅ 精确匹配成功: {qa['question']}")
                return qa

        # 2. 忽略大小写的精确匹配
        for qa in self.qa_data:
            if qa['question'].strip().lower() == question_lower:
                print(f"✅ 大小写忽略匹配成功: {qa['question']}")
                return qa

        # 3. 关键词匹配
        print("📋 开始关键词匹配...")
        for qa in self.qa_data:
            if qa['keywords']:  # 如果有关键词列表
                for keyword in qa['keywords']:
                    keyword_clean = keyword.strip().lower()
                    if keyword_clean and len(keyword_clean) >= 2:  # 关键词至少2个字符
                        # 用户问题包含关键词
                        if keyword_clean in question_no_punct or keyword_clean in question_lower:
                            # 简单的领域检查：避免明显的跨领域匹配
                            if self.is_cross_domain_mismatch(question_no_punct, qa['question']):
                                print(f"🚫 跳过跨领域关键词匹配: '{keyword_clean}' - '{qa['question']}'")
                                continue
                            print(f"✅ 关键词匹配成功: '{keyword_clean}' 匹配问题 '{qa['question']}'")
                            return qa

                        # 关键词包含用户问题（只有当用户问题足够长时）
                        if len(question_no_punct) >= 3 and (question_no_punct in keyword_clean or question_lower in keyword_clean):
                            if self.is_cross_domain_mismatch(question_no_punct, qa['question']):
                                print(f"🚫 跳过跨领域反向关键词匹配: '{keyword_clean}' - '{qa['question']}'")
                                continue
                            print(f"✅ 反向关键词匹配成功: '{keyword_clean}' 包含 '{question_lower}' 匹配问题 '{qa['question']}'")
                            return qa

        # 4. 包含匹配
        print("📋 开始包含匹配...")
        for qa in self.qa_data:
            qa_question_clean = qa['question'].strip()
            qa_question_lower = qa_question_clean.lower()
            qa_question_no_punct = self.remove_punctuation(qa_question_lower)

            # 只有当包含的部分足够长时才认为是有效匹配
            min_match_length = 4  # 至少4个字符才认为是有效匹配

            # 检查用户问题是否包含数据库问题的关键部分
            if len(qa_question_no_punct) >= min_match_length and qa_question_no_punct in question_no_punct:
                print(f"✅ 包含匹配成功: 用户问题包含 '{qa['question']}'")
                return qa

            # 检查数据库问题是否包含用户问题的关键部分
            if len(question_no_punct) >= min_match_length and question_no_punct in qa_question_no_punct:
                print(f"✅ 包含匹配成功: 数据库问题包含 '{qa['question']}'")
                return qa

        # 5. 模糊匹配
        print("📋 开始模糊匹配...")
        for qa in self.qa_data:
            qa_question_clean = qa['question'].strip().lower()
            qa_question_no_punct = self.remove_punctuation(qa_question_clean)

            # 只有当问题长度相近且有足够的字符重叠时才匹配
            if len(question_no_punct) >= 3 and len(qa_question_no_punct) >= 3:
                # 计算字符重叠度
                common_chars = set(question_no_punct) & set(qa_question_no_punct)
                question_chars = set(question_no_punct)
                qa_chars = set(qa_question_no_punct)

                # 计算相似度：共同字符数 / 较短问题的字符数
                min_length = min(len(question_chars), len(qa_chars))
                similarity = len(common_chars) / min_length if min_length > 0 else 0

                # 提高相似度阈值，避免误匹配
                if similarity >= 0.6 and len(common_chars) >= 3:
                    # 避免跨领域匹配
                    if self.is_cross_domain_mismatch(question_no_punct, qa['question']):
                        print(f"🚫 跳过跨领域模糊匹配: '{qa['question']}'")
                        continue

                    print(f"✅ 模糊匹配成功: '{qa['question']}' (相似度: {similarity:.2f})")
                    return qa

        print("❌ 未找到匹配的答案")
        return None
