{"_id": "color-convert", "_rev": "86-aa2f322439936ee182db8c4337a6b9bc", "name": "color-convert", "dist-tags": {"latest": "3.1.0"}, "versions": {"0.1.0": {"name": "color-convert", "version": "0.1.0", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.1.0", "dist": {"shasum": "1d55fd8784288d323a6fcc31077f79b632a12ec9", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.1.0.tgz", "integrity": "sha512-2l6bh1YOdc3zRK432q6ka7oJ5RDq/fHkeF2gOc7O82B0GH65eTp6eZe8kNAE+qzJNMp/Pi7IoD4ruNa3Y1KB1Q==", "signatures": [{"sig": "MEQCICI2ggkdzzokrrrW31DOMVfEkmNTvM+kmjJzFSJmp67kAiBLywLpSBzh7mgG0HOTjEwrMpHqVEQaRSWkXGeGEAmtMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./convertions", "engines": {"node": "*"}, "scripts": {}, "repository": {"url": "git://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.0.5", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}, "_engineSupported": true}, "0.2.0": {"name": "color-convert", "version": "0.2.0", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.2.0", "dist": {"shasum": "004174fa511dac015ef29a796a97ab7cf095cea1", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.2.0.tgz", "integrity": "sha512-dtQtr6/KzJ7+171iWAgl4as82CuSrw9xc/xKxEO67RjN45dmXYWEX58grD1vvRFF+OEdJxTnNO5f0AJHYfFmew==", "signatures": [{"sig": "MEYCIQDTX/pt2O2pEaLN5+OOfTOwFZ11Vj99rcgFqQbD8u3cvwIhANYKHvlTeO6NqSpL2oaBeIlTdRUJojWI/yZU2M/M1jsT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "scripts": {}, "repository": {"url": "git://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.0.5", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}, "_engineSupported": true}, "0.2.1": {"name": "color-convert", "version": "0.2.1", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.2.1", "dist": {"shasum": "363cab23c94b31a0d64db71048b8c6a940f8c68c", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.2.1.tgz", "integrity": "sha512-FWbwpCgyRV41Vml0iKU9UmL0dVTKORnm7ZC8h8cdfvutk2bU7ZcMLtSleggScK/IpUVXILg9Pw86LhPUQyTaVg==", "signatures": [{"sig": "MEYCIQCIgSoqvmfS7djGJJiXyhglHDb9SeJkDPx3yK0XF0SgWAIhAIU5bRFFIkzVWXX1ZypjHFnsZCuWJnEITlOlEiNfZsX5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "scripts": {}, "repository": {"url": "git://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.0.13", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "v0.5.0-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/color-convert/0.2.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}, "_engineSupported": true}, "0.3.0": {"name": "color-convert", "version": "0.3.0", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.3.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}], "dist": {"shasum": "b80a94e5791179ebe70b75284d95c6d43ad1367e", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.3.0.tgz"}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "harth", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "v0.4.10", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}, "_engineSupported": true}, "0.3.1": {"name": "color-convert", "version": "0.3.1", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.3.1", "maintainers": [{"name": "harth", "email": "<EMAIL>"}], "dist": {"shasum": "859d892b81dc849eb95cefea35084072cb362c68", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.3.1.tgz", "integrity": "sha512-OlCzfQubQiRFqKf+1JClHcGoeN3ZBbDR8JNhMiqoZow/6BPLm66+VZJY1uzk2RdfNBuRKn735iIRFsnVVXKMVg==", "signatures": [{"sig": "MEQCIEehfge84lDhfjr3mjNzdaJAhRK2rHkl2EMA1E02+j74AiAlm7dbyAeXUJRGyFZlBGBWHEvoXfMFgJnyjSmvjqYmnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "engines": {"node": "*"}, "_npmUser": {"name": "harth", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "v0.4.10", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}, "_engineSupported": true}, "0.3.4": {"name": "color-convert", "version": "0.3.4", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.3.4", "maintainers": [{"name": "harth", "email": "<EMAIL>"}], "homepage": "https://github.com/harthur/color-convert", "bugs": {"url": "https://github.com/harthur/color-convert/issues"}, "dist": {"shasum": "e5e3ab2028a57bc68d904bbfbf2ed7fd5a31a26c", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.3.4.tgz", "integrity": "sha512-HP0LFUapyR/0VJ4t5httwu4gF24EYhNE+3lBm1ILtGPq7fzOUgcV1hziYjKq5xZk/8k7/btNpt+iE7VTAeApeg==", "signatures": [{"sig": "MEYCIQDZDhSPyiH203kvVkSKnHGrgUN+sMb15kTsQv6xL/2R7AIhALPQvZrBpgz9Yv0kvT71nQs+hXLNjaKnF+GU7IfTSXjc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_npmUser": {"name": "harth", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Plain color conversion functions", "directories": {}, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}}, "0.4.0": {"name": "color-convert", "version": "0.4.0", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.4.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}], "homepage": "https://github.com/harthur/color-convert", "bugs": {"url": "https://github.com/harthur/color-convert/issues"}, "dist": {"shasum": "100207b7662987e3c5c7d7c491fbda0f58a692e1", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.4.0.tgz", "integrity": "sha512-EA8+CyOvqb9m37pxkjJJwUyrl0vmfF9iCEmnRCnJuVAjKUhDAuy8QbQaTNfMqArKYGQzelrkSOTl84hSkeNj+A==", "signatures": [{"sig": "MEQCIF1MCeAOGaFCKkybdi2rhq7VG9HrB6fXkF6Y9apDc8zKAiBTC+aJ/DLwgyyqlsD6BjX80t8Jl0MPUZShjJ/kvGTA9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_shasum": "100207b7662987e3c5c7d7c491fbda0f58a692e1", "_npmUser": {"name": "harth", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Plain color conversion functions", "directories": {}, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}}, "0.5.0": {"name": "color-convert", "version": "0.5.0", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.5.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}], "homepage": "https://github.com/harthur/color-convert", "bugs": {"url": "https://github.com/harthur/color-convert/issues"}, "dist": {"shasum": "4032cab2128c81670c7b394d77b6783f49caaaf7", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.5.0.tgz", "integrity": "sha512-M+t0Hsx0q6DmAOcqz/8KQ2I/F12N2hRUZGxp4P59ejM0trUcaOpPjIF/XaUYXz4mBdIArnGnoGpNegc6NmUBDQ==", "signatures": [{"sig": "MEUCIQC9JoMsbWgRDPXT9/dse1uezLVrFeK3Yi/eqwdgKGDnQQIgK826qKFMAnqKeYxWuxp6aqWhm1S3FNtk2xL0uaz/0wI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_shasum": "4032cab2128c81670c7b394d77b6783f49caaaf7", "_npmUser": {"name": "harth", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Plain color conversion functions", "directories": {}, "devDependencies": {"uglify-js": "1.0.x", "browserify": ">=1.0.0"}}, "0.5.1": {"name": "color-convert", "version": "0.5.1", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.5.1", "maintainers": [{"name": "harth", "email": "<EMAIL>"}], "homepage": "https://github.com/harthur/color-convert", "bugs": {"url": "https://github.com/harthur/color-convert/issues"}, "dist": {"shasum": "ef672a5d7410f0328f28771aa1d3a52dcbe98bf5", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.5.1.tgz", "integrity": "sha512-vIplGh/KRBnd322QYTS0fEwH/ukoBAMjU6xXp7aKS95jegEQCOznEWL4/wJRJBxRsa6ZJcaWloG4+bCE4kDv7g==", "signatures": [{"sig": "MEYCIQDOVaKeVMAA6B3ifWM3tjWAyjJayIO/fyxC9Ck4m10qNQIhAK7ZG9W0cVL/9u0cHOYiRdbBCjL/hx+TGJX65nlWypQi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_npmUser": {"name": "harth", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Plain color conversion functions", "directories": {}, "devDependencies": {"grunt": "^0.4.5", "uglify-js": "1.0.x", "browserify": "^6.1.0", "grunt-contrib-uglify": "^0.6.0"}}, "0.5.2": {"name": "color-convert", "version": "0.5.2", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.5.2", "maintainers": [{"name": "harth", "email": "<EMAIL>"}], "homepage": "https://github.com/harthur/color-convert", "bugs": {"url": "https://github.com/harthur/color-convert/issues"}, "dist": {"shasum": "febd9efc33674df3374ff8eeaec3bc56c79a9b35", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.5.2.tgz", "integrity": "sha512-dJyFhog4uzEPa1WGPoPCX/Tl4e3bUlmwesh6Ew4cZacsjMJSJHw9ACZOPn+UbyhVZ0mqJDq8LYH8Wn/y644RiQ==", "signatures": [{"sig": "MEUCIQCSNRhUxA00rEtOdhY+jIMpZIhdnU7eWoptb7tIewxDDQIgLIjc0MMrZnqQ5v5/NsQuX9axUk0Jqd7oOyjc5XOfh+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "scripts": {"test": "node test/basic.js"}, "_npmUser": {"name": "harth", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/harthur/color-convert.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Plain color conversion functions", "directories": {}, "devDependencies": {"grunt": "^0.4.5", "uglify-js": "1.0.x", "browserify": "^6.1.0", "grunt-contrib-uglify": "^0.6.0"}}, "0.5.3": {"name": "color-convert", "version": "0.5.3", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.5.3", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}], "homepage": "https://github.com/harthur/color-convert#readme", "bugs": {"url": "https://github.com/harthur/color-convert/issues"}, "dist": {"shasum": "bdb6c69ce660fadffe0b0007cc447e1b9f7282bd", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.5.3.tgz", "integrity": "sha512-RwBeO/B/vZR3dfKL1ye/vx8MHZ40ugzpyfeVG5GsiuGnrlMWe2o8wxBbLCpw9CsxV+wHuzYlCiWnybrIA0ling==", "signatures": [{"sig": "MEQCICFDrIV8kOzYN0AOk0Ji0dgjl6kiInXQG2P4wB++S45CAiBAMfZgpaIl88Wuk85DHdQPRrHM4vgGXc/sYNDee20UwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "bdb6c69ce660fadffe0b0007cc447e1b9f7282bd", "gitHead": "c05e34eb75de749faf15f0e362147a6add373625", "scripts": {"test": "node test/basic.js"}, "_npmUser": {"name": "moox", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/harthur/color-convert.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {}}, "0.6.0": {"name": "color-convert", "version": "0.6.0", "keywords": ["color", "colour", "rgb"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "color-convert@0.6.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}], "homepage": "https://github.com/harthur/color-convert#readme", "bugs": {"url": "https://github.com/harthur/color-convert/issues"}, "dist": {"shasum": "e1fbd9e9c2602012b1116267e25d5295ab1addc1", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.6.0.tgz", "integrity": "sha512-6B7jVX01SiI+sBL/Pb7A0Y5+vPEZHR1BbZ+7JFhGm5nUfUd5XP4ZzNemIjv1DCSVTVRHJMMQxDjKhyJnltEo1w==", "signatures": [{"sig": "MEUCIHRq9baCr8dTnTZzz9GnryBGMaK+vKrR4KYHTj+ju7M8AiEAy4iipjVL3vPfUtCcw1yPuNMDYt54rpKqBiH1iyNv05s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "e1fbd9e9c2602012b1116267e25d5295ab1addc1", "gitHead": "d024e9aeeeaa8b74ce5faa7fd864219e08946997", "scripts": {"test": "node test/basic.js"}, "_npmUser": {"name": "moox", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/harthur/color-convert.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "2.3.1", "devDependencies": {}}, "0.7.0": {"name": "color-convert", "version": "0.7.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@0.7.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/moox/color-convert#readme", "bugs": {"url": "https://github.com/moox/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "f62db9abe719dfe10263a9c175824d8d2f956dcb", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-0.7.0.tgz", "integrity": "sha512-88oCG8deVoeq8dPWhZGZ8LCSgEWN+He5pju5hsNGk5B9D1/Skw0gAR4h80w1KHP0UzXv9mXLP6jbZjmQrKP3+A==", "signatures": [{"sig": "MEUCIQDZnvkdnMJMQRZoozZdQBzGd7fioL2ExLto8Ovb9EqVyQIgXpihRbjoJTteogmRRsgp7xKdmGudmrs3BndB88jckFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js"], "_shasum": "f62db9abe719dfe10263a9c175824d8d2f956dcb", "gitHead": "2b6d8d539a06227beca9c1f61ce648ff1ba01807", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/moox/color-convert.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "4.1.1", "devDependencies": {"xo": "^0.11.2"}}, "1.0.0": {"name": "color-convert", "version": "1.0.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.0.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "3c26fcd885d272d45beacf6e41baba75c89a8579", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.0.0.tgz", "integrity": "sha512-gYYx50D9pDoH8p04NAdpwr8w5uzlmv2wnP3aKC6+9V7sw8Kw54k3ulOc/FujZmhe34PEXbzJGKtEa2wLFZwuZw==", "signatures": [{"sig": "MEYCIQD1724to4ysmZv2CvRCmGODCJD/L3YYoWFhfhuLq+1ppQIhAOM8E3OWTdZx6DAvKBcc3JsZ5b9NibrlDS02STDQbH8C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "3c26fcd885d272d45beacf6e41baba75c89a8579", "gitHead": "31cd56dc3d34ae332cc83d90bd4f925baf5bd982", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "4.1.1", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}}, "1.1.0": {"name": "color-convert", "version": "1.1.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.1.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "62b80961c1282f495177c5b49920072fed228f82", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.1.0.tgz", "integrity": "sha512-tgtQJSJa6ykUlZKx0mSLwAkQ1+P7Ye5OEw4dMiVRKxKz4gOtyLYUh9kIrO8rrcltXgG+/blB8r6C0CI/rYMyLA==", "signatures": [{"sig": "MEUCIQDASaR3+JvHrEBWy6QbYfCoWct4etxIhpNhlf9Oq9BFXwIgIW7cGJoxZNpYhc4mhz7S/Nf7zBaEvtaujBS9aET/WLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "62b80961c1282f495177c5b49920072fed228f82", "gitHead": "93bb03693050a01e2f11b56d5735945390033631", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.1.0.tgz_1459299767596_0.3268825323320925", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.1": {"name": "color-convert", "version": "1.1.1", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.1.1", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "7fdb09212afef46d88e0fa080c4b1b9cb8d8564e", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.1.1.tgz", "integrity": "sha512-spp6vdwQMIy7gfX55S5/kHNW9BTSCIjaT5wrfl0XoiTs7Gr/vqRkKd+a/pu3G6LQ0vCOclVU3AT46rV6Tyu5cA==", "signatures": [{"sig": "MEYCIQDwixgm6EqOiIhlRRUmRbr7YlMtSezD2BCQh9TZ4xDg4wIhANjngfVGCPw0cK8kXiOpDKtJy0I02Rx32J7ZqQoE/2by", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "7fdb09212afef46d88e0fa080c4b1b9cb8d8564e", "gitHead": "4766df514325f4bf52d895b569cdf83da461815c", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.1.1.tgz_1459299959881_0.024279947159811854", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.2": {"name": "color-convert", "version": "1.1.2", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.1.2", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "054024a472480dc345badb27cfe6de2eca9e4624", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.1.2.tgz", "integrity": "sha512-knYtDpaIHL/JX73cBC7MHRkUaPRJd8sbPCMJFjycG8wp+u9IP5pVyAuWdIgk6DBWq1nxP3+Va8WtXzcHlIGGUg==", "signatures": [{"sig": "MEYCIQCpArqMes1xSuevHWlC92PH1/FAt1sCgQUuRUiniRj+UgIhAJtZnacWnPuy5ZvISd5NB/0KIUKOaMpol4vIc3BXAjQv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "054024a472480dc345badb27cfe6de2eca9e4624", "gitHead": "125fe4a39df21756a3ea215f8eaf3eb2f304de93", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.1.2.tgz_1459300028367_0.6170615588780493", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.0": {"name": "color-convert", "version": "1.2.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.2.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "9c93f193d05fe1a4fb18b237d76662786646ab94", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.2.0.tgz", "integrity": "sha512-Pi0iDffrGVwcIHTLBxJVUIh29hpf4wV6lp0kBhqBDki9g4b3/JOI74sh+WOz7a71rwwLJuZc4mDSwKiyFskjuA==", "signatures": [{"sig": "MEYCIQCFZXAMyUv7Vy4Nvzo14TMlHprCFale4HRqMhVlNj8I0QIhAMpfCUQMSni1TkwpWo1KT3gRWBpYxg0BqO1LoShFSOIx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "9c93f193d05fe1a4fb18b237d76662786646ab94", "gitHead": "9cb584fc60297d8c059a5d2d489af8ecf745e911", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.2.0.tgz_1460027002556_0.05813836818560958", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.1": {"name": "color-convert", "version": "1.2.1", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.2.1", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "8599a9edc7dadbb7eb46d6b1e36ad7d742f5defa", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.2.1.tgz", "integrity": "sha512-VtkTquez5XNlap499eMN9wjELhPlOyMDJPJsIoaa/rC6VgavoSWemY5XZfKIWnNc6WB0JG0Zh4G0OiZVYRplfw==", "signatures": [{"sig": "MEUCIHsg4RDXPxTnw7F2fklKIaVGCytFZTrhrpDQUDrkZyzhAiEA4fmYOtzPd7r8jEin+XUARS+Eke0Y9ObUm5oAfxmeV8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "8599a9edc7dadbb7eb46d6b1e36ad7d742f5defa", "gitHead": "8e0f1548174f2e11933a4b6e4b417892019153b5", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.2.1.tgz_1460027099834_0.06415465543977916", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.2": {"name": "color-convert", "version": "1.2.2", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.2.2", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "efbe95697dd37d7eecfae1fd2cfbeb84cc25064e", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.2.2.tgz", "integrity": "sha512-yXAhpn5+dVTIW1ADPkmcFu3W2c9yZJnmRsbA8tExvSL9NcBI/mEVGECZHVbRln+1K7BL031a4VzkMe9+b9R+Qw==", "signatures": [{"sig": "MEUCIAdxn+pozRDSnF4gzqzH/2BEXvThbsdxWV152l/+YPFzAiEAlpUHkquGuCY0Q1RKariAOlk7DtcC6ggNvsPFOb19BB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "efbe95697dd37d7eecfae1fd2cfbeb84cc25064e", "gitHead": "7fda3d54d14e6689a7f7628e8ddd014532d1b30a", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.2.2.tgz_1460027191020_0.34015593072399497", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "color-convert", "version": "1.3.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.3.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "4ff8595dee13e30270653a9725b1adce6c3d728f", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.3.0.tgz", "integrity": "sha512-7ZT/iBoBkke70k2xunt2EsFr+zAznLU47IZqBDSYI6RX5mpvWJvxYX0ogsNKlDAfRpnu/MPFsEK9yS+prsUpJg==", "signatures": [{"sig": "MEUCIQDQJcxE9dEw6glMYP1C6gyH3xR6/ujclJRdTm0uUVyoWgIgNNOcxOPvtCHMGz4vlYiByA+WS/+N9mpeY0zID4l5XXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "4ff8595dee13e30270653a9725b1adce6c3d728f", "gitHead": "5ca1617016f09fe839fe391c7efa4b007f078fc3", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.3.0.tgz_1460029266861_0.09198974072933197", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "color-convert", "version": "1.3.1", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.3.1", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "c8ce797c96c62153994888ed9402959fdd2398f9", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.3.1.tgz", "integrity": "sha512-l1YT7zX9IJJHjJu6aSff8Nm4J25AZr5lwLjvIY73ku+p/A+lXdgtrQho9zISx3XroWq0Qf+g+uoGo0UuqiWSvg==", "signatures": [{"sig": "MEUCIAzY7T5HpLmZ3E/xLkX+louDubUGBdcj44n5taBE68IaAiEAqErvXcjCVH4tObMN85UiTs/AzGwfEpugAcJQXDkwdwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "c8ce797c96c62153994888ed9402959fdd2398f9", "gitHead": "110333e817c0f3170e2b27d4764d80efb3f1103d", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.3.1.tgz_1460064107071_0.10304746846668422", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.0": {"name": "color-convert", "version": "1.4.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.4.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "4ad8f531c31af5d8cbc5a4af2bb6000891d398e1", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.4.0.tgz", "integrity": "sha512-RLRCieEzw8ZD8Opbrszf/4pjrCCPEi2ASteXOUKl1vz3pOU+Oi01dvxHzKBdsbeiEQPJ0YVqo7X0DxPYwnffPA==", "signatures": [{"sig": "MEUCIFMWBAJOUo1tWZW7TsbDNOO+xtDRp9HrPd2jceuLJFT3AiEAgbibFwkDGFuyM949h+J6iqckWwQ39Z/D+przpiwpPNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "4ad8f531c31af5d8cbc5a4af2bb6000891d398e1", "gitHead": "868bb4cce6cdb2cc3c0231451614793047a83b7e", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.4.0.tgz_1470817042687_0.8616472999565303", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.0": {"name": "color-convert", "version": "1.5.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.5.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "7a2b4efb4488df85bca6443cb038b7100fbe7de1", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.5.0.tgz", "integrity": "sha512-WGgcqJeah/K0HNtonAkRfQBzuVk/KiHVwEJeEqLyjXWEl2O2LWV6ZYi79qLWegNCwmfNNe9TxOvLSj8JeSjoVg==", "signatures": [{"sig": "MEYCIQDoMkKTx831sSZIq7S73yv8TLdsn7VJJt/ewCQ+3K/3FQIhAIzp5J0aXP238jK+QDhaZGm29S0GOmMa154XtjaZgzQA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "7a2b4efb4488df85bca6443cb038b7100fbe7de1", "gitHead": "427cbb70540bb9e5b3e94aa3bb9f97957ee5fbc0", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.5.0.tgz_1472948780177_0.9181577879935503", "host": "packages-16-east.internal.npmjs.com"}}, "1.6.0": {"name": "color-convert", "version": "1.6.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.6.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "7592755faf53938a05b1ea8e5374cab77d6dd190", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.6.0.tgz", "integrity": "sha512-lXbsfiGUUBQymocTqUUazU5bNE3WdOPuvQdLG1sjniCoBlw6RQ9WAeW/jAR+LyDuYAUTdDmPDmrP7272o5WmWA==", "signatures": [{"sig": "MEYCIQCepgDGyq90eptFhGKhArhQZtgEQuntO6SnIcFHlFy5OAIhAIrJ9+fnpPARX0y5jaBgAd1IMz4fQsrMozy+0Wo42TJ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "7592755faf53938a05b1ea8e5374cab77d6dd190", "gitHead": "12f128436b9e3d6833f74b28f544d4b5ca3a4c21", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"color-name": "^1.1.1"}, "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.6.0.tgz_1477961042575_0.9619914321228862", "host": "packages-18-east.internal.npmjs.com"}}, "1.7.0": {"name": "color-convert", "version": "1.7.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.7.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "473bcddfa54b76a77a3d435aceccfbf3d99cbbb0", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.7.0.tgz", "integrity": "sha512-3ZtldGg6e5F9CAqFGTkXga58z1IapbvUNmOM+j0CFquGWHoHft9Jv4c+3yOa3nV+ACfZsJG81x56o0AxGwYhBA==", "signatures": [{"sig": "MEYCIQCVOD/NkWAxj066PQ6yh+TW0sYEpgiYLT3vrZfQZW7tswIhAOWMhmUY522p4hcQQbjjRCyXk6iNnR9GcQH1pVNWLpJN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "473bcddfa54b76a77a3d435aceccfbf3d99cbbb0", "gitHead": "b2bc9d9fdb64c47c87f136606face84798ecf742", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"color-name": "^1.1.1"}, "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.7.0.tgz_1478811862978_0.5804389235563576", "host": "packages-18-east.internal.npmjs.com"}}, "1.8.0": {"name": "color-convert", "version": "1.8.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.8.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "123bc83c8194677fd508eeba0e0a1a5169ea424b", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.8.0.tgz", "integrity": "sha512-buL3Zh2W65vgH+5u0aOx5zrgkHjFIXdk39bL7VGDKkjc7wHR6MFQGlCCAIDhwb27SJu6jQ73DOCn8N8K+7SrYw==", "signatures": [{"sig": "MEUCIBgG2UxgbR0yWePqcU73J3tsruh95f2iHMCjeZ7y8JTKAiEAoqe0IR23Zpo92Wm4WJpEE0WdusQribVlYoBeg7r+G6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "123bc83c8194677fd508eeba0e0a1a5169ea424b", "gitHead": "8a3941cd848d301bcb6638ba29a0f54e142fd8b2", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "This version uses faulty channel labels; please upgrade to 1.8.2", "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"color-name": "^1.1.1"}, "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.8.0.tgz_1479256504457_0.2847395793069154", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.1": {"name": "color-convert", "version": "1.8.1", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.8.1", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "840bd3844b690aeb7b9f6a59f70f940267e6bb94", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.8.1.tgz", "integrity": "sha512-mu3TGAJHjRwNH5PeTUD0RF8w7trNojB/u6VQVE1OGYk2TgaQdYXB9S5nUUM2JNy0Z1CpXVjYR+GJVFgf9L0XOg==", "signatures": [{"sig": "MEYCIQDvujcst3lJ7pCJP4PT6Un6Ci6lK/cj94pZu7F9Fq0/CAIhAI1XXvymxcAe8gzgQ+u1D1R9I4wtUq9MhBi6A1NbSpgi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "840bd3844b690aeb7b9f6a59f70f940267e6bb94", "gitHead": "7df220aacc58f869adb4f5fdfb6d38dae82ab7e5", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "This version uses faulty channel labels; please upgrade to 1.8.2", "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"color-name": "^1.1.1"}, "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.8.1.tgz_1479257672237_0.7131252812687308", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.2": {"name": "color-convert", "version": "1.8.2", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.8.2", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "be868184d7c8631766d54e7078e2672d7c7e3339", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.8.2.tgz", "integrity": "sha512-EhZ1cQS1oriLET/iw58/Px7pZhsUMCBMWnUeDGSGlUJMimGf84z4yO8xZrU0dOeqfiaNI1JQV4WXvf+dAx+lCg==", "signatures": [{"sig": "MEUCIQD10bxDsLjB/H38ertu4fD3ITzAUyL6BX9RZny4+wOEbQIgUHpYyDAzz+DyvnBuyHHVXuBkmwK3pK141JOSVh8itpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "be868184d7c8631766d54e7078e2672d7c7e3339", "gitHead": "75c08d063bda2b9906d9302e674bedce331230e5", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"color-name": "^1.1.1"}, "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.8.2.tgz_1479258757365_0.8348635451402515", "host": "packages-18-east.internal.npmjs.com"}}, "1.9.0": {"name": "color-convert", "version": "1.9.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.9.0", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/qix-/color-convert#readme", "bugs": {"url": "https://github.com/qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "1accf97dd739b983bf994d56fec8f95853641b7a", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.0.tgz", "integrity": "sha512-cBdgwBveAUUexnimWkdqoTDizLaNhyWPRTvsNQI7eg2k5Y8sqQzymwc2V0qGhX0QdsPS9pqR5nOxEiMAE7SmHQ==", "signatures": [{"sig": "MEYCIQCuTA7dVW06d96ee8qobdPBdAPQsETOFvaaWXJ92sxeLwIhAJEuFmHgTD3ozRGQFwWeKWv/+hfai2MYWmGlyhWBIZXn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "_shasum": "1accf97dd739b983bf994d56fec8f95853641b7a", "gitHead": "f8b2cf64544c551f22b74947d511f0e97c4e6ef1", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/qix-/color-convert.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"color-name": "^1.1.1"}, "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.9.0.tgz_1484684133840_0.4650497359689325", "host": "packages-18-east.internal.npmjs.com"}}, "1.9.1": {"name": "color-convert", "version": "1.9.1", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.9.1", "maintainers": [{"name": "harth", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/Qix-/color-convert#readme", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "c1261107aeb2f294ebffec9ed9ecad529a6097ed", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.1.tgz", "integrity": "sha512-mjGanIiwQJskCC18rPR6OmrZ6fm2Lc7PeGFYwCmy5J34wC6F1PzdGL6xeMfmgicfYcNLGuVFA3WzXtIDCQSZxQ==", "signatures": [{"sig": "MEUCIQD8BN+RAydhu+cK9Qm7y7oEnzL5jpq9QXK9mOZhTD5KcAIgMuzKw8yoExfH3GWx86x9/hHcH4FoS1VSs9YlmYMMy0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "gitHead": "1df58eff59b30d075513860cf69f8aec4620140d", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Qix-/color-convert.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "9.0.0", "dependencies": {"color-name": "^1.1.1"}, "devDependencies": {"xo": "^0.11.2", "chalk": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert-1.9.1.tgz_1510178336068_0.0703919562511146", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "color-convert", "version": "1.9.2", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.9.2", "maintainers": [{"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/Qix-/color-convert#readme", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "49881b8fba67df12a96bdf3f56c0aab9e7913147", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.2.tgz", "fileCount": 7, "integrity": "sha512-3NUJZdhMhcdPn8vJ9v2UQJoH0qqoGUkYTgFEPZaPjEtwmmKUfNV46zZmgB2M5M4DCEQHMaCfWHCxiBflLm04Tg==", "signatures": [{"sig": "MEYCIQDt691if4a3Vow3ll37ecO72QpwY0jfhaQOq4Po/tM8jAIhAOkIeDyzdRxP/MftS0Hi/BGhkbt1LDUmhQIyM+dYsBf5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHyBFCRA9TVsSAnZWagAAEt4P+QG7yaLhjk4RsjyN2ql+\nIFw0QwJ7kZnkHrFpl787XEOamf/Co/EoY1fj0ZV848wnGYu44RIk55Xp3ZFc\nWisi5QtGjOKyX1RueJ8Y1tYbEsQ/8jeAUEAPA+hj7P4ZPTJIkqkz3gC4qbz0\nsVbdA0h0kYzKw9cHOUQIBmPQFxuRPRoJcImkSK+YaFSatKDFWbMo4D5gEvCV\nY6xfCREm4cFD1Ql8jV89PQc93+qTAWdODCtsPPDRTmmspamzalIBmdOOMiEw\n9SC3YLOJK+8OytNR7fA3JPcxn1RFDpFNNuRF9DRjfYns2cwFd8LGoeZvvOYK\nHadq72Ox3x4etT+eDqrjM+IJxxjYPJSF3CWC8p8BZab1ZXk9K8h6VJm/o7/d\nS3JLtyUQREfGUmUonH5KUuOYXO7Z3RLu4G1amiqPAn5BvFUqHCGuf7qZ58vL\nZ487Lv9Wjwq523KpuYaKGJhX0Wz61e0oT0VXCbYLY9/Ih9pZYrlj2vhOZewF\nXm+vhEatUdHmhDG5ezMsagc0mm46h2TB7A6O66RFcv9K7PHal9XMxpqQslg1\nyOD23N/l7BTdf//A5DOd1qKNOz/D2GOD4zjSy31JSHyAPjPC63k1h2Tb3bcz\ntjKZANSBfc4QVGMV91RT1tn0g+xl+aKGFEaJNO35HBxmEDMfGrIdaE4zAG5X\nrWJM\r\n=KzXz\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "gitHead": "2c3f959f055bda471749a3d9d27f6ff9218c875b", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Qix-/color-convert.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"color-name": "1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "0.11.2", "chalk": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert_1.9.2_1528766532123_0.40888513473984656", "host": "s3://npm-registry-packages"}}, "1.9.3": {"name": "color-convert", "version": "1.9.3", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@1.9.3", "maintainers": [{"name": "moox", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/Qix-/color-convert#readme", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "bb71850690e1f136567de629d2d5471deda4c1e8", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "fileCount": 7, "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "signatures": [{"sig": "MEQCIH/QbhCJljTbNBG6f8NzClQbAt6MsH7r5AEYAXslnfQKAiA2rS3w32jDxFCmupdO6AhvIfLX1FkGRXGGT/8Pwg8tIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhN53CRA9TVsSAnZWagAAM64QAKN/CRQ/i6v2JtWVkAob\ntboJSXsnqa/T2ozTyI5UngXYdqk+33XF1EOeTCQ4Heay0zYk8MtMDk8GCIO0\naKHGzhgC5lKYXYJ+6AE/0C/047LdgTUqEbYriwTkYTa1FyozgLMghdkBLklF\nADEZ3elhqjuCJNhe/CIHfAVBrNt/b7t8O6zhQY/n53wA6tU9N6gJq2R5njML\n/bKF5JxEX6RITFxC5jqUQecyDf1uYt0B/0IJQEylsdN8JqNrL6IzrXjLI2Xp\n37zH5P17Adl2yqIuwIoqmjDxuTFcAjA02WNPj4D0h88h7C5kmVvS5x3FC+D4\nYVZAwIKCqeFtYOtTMvoLhXd3Zgc7OSEg+KLi9UR9CxQrP42/28Bi0guZGCua\nOj/Tr660U4+sKr5Y9tRdpE8J55pm/wELPntR9TwVj8qnq8+C9efo8hDxMGT9\nJ+GWrrGAtEHP0ffs4W3O/yn5QfWXUmyIueNg7CjFgYG2pgMwSKOVgKsVc+iQ\nP6/bDsk3exESageTtRxA3gIK279QIRtqY+4M2HV7lcL3l9nPhiD841o2mep4\nVIcNiR3SD3MeAr5DBZqJKNd1qxDLRAAnCTNLfrQBx5hHtXgm9wDKrZNTdOxF\npUvIxhDTbkKIAmfSAI4fpZxwsSP10PRMHdtRRmDahZU9Uyj5qY+KEDhOnotq\nKF7e\r\n=S0I7\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "gitHead": "99dc5da127d3d17d0ff8d13a995fd2d6aab404aa", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Qix-/color-convert.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"color-name": "1.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "0.11.2", "chalk": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert_1.9.3_1535434358933_0.039520734999560325", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "color-convert", "version": "2.0.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@2.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/Qix-/color-convert#readme", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "9851ac61cc0d3898a8a3088650d5bf447bf69d97", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-hzTicsCJIHdxih9+2aLR1tNGZX5qSJGRHDPVwSY26tVrEf55XNajLOBWz2UuWSIergszA09/bqnOiHyqx9fxQg==", "signatures": [{"sig": "MEYCIQDSYsea5tjUUN6CN271Tn/D2IOGWYHHJJCGtrbWnOACdwIhAJErU2Es4JQ0XuhMu3/CBXschI6x1/3ysWXsl6Xs1wuc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcUyzoCRA9TVsSAnZWagAAuUMP/13rMhUhoF9/xcfTJijb\nHmpq0WhB95p8UrqDE5DML/IhmIAtOO2YTMDmDiFi9ruwfC90rjt1JyI14G0G\nnmiyIqi3y+TPBWh4XhKval7Fo9N/Ar5hGTQNXCI0uLmjiSjMl+CZc5UmvOhv\nWq421A1Qkh31nFfbLiMC6bL2BFN3TUIcHQAFuxrUQcb/gIRZPnbAjlfSFSgx\n1Pv+67QEOUKKKCAlB0DEd6UAA4Qjhjca6gBfl9eU+2ZvpbXPVxuvRDVchRYM\nFfbk4jUCPgLu503a3gi7Sikio1Q8jZRiCxvQyHH7NiIi/Mw5qoQxvLdYvW9x\nBtMXMT9k+V89XkLBQ8edfpxyQ8a16ltkYowtFSN7DJnglt6M3j3pZtyjscNI\n7OkkC4AP/HrRz5HBPCwNSmczVyRYIqhLgwFONOCkJQdaS6k9db4X+dvVIfzE\ntO0SBdklnpgoqLG++OrOack1+ZbouPeg1DkNMayva0uRfOQq2ps4jSOcGHiY\npq/iIR9GrtUSfgpMJC/rIZFL2GSScFbYIiOyS20+W4qL2DbJkifrOIcMvy6w\nAuou8WmOHeblEJpzYl7CCZW8GRW2UIkM2j1WpQjyYOxUEQFBSGRb0EHpqrAS\n09z2ZbuWC0JZ5xF4U4uwYg1PxWIUoopVCRVMJ+HuG1fMRNFwXASGfcNKKt6y\nAvtb\r\n=+pms\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=7.0.0"}, "gitHead": "66068bffd44c0303ab5b72a64a31f6899d13f32b", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Qix-/color-convert.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"color-name": "~1.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "chalk": "^2.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert_2.0.0_1548954856060_0.041033818467610006", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "color-convert", "version": "2.0.1", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@2.0.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/Qix-/color-convert#readme", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "dist": {"shasum": "72d3a68d598c9bdb3af2ad1e84f21d896abd4de3", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "signatures": [{"sig": "MEUCIQDawANUBU4JyKUu4FIXFy8S7MRHb4bNg7kk0ifG6KLYaQIgJ3p3N1TtoTXNQlvECo4whQoW3oot3kbzgAOutQTZTrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWw8hCRA9TVsSAnZWagAAc60P/14ziCngY6rmLxrmBZwI\n/u9S0nyyyBwBtCMRDYg9QnZizmcuDmeoBb1ptCNHAqPzW9O5xS4jtc59h+Cz\nFJgsFWNdSxQm7VraaBfgRvlDD+epDc8TyHRarG3wZ3GwK/xUPDkY8exK7JHl\nTUqwy3rOSJlxlfY5KYqvcAw9jUW4Ki5NA+1mvWpill/T6GwwiU9/mxv3Ul6f\nSuid8MxGZxBKVy5qMASbJ452cer5QyPgbty57ZdpAqWiIK0s6iaESJJCjfxI\nyiO7lWZMpgRfDihzSCxBhEb3hdO+7dia5M/RJm2L0jkW1rO6oTtvdOiI82AN\nRgRMT53u+ZVgf3gXoVQHgHmb2aikycXVSnwRv4Z6JIrToxbxbYj6AmNgSb39\n4Royuzzlt17SIKTkJ0BF3ZKeumnQyeK2aIgDahRzT1xeVvCcpMLqsgpu6FKP\nsMDk9f7+nKvaQf13D7xh9tEeMB3wd5ab2FAYpk7Gk47jtOQTG5FGyGwIKCyn\nxaxv85/KDAITn0AQQ2dyPuUshoTQVPUCnaGlN2XNtnLQ9QjPXIkTfdYlgry4\n6ysJCtzKLrjaz1zXpvBxQmexcjpBozt7raOO76XqDc9rQY8hirMMT+kFn4wy\nBK0FSk4jJNPqb9GduVnyMGR3eq5UxrDZbYdowb+DpDBEih/DUimhi0+5uro1\nqeOh\r\n=9TuD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=7.0.0"}, "gitHead": "e1cb7846258e1d7aa25873814684d4fbbbca53ed", "scripts": {"test": "node test/basic.js", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Qix-/color-convert.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"color-name": "~1.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "chalk": "^2.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert_2.0.1_1566248736467_0.5739359021350308", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "color-convert", "version": "3.0.0", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@3.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Qix-/color-convert#readme", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0, "@typescript-eslint/naming-convention": 0, "unicorn/prefer-exponentiation-operator": 0}}, "dist": {"shasum": "f55904b7c59a8f7db3df0dba2df92c58fb2554fa", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-m65xBZ5vGd9Mxtaw/QSGoC5UykQoIksUfcQZ1w3kzl12jM3u1loRAkgXXSkbXp0+/vEslCW4BCajnXLZlxIstA==", "signatures": [{"sig": "MEUCIQDoy8czpZ3M4DeJkrdGXhm8f7Sa24QdXZbxKavA5D537gIgCl4+ADKFeD2swEHmlO1qHJKEUv4foHE4Y/qxJAueqmA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32344}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.6"}, "exports": "./index.js", "gitHead": "e0a2fbb4c765ea743038e0357d2090efcb5f07ef", "scripts": {"test": "xo && tsd && node test/basic.js"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Qix-/color-convert.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"color-name": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "tsd": "^0.28.1", "jimp": "^0.22.8", "chalk": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert_3.0.0_1738782064730_0.6999622000999861", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "color-convert", "version": "3.0.1", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "color-convert@3.0.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/Qix-/color-convert#readme", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0, "@typescript-eslint/naming-convention": 0, "unicorn/prefer-exponentiation-operator": 0}}, "dist": {"shasum": "c0d327fd53cb93dfac3ba60fb04f2f8069638d6f", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-5kQah2eolfQV7HCrxtsBBArPfT5dwaKYMCXeMQsdRO7ihTO/cuNLGjd50ITCDn+ZU/YbS0Go64SjP9154eopxg==", "signatures": [{"sig": "MEUCIHr/XjL/sYDNX2VxL8poY6vfKDdFOXEA+O1CVRmK9yDnAiEAiMJMQkqLwBQjQQ2U+KO/Fq0RquYuK84ag6Z3T3G0mDM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44568}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.6"}, "exports": "./index.js", "gitHead": "f53dad2f14e055e0bc7b4434ca13ec0435092172", "scripts": {"test": "xo && tsd && node test/basic.js"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Qix-/color-convert.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Plain color conversion functions", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"color-name": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "tsd": "^0.28.1", "jimp": "^0.22.8", "chalk": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/color-convert_3.0.1_1738941873602_0.0683810567928842", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "color-convert", "description": "Plain color conversion functions", "version": "3.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Qix-/color-convert.git"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.6"}, "scripts": {"test": "xo && tsd && node test/basic.js"}, "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0, "unicorn/prefer-exponentiation-operator": 0, "@typescript-eslint/naming-convention": 0}}, "devDependencies": {"chalk": "^5.2.0", "jimp": "^0.22.8", "tsd": "^0.28.1", "xo": "^0.54.2"}, "dependencies": {"color-name": "^2.0.0"}, "_id": "color-convert@3.1.0", "gitHead": "07e073af02e9a3722f6926d214ffd778af20ab03", "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "homepage": "https://github.com/Qix-/color-convert#readme", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-TVoqAq8ZDIpK5lsQY874DDnu65CSsc9vzq0wLpNQ6UMBq81GSZocVazPiBbYGzngzBOIRahpkTzCLVe2at4MfA==", "shasum": "ce16ebb832f9d7522649ed9e11bc0ccb9433a524", "tarball": "https://registry.npmjs.org/color-convert/-/color-convert-3.1.0.tgz", "fileCount": 7, "unpackedSize": 47843, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIH/K58h0ahjOaRyFVBEfbTqWBr4s2jsPsmB450bDOPY5AiEAyWgL+cUIszbcoyF7X7TgcuwlZAZpfuyFxLkWKtaCKns="}]}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/color-convert_3.1.0_1747130594347_0.5401215698756192"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-06-10T04:11:24.300Z", "modified": "2025-05-13T10:03:14.699Z", "0.1.0": "2011-06-10T04:11:24.939Z", "0.2.0": "2011-06-16T17:17:49.090Z", "0.2.1": "2011-06-23T03:04:46.838Z", "0.3.0": "2011-10-02T00:52:31.161Z", "0.3.1": "2011-10-25T19:06:25.763Z", "0.3.4": "2014-04-04T22:22:42.008Z", "0.4.0": "2014-07-15T15:22:49.129Z", "0.5.0": "2014-07-21T06:39:16.104Z", "0.5.1": "2014-10-17T06:42:27.142Z", "0.5.2": "2014-10-17T19:44:43.302Z", "0.5.3": "2015-06-02T04:46:06.747Z", "0.6.0": "2015-07-23T07:09:45.206Z", "0.7.0": "2015-12-13T00:56:16.762Z", "1.0.0": "2016-01-07T23:11:28.002Z", "1.1.0": "2016-03-30T01:02:48.608Z", "1.1.1": "2016-03-30T01:06:00.303Z", "1.1.2": "2016-03-30T01:07:09.327Z", "1.2.0": "2016-04-07T11:03:24.622Z", "1.2.1": "2016-04-07T11:05:00.372Z", "1.2.2": "2016-04-07T11:06:31.460Z", "1.3.0": "2016-04-07T11:41:07.368Z", "1.3.1": "2016-04-07T21:21:47.734Z", "1.4.0": "2016-08-10T08:17:26.486Z", "1.5.0": "2016-09-04T00:26:23.790Z", "1.6.0": "2016-11-01T00:44:04.284Z", "1.7.0": "2016-11-10T21:04:24.866Z", "1.8.0": "2016-11-16T00:35:04.685Z", "1.8.1": "2016-11-16T00:54:32.469Z", "1.8.2": "2016-11-16T01:12:39.223Z", "1.9.0": "2017-01-17T20:15:35.867Z", "1.9.1": "2017-11-08T21:58:56.163Z", "1.9.2": "2018-06-12T01:22:12.214Z", "1.9.3": "2018-08-28T05:32:39.014Z", "2.0.0": "2019-01-31T17:14:16.204Z", "2.0.1": "2019-08-19T21:05:36.605Z", "3.0.0": "2025-02-05T19:01:04.922Z", "3.0.1": "2025-02-07T15:24:33.783Z", "3.1.0": "2025-05-13T10:03:14.540Z"}, "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/Qix-/color-convert#readme", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "repository": {"type": "git", "url": "git+https://github.com/Qix-/color-convert.git"}, "description": "Plain color conversion functions", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "readme": "# color-convert\n\nColor-convert is a color conversion library for JavaScript and node.\nIt converts all ways between `rgb`, `hsl`, `hsv`, `hwb`, `cmyk`, `ansi`, `ansi16`, `hex` strings, and CSS `keyword`s (will round to closest):\n\n```js\nimport convert from 'color-convert';\n\nconvert.rgb.hsl(140, 200, 100);             // [96, 48, 59]\nconvert.keyword.rgb('blue');                // [0, 0, 255]\n\nconst rgbChannels = convert.rgb.channels;     // 3\nconst cmykChannels = convert.cmyk.channels;   // 4\nconst ansiChannels = convert.ansi16.channels; // 1\n```\n\n# Install\n\n```sh\nnpm install color-convert\n```\n\n# API\n\nSimply get the property of the _from_ and _to_ conversion that you're looking for.\n\nAll functions have a rounded and unrounded variant. By default, return values are rounded. To get the unrounded (raw) results, simply tack on `.raw` to the function.\n\nAll 'from' functions have a hidden property called `.channels` that indicates the number of channels the function expects (not including alpha).\n\n```js\nimport convert from 'color-convert';\n\n// Hex to LAB\nconvert.hex.lab('DEADBF');         // [ 76, 21, -2 ]\nconvert.hex.lab.raw('DEADBF');     // [ 75.56213190997677, 20.653827952644754, -2.290532499330533 ]\n\n// RGB to CMYK\nconvert.rgb.cmyk(167, 255, 4);     // [ 35, 0, 98, 0 ]\nconvert.rgb.cmyk.raw(167, 255, 4); // [ 34.***************, 0, 98.**************, 0 ]\n```\n\n### Arrays\nAll functions that accept multiple arguments also support passing an array.\n\nNote that this does **not** apply to functions that convert from a color that only requires one value (e.g. `keyword`, `ansi256`, `hex`, etc.)\n\n```js\nimport convert from 'color-convert';\n\nconvert.rgb.hex(123, 45, 67);      // '7B2D43'\nconvert.rgb.hex([123, 45, 67]);    // '7B2D43'\n```\n\n## Routing\n\nConversions that don't have an _explicitly_ defined conversion (in [conversions.js](conversions.js)), but can be converted by means of sub-conversions (e.g. XYZ -> **RGB** -> CMYK), are automatically routed together. This allows just about any color model supported by `color-convert` to be converted to any other model, so long as a sub-conversion path exists. This is also true for conversions requiring more than one step in between (e.g. LCH -> **LAB** -> **XYZ** -> **RGB** -> Hex).\n\nKeep in mind that extensive conversions _may_ result in a loss of precision, and exist only to be complete. For a list of \"direct\" (single-step) conversions, see [conversions.js](conversions.js).\n\n## Color Space Scales\nConversions rely on an agreed upon 'full-scale' value for each of the channels. Listed here are those values for the most common color spaces\n\n### rgb\nchannel | full-scale value\n---|---\nr | 255\ng | 255\nb | 255\n\n### hsl\nchannel | full-scale value\n---|---\nh | 360\ns | 100\nl | 100\n\n### hsv\nchannel | full-scale value\n---|---\nh | 360\ns | 100\nv | 100\n\n### hwb\nchannel | full-scale value\n---|---\nh | 360\nw | 100\nb | 100\n\n### xyz\nchannel | full-scale value\n---|---\nx | 94\ny | 99\nz | 108\n\n### lab\nchannel | full-scale value\n---|---\nl | 100\na | -86, 98\nb | -108, 94\n\n### lch\nchannel | full-scale value\n---|---\nl | 100\nc | 133\nh | 360\n\n### oklab\nchannel | full-scale value\n---|---\nl | 100\na | -23, 28\nb | -31, 20\n\n### oklch\nchannel | full-scale value\n---|---\nl | 100\nc | 32\nh | 360\n\n### cmyk\nchannel | full-scale value\n---|---\nc | 100\nm | 100\ny | 100\nk | 100\n\n### hex\nchannel | full-scale value\n---|---\nhex | ```0xffffff```\n\n### keyword\nchannel | value\n---|---\nname | any key from [color-name](https://github.com/colorjs/color-name/blob/master/index.js)\n\n### apple\nchannel | full-scale value\n---|---\n0 | 65535\n1 | 65535\n2 | 65535\n\n### gray\nchannel | full-scale value\n---|---\ngray | 100\n\n# Contribute\n\nIf there is a new model you would like to support, or want to add a direct conversion between two existing models, please send us a pull request.\n\n# License\nCopyright &copy; 2011-2016, Heather Arthur.\nCopyright &copy; 2016-2021, Josh Junon.\n\nLicensed under the [MIT License](LICENSE).\n", "readmeFilename": "README.md", "users": {"285858315": true, "tg-z": true, "zeke": true, "leesei": true, "ryanve": true, "sawa-zen": true, "leizongmin": true, "flumpus-dev": true, "natalitique": true, "smutnyleszek": true, "stevomccormack": true}}