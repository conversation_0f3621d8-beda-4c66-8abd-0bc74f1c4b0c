#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
阿里云百炼API测试脚本
用于诊断知识库检索服务的问题
"""

from http import HTTPStatus
from dashscope import Application

def test_ali_api():
    """测试阿里云百炼API配置"""
    
    # API配置
    api_key = "sk-8a9d2ac53b5448c5856c65f51872fd05"
    knowledge_app_id = "fd00ab6eaf7c47a6a9ab1d651b3be0b1"  # 知识库检索
    online_search_app_id = "ff034050df7a4dc39e491c6307d3e5b3"  # 联网搜索
    
    print("=" * 60)
    print("阿里云百炼API测试")
    print("=" * 60)
    
    # 测试问题
    test_questions = [
        "你好",
        "什么是工伤认定？",
        "如何办理营业执照？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 测试 {i}: {question}")
        print("-" * 40)
        
        # 测试知识库检索
        print("📚 测试知识库检索模式...")
        try:
            response = Application.call(
                api_key=api_key,
                app_id=knowledge_app_id,
                prompt=question
            )
            
            print(f"状态码: {response.status_code}")
            print(f"请求ID: {response.request_id}")
            
            if response.status_code == HTTPStatus.OK:
                print("✅ 知识库检索成功")
                print(f"回答: {response.output.text[:100]}...")
            else:
                print("❌ 知识库检索失败")
                print(f"错误信息: {response.message}")
                
        except Exception as e:
            print(f"❌ 知识库检索异常: {e}")
        
        # 测试联网搜索
        print("\n🌐 测试联网搜索模式...")
        try:
            response = Application.call(
                api_key=api_key,
                app_id=online_search_app_id,
                prompt=question
            )
            
            print(f"状态码: {response.status_code}")
            print(f"请求ID: {response.request_id}")
            
            if response.status_code == HTTPStatus.OK:
                print("✅ 联网搜索成功")
                print(f"回答: {response.output.text[:100]}...")
            else:
                print("❌ 联网搜索失败")
                print(f"错误信息: {response.message}")
                
        except Exception as e:
            print(f"❌ 联网搜索异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_ali_api()
