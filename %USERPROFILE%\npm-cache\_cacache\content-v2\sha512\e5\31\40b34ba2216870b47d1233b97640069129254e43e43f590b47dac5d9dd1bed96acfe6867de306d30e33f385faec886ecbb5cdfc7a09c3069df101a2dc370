{"_id": "dunder-proto", "_rev": "1-8990e995311508d36bd7745acfe3fa2d", "name": "dunder-proto", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "dunder-proto", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dunder-proto@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/dunder-proto#readme", "bugs": {"url": "https://github.com/es-shims/dunder-proto/issues"}, "dist": {"shasum": "c2fce098b3c8f8899554905f4377b6d85dabaa80", "tarball": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.0.tgz", "fileCount": 15, "integrity": "sha512-9+Sj30DIu+4KvHqMfLUGLFYL2PkURSYMVXJyXe92nFRvlYq5hBjLEhblKB+vkd/WVlUYMWigiY07T91Fkk0+4A==", "signatures": [{"sig": "MEUCIQDTqu4CkBBEmXsTtJaQO0tADQ6ncDabT1Bx9wrvtQK3ngIgYA6rzcLrx5A9a/TWg9mt+JnuuhLf8XpmB0R4QLDd6l4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11759}, "main": false, "engines": {"node": ">= 0.4"}, "exports": {"./get": "./get.js", "./set": "./set.js", "./package.json": "./package.json"}, "gitHead": "7aaad8673f95c39e5c6c7c0a58e8b99f79471a08", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/es-shims/dunder-proto.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "If available, the `Object.prototype.__proto__` accessor and mutator, call-bound", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "dependencies": {"gopd": "^1.2.0", "es-errors": "^1.3.0", "call-bind-apply-helpers": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.2", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/dunder-proto_1.0.0_1733502220694_0.358216166786711", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "dunder-proto", "version": "1.0.1", "description": "If available, the `Object.prototype.__proto__` accessor and mutator, call-bound", "main": false, "exports": {"./get": "./get.js", "./set": "./set.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/dunder-proto.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/es-shims/dunder-proto/issues"}, "homepage": "https://github.com/es-shims/dunder-proto#readme", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "testling": {"files": "test/index.js"}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "dunder-proto@1.0.1", "gitHead": "c6f4b69ab80c495777870f6a5f6a22db16ecaecb", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "shasum": "d7ae667e1dc83482f8b70fd0f6eefc50da30f58a", "tarball": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "fileCount": 15, "unpackedSize": 13003, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC89R4Zly8NOp3Gy6xo36Mk8em8hbx1EzTkhis6wJm2CgIgY0uA2ka0xbLQxk0WSw2x5Y5Hpw3uIduKLrlXCkYAgCg="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/dunder-proto_1.0.1_1734401566883_0.3382373483141208"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-06T16:23:40.693Z", "modified": "2024-12-17T02:12:47.238Z", "1.0.0": "2024-12-06T16:23:40.899Z", "1.0.1": "2024-12-17T02:12:47.047Z"}, "bugs": {"url": "https://github.com/es-shims/dunder-proto/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/es-shims/dunder-proto#readme", "repository": {"type": "git", "url": "git+https://github.com/es-shims/dunder-proto.git"}, "description": "If available, the `Object.prototype.__proto__` accessor and mutator, call-bound", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# dunder-proto <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nIf available, the `Object.prototype.__proto__` accessor and mutator, call-bound.\n\n## Getting started\n\n```sh\nnpm install --save dunder-proto\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst getDunder = require('dunder-proto/get');\nconst setDunder = require('dunder-proto/set');\n\nconst obj = {};\n\nassert.equal('toString' in obj, true);\nassert.equal(getDunder(obj), Object.prototype);\n\nsetDunder(obj, null);\n\nassert.equal('toString' in obj, false);\nassert.equal(getDunder(obj), null);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/dunder-proto\n[npm-version-svg]: https://versionbadg.es/es-shims/dunder-proto.svg\n[deps-svg]: https://david-dm.org/es-shims/dunder-proto.svg\n[deps-url]: https://david-dm.org/es-shims/dunder-proto\n[dev-deps-svg]: https://david-dm.org/es-shims/dunder-proto/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/dunder-proto#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/dunder-proto.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/dunder-proto.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/dunder-proto.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=dunder-proto\n[codecov-image]: https://codecov.io/gh/es-shims/dunder-proto/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/es-shims/dunder-proto/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/es-shims/dunder-proto\n[actions-url]: https://github.com/es-shims/dunder-proto/actions\n", "readmeFilename": "README.md"}