{"_id": "http-proxy", "_rev": "454-aca4452a92fd5f04275fdebed6db2fae", "name": "http-proxy", "dist-tags": {"latest": "1.18.1"}, "versions": {"0.5.9": {"name": "http-proxy", "version": "0.5.9", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.5.9", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "957103fa0515e475f99a2b4c5bfe3507d513a81e", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.5.9.tgz", "integrity": "sha512-n5KCig61lZPicNjzyoWZXa5dc9gYFNCu/s+2YToMiPCmOeZ8PqAqB9Eb6q0cG/J8qgY6aMDPilyWhxxfXo4PQQ==", "signatures": [{"sig": "MEQCIAifQSRuOI4t0Eu2mgItNXFFETdy4UxEFuuuuDCqr1ErAiBnrV2A1t5iWrSqDx+MW6loSVYGw3j8qdf9MOTtnTj7Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.6", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"colors": ">= 0.x.x", "request": ">= 1.9.x", "optimist": ">= 0.1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">= 0.5.x", "docco": ">= 0.3.x", "socket.io": ">= 0.6.x"}, "_engineSupported": true}, "0.5.10": {"name": "http-proxy", "version": "0.5.10", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.5.10", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "acd2b9126569dea265fc01bcaca1b2293232067b", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.5.10.tgz", "integrity": "sha512-Oqz7cSxNxtT+WKlqYc970hpHTQVqrLx3ph4OYDFQ9gtzSU9PULIuqD+b2zcAclhwcPFGG0PMUgKCu56Um47Hjg==", "signatures": [{"sig": "MEUCIDohRXQvXeOyiCVIqpl3QGvjYNi/gTbYeMTzMuOk5U0HAiEAoY6VDwEYw6RHkwQ3YvIFfN/8eS1P4DYFAboquRhr4Pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"colors": ">= 0.x.x", "request": ">= 1.9.x", "optimist": ">= 0.1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">= 0.5.x", "docco": ">= 0.3.x", "socket.io": ">= 0.6.x"}, "_engineSupported": true}, "0.5.11": {"name": "http-proxy", "version": "0.5.11", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.5.11", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "f58f2572765d06c71749b09275b1ba167ffabdf2", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.5.11.tgz", "integrity": "sha512-9kC4KT1iX+L1erkGVhbepcsycV1OfKp/u3WNdDuy9B+kEsYKZ1DqUgilx2RBjfkTHcJ72vCqSJ6KL2v72KJeKw==", "signatures": [{"sig": "MEUCIQD3Zl6ffGzqjXfvNtLPWVtE8bjg0a4mC4YImFmvh6+2WwIgU3jIpPNFewYXzJtLewou3wKHWF+joIZ4svTnuVjtGBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.13", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/http-proxy/0.5.11/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.6.0": {"name": "http-proxy", "version": "0.6.0", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.6.0", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "4fbcdc84d01f20c2531f375c437ec619c9e1012c", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.6.0.tgz", "integrity": "sha512-FSlIoeYA3IsnVv7rmSYG/hY/iHBZ4wpaH4auCCYJLqaaTQ6sn5aaRw+Aa6MyimkqTzdQoyWhq+m5Bl17eMzwgg==", "signatures": [{"sig": "MEQCIGNBvh1UmFC9KvSkNJcOaLdxa6hzvjDzM0Lu3Vp5GbvCAiB8o9J9zgRKuQKPR//fL+pyDH34J4BilyQoe7I7v4gCZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.13", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/home/<USER>/.npm/http-proxy/0.6.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.6.1": {"name": "http-proxy", "version": "0.6.1", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.6.1", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "0e786540c438fa139781d1b0be3521c3d7c6e728", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.6.1.tgz", "integrity": "sha512-UXjaTqOq1MSnicnQotJ4fa1xRueibefahEVOoiCN/JSrThOH6ow+1wsqzGfBIoUSpq5YR4QcjP30ZfSYy68cdg==", "signatures": [{"sig": "MEYCIQCtp926WMtIwDEKHFx9kJlJUEYFv6mirvkkDuGzX+JumwIhAMbcyRN90W6OKfWAzXig12YBgF8Enz1brsvGyr5bf2qb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.13", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/home/<USER>/.npm/http-proxy/0.6.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.7.0": {"name": "http-proxy", "version": "0.7.0", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.7.0", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "36c843818cdab7052f2f93aeed778e0f3cde5ada", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.7.0.tgz", "integrity": "sha512-IGKyRAAEiV/+borLwO8939QBu9Nwly7Le7zQLiWNHctq2FN37olpqBTq1b+KnOr9TBrYgfzdW7swNiQ/Wl2iWg==", "signatures": [{"sig": "MEUCIQCFIcEfN6dtydrp/W00AbRhlX+o+Ev+ig6OGh2H88v09wIgdS2ScpBMy8Nnv61leBnKX1sPZgaq/tTYiK3gCucQOHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/http-proxy/0.7.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.6.2": {"name": "http-proxy", "version": "0.6.2", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.6.2", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "5114c56b4cf6dbe33094d6f0bbcb79d757210708", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.6.2.tgz", "integrity": "sha512-5g3rFD8A9OVExyxM/vyWGdouGctW4hUbifkh2yBIB7vWQKLlTuMg+f6zqE5V2ccny2kqinnBOGiD0HU7S6LWFA==", "signatures": [{"sig": "MEUCIQCpqKBem3Ok2+svY7T8/Gvnh8+rnRyacIW9FFuPcZynUgIgQCQZwLlDTvzq16G0yAaH1J34zTZkV72iWuA638o8nAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.13", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/home/<USER>/.npm/http-proxy/0.6.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.6.4": {"name": "http-proxy", "version": "0.6.4", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.6.4", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "1301de97d023eadbf7bdda81e1dc7efb5cedf4c5", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.6.4.tgz", "integrity": "sha512-NzHZArBp3QLIJxq2sZpajV9dWndAJo21b6AEjPWf8r1yNu/6zDhCc5EIQVUsAPNMHGJ26Pn9i9dB22/Ll873Hg==", "signatures": [{"sig": "MEUCIEcjivDHcwTEuojYa1BKBiaDKJo+iQ/oF6jm/cuvWVb/AiEA7ZJThbzZzT0YFnAH+AmF/tZPZiQQh/J4Uad7QReL0mM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/http-proxy/0.6.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.6.5": {"name": "http-proxy", "version": "0.6.5", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.6.5", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "1b17209ee173b71fd74961e23a1f6f369978077f", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.6.5.tgz", "integrity": "sha512-8jAeIGdQbNG5voWhV8KNzHFYK5MhoVTSS5yu/mxWvYDdmys9YL7jPLRzEFJlEQeTmf0XbCPlPQ4TzHZqEu28XQ==", "signatures": [{"sig": "MEUCIQDow5KBTxm005DuIug9DAMofE6PxBK433z0AYwixEy55QIgfv3GsmUHkuGPhOUWD3F+/9n8qGVwE5bkyEgkVXEq2cs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/http-proxy/0.6.5/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.6.6": {"name": "http-proxy", "version": "0.6.6", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.6.6", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "5a9cdbb02fc3cb740f2e511497da5e9e2b3ac469", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.6.6.tgz", "integrity": "sha512-EqgZZUy+icdrnnGHoQ74bjxgaA5mMkqxqVTX1ZVs8bbRsG1II82wOmKodih/ZU+OyFMS3Jqh8mo+V7/+lnAo3w==", "signatures": [{"sig": "MEUCIAaU5B+XA+70BX6hD9R+FdTgXICMkabeJXeORWoj+P6SAiEA8ZfHDqdTTzvf3kkINVAt6rWxscVwiPy5IAu47F8t0ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "vows test/*-test.js --spec && vows test/*-test.js --spec --https"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/http-proxy/0.6.6/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.7.2": {"name": "http-proxy", "version": "0.7.2", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.7.2", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "4e2e473b2c8875313101fbc657b2706e064525dd", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.7.2.tgz", "integrity": "sha512-wjlSTkuVNga9uAS7w6VJ5D/oi6lwyt1xKwmPOp2pIQHRm0F/7owk1PThOCpg+DorQ1qpSnieqKCkgzHG//IgCw==", "signatures": [{"sig": "MEUCID1tFvzlFNWGVys3NnQHqr2ienIcgAMdnEk0Xb6rkaJlAiEAymxxrYP1BxQPX4CMEffdcL23e+b9wwVMY07iXbOky+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/http-proxy/0.7.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.7.3": {"name": "http-proxy", "version": "0.7.3", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.7.3", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "4f4bc8bbd08a206c6d822ba7a3dc582f4e8e7b32", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.7.3.tgz", "integrity": "sha512-Q3lTQKy1nuRMIMAXUerD0If1p6oRWdaXxAtgWtElg7d4FB/FZSwgJDu9XOmIGfGy/LdtKAy10Dsgk5mx19Muyw==", "signatures": [{"sig": "MEUCIAePRegNB2y6G1hpSvgMEm4Trs+fgbhbJLqrcHUoSMZYAiEAnao2pADUZevVAkREIjFhBM6EflC24kRfpVkim0EBPI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/http-proxy/0.7.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.7.4": {"name": "http-proxy", "version": "0.7.4", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.7.4", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "385556d7e84ca1f367f8a3887f4c8263f8d8a3ca", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.7.4.tgz", "integrity": "sha512-+NJnQ7pICCY29K+n5seCbhXGEyhJxlCqgCimhgutQcMdEXB5or+vDNzBCYzNw87W4EbsFjcoUN0xkN9zHPbexg==", "signatures": [{"sig": "MEYCIQC4JFaf0sZ+7TJmQt8sBiq/2fMtRc6saZonx1X1t+sT7wIhAISQM8k9k/sVhUDYUDzSqceGmJBtRPVugMPx+d6OoK2u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.7.5": {"name": "http-proxy", "version": "0.7.5", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.7.5", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "ce69c26ccd432837548caf606ea44d2997d9d337", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.7.5.tgz", "integrity": "sha512-XAM8rhHS+Bf4LP51fCH+ML25CZPP3jW6lbbflf+Nv8x2Ydao0Xs83oCsXhuHfsjPJ4HFm5tFOp0H1JJ1hdgTQQ==", "signatures": [{"sig": "MEUCIQCEk/+qSnP9jc9RkbpLnR/IcKnIvsGbyP1O/8Wvb0teowIgaajmBLOqOEgw/ePkhf4SI/1MFuc9UbPtnL+OIs0SG8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.7.6": {"name": "http-proxy", "version": "0.7.6", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.7.6", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "7193cca1ebdf828d1582e740630b5caf816fd1e0", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.7.6.tgz", "integrity": "sha512-IMA45qkgt7UaL3fsuSBqTlXF+tvBzaJG5dUxaGCdIQBFEaS+D5K2/stCvgklKm5sOrwNoeA9m16RF+bwsbCanA==", "signatures": [{"sig": "MEUCIAuzvt9XSPTYpEMIEpiO6uZVTH/W/DC1CSJ2Q/RJ0QEhAiEA2DHmLzZ7VSMBoEz4fWRsrCDX/RdSrmU12Da/Jn/WemQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": "0.4.x || 0.5.x"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.8.0": {"name": "http-proxy", "version": "0.8.0", "keywords": ["reverse", "proxy", "http"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.0", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "b366d265688ffcaa9e3c77ead20d154ddf94489e", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.0.tgz", "integrity": "sha512-XLKF139MmIyNGWLqqSaccFb9axE/9UneH3aCAY1mzb2Z8bc9FX2O9eTvxWXex244NAVaFC6E6pT1C7VAxVTlMg==", "signatures": [{"sig": "MEUCIQCCBKVSwz7y2cibpFmzQAqC370OuCok1Q/s6n3a4hOGKAIgInB46ZDYizmaaQLo+7zGfo9umnUBfHhGeVTWgONUGyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.6.x"}, "_engineSupported": true}, "0.8.1": {"name": "http-proxy", "version": "0.8.1", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.1", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "cc4dd5793ba7bb17fa1834e6abc72a99e94bb996", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.1.tgz", "integrity": "sha512-+LsZLr8CUjOrOYDI/QUBtZY4iC2lKPE7YX+RZE43FbYrC77EX1kVBNmD6+f66qrzRu7SeV6dVof04rCY+hs5Hg==", "signatures": [{"sig": "MEUCIDsK5YWZmjIe3eEf3MxUW6YzIg+m0S1E5NvijMKl2IMCAiEAwe1H5/Ab538U+3R4AWl9Wn8/jA/QRe1g94umtc/WDmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=secure", "test-https": "vows --spec --source=secure && vows --spec --source=secure --target=secure"}, "_npmUser": {"name": "cronopio", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.6.17"}, "_engineSupported": true, "optionalDependencies": {}}, "0.8.2": {"name": "http-proxy", "version": "0.8.2", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.2", "maintainers": [{"name": "marak", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "f75a3a754b5c27bea2fddad16e9b267b325a8273", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.2.tgz", "integrity": "sha512-OVbZU2tQaM9A0wTiIVKvgt/K60b4kOcjatAK7gFtg9H4NzJ5EYIy1z+71nIGlvwqIHVEKUMM8iC7CH0+yxv3Hw==", "signatures": [{"sig": "MEUCIQCmjVW299bQ+KUQyAS5q52EooyYWLk8CxMVjnSUpsl5eAIgedfBEYozofzEBf2hmE0wfKKqtp+LtlrQT2+pxNo+wKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.1.2", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "_defaultsLoaded": true, "devDependencies": {"ws": "0.4.21", "vows": "0.6.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.9.6", "socket.io-client": "0.9.6"}, "_engineSupported": true, "optionalDependencies": {}}, "0.8.3": {"name": "http-proxy", "version": "0.8.3", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "031cb4512df4cf28f387277dfcd2a0ea4f2a5466", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.3.tgz", "integrity": "sha512-H6bHcJUINyImLiw5/FNG1Er43ZXD6eDZOWx773pgCzZFpWB/Wht7Yql1kmOZQUvbNiMTzpHTdNo+B2syLP92Gw==", "signatures": [{"sig": "MEQCIDCwbYSpYB5HEayx3o9DPMRErclm3JK7ReUP5NqwGhYzAiBGWM6V1RoNxG75tkZTFSq3dN7nt/fmigeQbUQMReR+og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "bradleymeck", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.21", "vows": "0.6.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.9.6", "socket.io-client": "0.9.6"}}, "0.8.4": {"name": "http-proxy", "version": "0.8.4", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.4", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "00075c99098041cb0336c6dbe7f13bf6144e5b23", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.4.tgz", "integrity": "sha512-YtuiY6xrKCBRSXonOOA/ULogKP4GkqBD/T52VSQy9nWlNAQ7mYHZYC8oKCm91F/BhNfQjy6NJcjFcFD17PhVkA==", "signatures": [{"sig": "MEQCIHiRhWT5CpwuLBTzyTnW5CkkcH5HULqSIxLNs9rHnNRKAiA0FdnlFg0HkRHiVuK3/kQwQeJTBihLgBC0GiGi0yVASQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.21", "vows": "0.6.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.9.6", "socket.io-client": "0.9.6"}}, "0.8.5": {"name": "http-proxy", "version": "0.8.5", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.5", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "32eee6272cabdc1ef6bc5a732196262b6b687faf", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.5.tgz", "integrity": "sha512-YmDrKN4D3WoEzpf/+CXEutZxpGn2JQ8gZOpQKpavwiYCfWnSNk+Y/iZAOKVYXIKFTabAhGPMff+uOhit1d5EVA==", "signatures": [{"sig": "MEYCIQCi3QGzTcaOILcFMt0oAWtRsaUnRaZcmbQ5uOHviguwVwIhANLyTbH/jMx1zOOf1LubvMKZsi7bP2vKez1NnvlyM8tv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.21", "vows": "0.6.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.9.6", "socket.io-client": "0.9.6"}}, "0.8.6": {"name": "http-proxy", "version": "0.8.6", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.6", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "176f54a4fee949447807c58b69fdbb9e122d1394", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.6.tgz", "integrity": "sha512-5vLof8K1MwqsTZ4DdvcK3Ns9Ly7997+5LKKrllGdwIU1+ZmHr80/m4OPm7+I/3tQzp6tQ1tjqkxKpj7lmO9Vow==", "signatures": [{"sig": "MEQCIAMx9pc0LeXU6tPzbtwlxNnAU3VWaB0RPp4Tq/ubkYrhAiAsemiE+JD/pjid2V9AoiAfdMRHvK80xdO3E7U9n+V4HA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.6.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.8.7": {"name": "http-proxy", "version": "0.8.7", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.8.7", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "a7bc538618092cd26ed191e4625933baef6de80e", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.8.7.tgz", "integrity": "sha512-v8gG5W4QwghXJL3bsD4PyzXTRoSrje1z/QFPCnGdMg0rgxo4+tbZlsWWBwFyXAxT2KAEgiTH0I3XgkwSB+hcig==", "signatures": [{"sig": "MEYCIQDnws1we2DhPCJYFW3+Rrak3mHfbwXVXLhEbCdGtgfWMAIhAMXGPCt2NbiEtt5GGDFSHmZJytZUATIgLfypKLeJzQM4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.6.x", "async": "0.1.x", "request": "1.9.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.9.0": {"name": "http-proxy", "version": "0.9.0", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.9.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "c3f3601bf0fac3d9961bea8deb30a84360c34ef5", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.9.0.tgz", "integrity": "sha512-wCM2u8b5QxC8O8LxVSdFqmAAJEfji38WghPoPhW8Jqm1OdYY3ztL2iDzyBLBkOgzMSIjJRKhW7ZJjMVlHWYyUQ==", "signatures": [{"sig": "MEQCIC2BOZyn3/QHB4ZIb3L9A6KyhWWeXUITQkn6gUZUxkXcAiAt/AqWNe2yWR9drpLzPV+OS0m4fFtZypfuF3y9ZRNHBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"utile": "~0.1.7", "colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.7.x", "async": "0.2.x", "request": "2.14.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.9.1": {"name": "http-proxy", "version": "0.9.1", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.9.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "ba074a55cb21cfde4deb359d19334a3eedda9325", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.9.1.tgz", "integrity": "sha512-mQ2uBP6IjUQ8LudrVWKs0TUc6IW2JYP8azJkrLI1f9Efwkx0zbjv+GiGTB0LPQMC9K4jCKlL738Q/Drm7K3rqA==", "signatures": [{"sig": "MEQCIDYzuzWUS7p1qWTE6aVxJ67BonEU2r71446W40QutNeUAiAd4l7+JIER3T1hWmp4WenZ3r9SpyLFpGiMqZtVxALApg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"utile": "~0.1.7", "colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.7.x", "async": "0.2.x", "request": "2.14.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.10.0": {"name": "http-proxy", "version": "0.10.0", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.10.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "a20b718d05ec40427ae61644dabdcc427ba62508", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.10.0.tgz", "integrity": "sha512-WtmrUzZPnyk79D0I34anQl2flXDHfzis+xLsYJ8PeLqdIQieI61oeycxL6F8O9gsI6pwa0XYJwc0fr6tXDjQlg==", "signatures": [{"sig": "MEUCID3WAlafYFs/SyUnedofK2NUuLy3+7LylyLhDK4WKWWhAiEAn9sXVlxWiIXpYYrO5hrm3MYOkwxRKXgB1ogc49e4At4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"utile": "~0.1.7", "colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.7.x", "async": "0.2.x", "request": "2.14.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.10.1": {"name": "http-proxy", "version": "0.10.1", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.10.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "50f1ce725c87c90a4ec42679dbf4e3d774dfb181", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.10.1.tgz", "integrity": "sha512-OZW2IF+JNZPRJNU0zIgjPF8RDRJYQaXHx52gf6zwLKIc/sKH3IHXfXAQgcib6g7+WNMvmtkT/coTP8TKF7MVbw==", "signatures": [{"sig": "MEUCIQD7OQixvsRJ7OGwtcHaXJ43WrPsHdVsvay0tXxun3f/IAIgfAfo+nGsHkNSXuMXfsxjnkvsX/OUSgoIxJ/5oAGaumg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"utile": "~0.1.7", "colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.7.x", "async": "0.2.x", "request": "2.14.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.10.2": {"name": "http-proxy", "version": "0.10.2", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.10.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "c28ebf268946faf4cce13104e7266b6827fc3a20", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.10.2.tgz", "integrity": "sha512-xSZF4vADHqW06dcCHucwdViv2jz2v3HNqs1J3JJOhl9c5R0l+MbDgY0+aEI+YnyxYYg6g1WTkBqyo65VU7IYZw==", "signatures": [{"sig": "MEUCIHYJDNKjymluBKExJ95x0tSDakCL0GfkK0QLRBnuHe+NAiEA++H0J3D5zmf96VQGiILxxX0iA3okz66LoXHRBTHcxLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"utile": "~0.1.7", "colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.7.x", "async": "0.2.x", "request": "2.14.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.10.3": {"name": "http-proxy", "version": "0.10.3", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.10.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "72ca9d503a75e064650084c58ca11b82e4b0196d", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.10.3.tgz", "integrity": "sha512-TTTzpW+j6yTOOe8It+GoA1c9m6M5P7L1qeH/Np6zcD5uWj/ozXg3DSCzISSQdEzbgIuF6Ly811+V/zE7eujj5Q==", "signatures": [{"sig": "MEUCIGBf07TQYYtvY4bNw0uWQamm27blDMCi0Xw5k0tHbP9wAiEAjQopjPZhSWwlfI3hOJrRb/laM+Nd2PWyD7cn5l/gvGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"utile": "~0.1.7", "colors": "0.x.x", "pkginfo": "0.2.x", "optimist": "0.3.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.7.x", "async": "0.2.x", "request": "2.14.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "0.10.4": {"name": "http-proxy", "version": "0.10.4", "keywords": ["reverse", "proxy", "http"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "http-proxy@0.10.4", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "bin": {"node-http-proxy": "./bin/node-http-proxy"}, "dist": {"shasum": "14ba0ceaa2197f89fa30dea9e7b09e19cd93c22f", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.10.4.tgz", "integrity": "sha512-lb5uBBW+eLzoZ/Tute8ENcbNvibErYYPzNhjZk9IZugBAl87kUSxzVLBwQ7FbU2qBuorbah6VObuXvp2DN1LbA==", "signatures": [{"sig": "MEUCIDlJr6PHIMPjA7rbQBizXNPaAKEUxwpKU0pbXBrdWR8GAiEA41wk4BZ/PhRkQ4CLFL1OLsvYPzgbOurtAQovdcPiLrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-http-proxy", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "npm run-script test-http && npm run-script test-https && npm run-script test-core", "test-core": "test/core/run", "test-http": "vows --spec && vows --spec --target=https", "test-https": "vows --spec --proxy=https && vows --spec --proxy=https --target=https"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.3.22", "description": "A full-featured http reverse proxy for node.js", "directories": {}, "dependencies": {"utile": "~0.2.1", "colors": "0.x.x", "pkginfo": "0.3.x", "optimist": "0.6.x"}, "devDependencies": {"ws": "0.4.23", "vows": "0.7.x", "async": "0.2.x", "request": "2.14.x", "socket.io": "0.9.11", "socket.io-client": "0.9.11"}}, "1.0.0": {"name": "http-proxy", "version": "1.0.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.0.0", "maintainers": [{"name": "yawnt", "email": "<EMAIL>"}], "dist": {"shasum": "81e0443e67c8292842892ded718553dff7000603", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.0.0.tgz", "integrity": "sha512-uzCoy8FwV9kzQlNrIhn8GJ/ZVIrdDDAJ+thHEz6acFPmgp4kL4YQ02U/EIqNQt5lM4Iwid5eposbwr9yaP4Z0Q==", "signatures": [{"sig": "MEUCIF/J9rnJbxZpXkNbsdwPKfanFYT8x34qhezYzuLTMF7/AiEAy5fidudBm3Tk2VDGMuJnce7hDsJTptdl1iEz054Ya+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "yawnt", "email": "<EMAIL>"}, "_npmVersion": "1.3.21", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.0.1": {"name": "http-proxy", "version": "1.0.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.0.1", "maintainers": [{"name": "yawnt", "email": "<EMAIL>"}], "dist": {"shasum": "d203d3b2dc34e968174956f11a6c35140da84384", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.0.1.tgz", "integrity": "sha512-vlsdwLyGg2DkYLrUKPVfog/Mgdp/q+OGeN5+8RD33I9YnFXpD+HL3CLs/nnRnvy3Q4BI2JnSTaqZkEu5lta8Gw==", "signatures": [{"sig": "MEQCIEGDj4ZHP+AQhxSCy5IHFg1phd2P2smCdGIlqiGlt1/MAiAdp2U+j70qcaGG4SP1t6mVkd9vah0rRbe4nzotJUDxcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "yawnt", "email": "<EMAIL>"}, "_npmVersion": "1.3.21", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.0.2": {"name": "http-proxy", "version": "1.0.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.0.2", "maintainers": [{"name": "jcrugzz", "email": "<EMAIL>"}], "dist": {"shasum": "08060ff2edb2189e57aa3a152d3ac63ed1af7254", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.0.2.tgz", "integrity": "sha512-nvvE4Yikgj+6kzFpyLlICTQ44zPe4J2PmTt0WA2ThvjNbuXRJCmEG1qxnobnJmhdtTHCvpJ+Sb+8PYSXAy8B+Q==", "signatures": [{"sig": "MEUCIHK1Va1FWy6+UWw81Q11aj00VNmXP30ECfmJ+JXSYBhfAiEA3tSlnsPRXwQf8df6qbXzxoD3Pkq8t/F2Nfb+RJ6x//A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "_npmVersion": "1.3.24", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.0.3": {"name": "http-proxy", "version": "1.0.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.0.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "3867396ab7d8fc70855fa72b55a527cc13427ea5", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.0.3.tgz", "integrity": "sha512-WCyoLRzuSJYvzPMFkEHbARw6tskcVFjEWXw4gRqtvc05iUrJDwq7HUrwi0Sb3B6ZgUyf1EthmCRi6PSOv81O5g==", "signatures": [{"sig": "MEUCIBpiiWOU4kaW1PMwZ+YKH4aM8NhnD27xAmZqlaFfBO2hAiEAxRc9HSx8T15Joc+JEJDqmO3euSF0nOT1fgwWsUitVP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.1.0": {"name": "http-proxy", "version": "1.1.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.1.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "309056d122bd5708ec806b40b22e379a9dcbdc83", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.1.0.tgz", "integrity": "sha512-qCZQMX4ePII2mh8YpgoDqNmT6WNuN/wcymjkUUFOssEuu/+k3j9GaLeZmgxCDo+3LPwwN5M/ZI2DNrovO4pcZQ==", "signatures": [{"sig": "MEUCIBQPlRQyDagC0aTYNdlPkMoN8YSyqyZ7FVtVE6z2L637AiEAvCR/bX4d6i0QIhbx/ykQfzxJZG2aCMhp9ENBNLmvqGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.1.1": {"name": "http-proxy", "version": "1.1.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.1.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "3ddbfae24ead5f0edd04beb5881d9fa779a709f1", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.1.1.tgz", "integrity": "sha512-RffTfroTe2H+aKfYYQgZYklwMIJcm+wFG/H/zd5WWIt1EQuJmNRoQNzKYU6JheglOag/MhsNZaUEzuTUepTFvQ==", "signatures": [{"sig": "MEYCIQD4A4u+G+nEPxNXlbWHmTuV6Qskdv2SkGMlGEuAn1i45wIhAIvl4tsBZWUmXsYMnOHm0ddc5SVYHy2kJnoPcG4ANwtn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.1.2": {"name": "http-proxy", "version": "1.1.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.1.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "d78d0793c46815dbf39fb27723a4ddfe767777b0", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.1.2.tgz", "integrity": "sha512-urSE+TTNhdbUkIUPS6AYf3Q3rkUi+x/r6cMrPpSsBDXpk0t049cX1j2G3SGOvfRpIRn3rQQuP1R/IFWpGQy9Dg==", "signatures": [{"sig": "MEYCIQCE7Lzx8zxQDdf0iRwe4zdCyHtINpJwHvk3NNSIomswvwIhAJ6K0nOGV5lYAxLWg85PTvFFYzjLXmxsSjk8Nqj33H3q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.1.3": {"name": "http-proxy", "version": "1.1.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.1.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "4f93ed116fa975d63c2880e7818504678287b300", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.1.3.tgz", "integrity": "sha512-HCevbCXiRV0IUyYyX/3MTAVb/urhMz0FPJ/aVJFELItHY4bXXVqa4W9QdRXY3/kEd2/tXF9bKISIYzif6aTqmQ==", "signatures": [{"sig": "MEUCIGYnncKKr2F3Og0TYDvPXX6+yqxV2kGdx7THuS5DiWyqAiEAvzHRNzq5ZuBdXjL0WM7vDZOewBS+FhaquqBox/wagrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4f93ed116fa975d63c2880e7818504678287b300", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.1.4": {"name": "http-proxy", "version": "1.1.4", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.1.4", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "65bb5bfe645f322c65761febcc145a38111174b1", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.1.4.tgz", "integrity": "sha512-74ZFiUqOucpB0ttgZLjogJiV50SCDNbZdZUyWqfhbYf6Wab2cAvJAUEkFp5YtmJVkYb79dg4gMXGhmDPJ/yU+Q==", "signatures": [{"sig": "MEQCIA5zF55xRZzT5yWTJDsRW6/H9Np6L24/zTORW/T+Yi6YAiACqXvcKpmhrFMi146ilHrIZ4UBWgsSbqdDo3YMLXH93A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "65bb5bfe645f322c65761febcc145a38111174b1", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.1.5": {"name": "http-proxy", "version": "1.1.5", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.1.5", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "ade491a3d40e61b31334ee9a40cd91ac125d1839", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.1.5.tgz", "integrity": "sha512-4kLjVMsjGhvOUt9Rm4yDkwaEXR+6V+4u0eyCtzzYgr3d2Ev3tYKgfSb4rPiLKJIUWGqdD1Qk+arvuQm40nJBQg==", "signatures": [{"sig": "MEYCIQD06kHXWtjQ4kp4RZAVLb4RAk+vjaIlZ8YiSB0ODam35wIhAOeXTqB48pgJjT0Cm6Nw80BXVnDbuKpFMeXsxlZXyJeH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ade491a3d40e61b31334ee9a40cd91ac125d1839", "engines": {"node": ">=0.10.0"}, "gitHead": "7104a7c023073a49091969f825738c79ae036123", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.13", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.1.6": {"name": "http-proxy", "version": "1.1.6", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.1.6", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "1d9262f6dff3a325c0eb84e125502b5a930a1a58", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.1.6.tgz", "integrity": "sha512-r2R94aWuWPYfNUTGRENi3M/E+0y4pz3mbHjBLfhTCntTqOp48taAmBxSaFWen9Kk8DZAL3smTdLgzh4R01tz3A==", "signatures": [{"sig": "MEUCIQD9/UYw9D7s8QL/dFECGJc9oQd+quM37NS5O6JY8Dqw5wIgcoAfdY77PmhsLKMPVcF0TcJg+1C3d6iBB5G2WUzfBzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1d9262f6dff3a325c0eb84e125502b5a930a1a58", "engines": {"node": ">=0.10.0"}, "gitHead": "ed9e12b0edb0fc206610e94bd696425619868474", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.13", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.2.0": {"name": "http-proxy", "version": "1.2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.2.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "17f4626950d75c0fcdcb3c4d3af35bfae5fdc016", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.2.0.tgz", "integrity": "sha512-TACK0zwxdRH9M7fQ6pWf72MEhZRXjoKtUBFIMMy+GOpfHH9pUAU5vPUskDKAPdT7tWG66ZnBq2n5i5eQj6Uo2w==", "signatures": [{"sig": "MEYCIQDeUF2QpMqx2eWW/sBH2BROaYYJ1JjiZoihUYl9oW8HfwIhAIJ3evihyY1oBgyJwVDiMu7MfDfHMob5h0fWO4R9OXrJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "17f4626950d75c0fcdcb3c4d3af35bfae5fdc016", "engines": {"node": ">=0.10.0"}, "gitHead": "63c53a177217283ec14e4f7c2e891db48842ab4b", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.2.1": {"name": "http-proxy", "version": "1.2.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.2.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "83d93c05431e5b7753db22c961d1196cfc39877d", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.2.1.tgz", "integrity": "sha512-hlftjVnEc1QHToNeYN2k4rZZhb5bFw/XLwve1ve+VbdBoMlRahHZlLpWmDCYxI4M1zfrDloRAEjutXMDGC1XgA==", "signatures": [{"sig": "MEUCIQCOvbwuonWe5b8jkWXIO6SjXn2c5sXX4BDUu1JCphsmfQIgfty3HkdNDFugRnC0HpkaiLDSTlsFFXdjgBbURrm8RdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "83d93c05431e5b7753db22c961d1196cfc39877d", "engines": {"node": ">=0.10.0"}, "gitHead": "0a6b424e2c3b6cef68362a71f0e56740b2605af7", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.3.0": {"name": "http-proxy", "version": "1.3.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.3.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "21fa4368accc09c6341abb9c16275db4645053fa", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.3.0.tgz", "integrity": "sha512-PprwQ3vLZ3i+3dGYko0RJdrO1zLQQ7foueVL3pmhfiUniAhFnqC8H4uL3+McmpZNydi96AUPHfwg/7cjrMIHTA==", "signatures": [{"sig": "MEQCIFyzqvBKAb8NIQT6tfkCDEcfx7vZvugg+2u/akBrl4LFAiArTLh4dOS4wAdBlMJbnHspT43NYqCnojZk/v3cjWx4vA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "21fa4368accc09c6341abb9c16275db4645053fa", "engines": {"node": ">=0.10.0"}, "gitHead": "05f0b891a610fb7779f90916fcd9ed750df818b2", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.4.0": {"name": "http-proxy", "version": "1.4.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.4.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "4173cbe81564f8ca94c7228e46d2898048066d3b", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.4.0.tgz", "integrity": "sha512-CuLhsr8V7GRlvpvoNgURSQpx97QbzQEsJD1Y4Uo8u6zgyZv7H46HFonWVmhF1adRbR3Fw3gjsjQsOYA5jMn6NQ==", "signatures": [{"sig": "MEQCIGFi6fkfyYqHHceremJCgc+qx4prniIErko0pFPOU6y8AiBk3kjWRaRWK8+eFQPIe7olRRnFA+MNmkN5YfsvzzC1nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4173cbe81564f8ca94c7228e46d2898048066d3b", "engines": {"node": ">=0.10.0"}, "gitHead": "c82f5dfc621c6fafb8702e3ea87cb1560fec7455", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.4.1": {"name": "http-proxy", "version": "1.4.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.4.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "31cbd2d89383bfa1885da260ad1fc023df13662c", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.4.1.tgz", "integrity": "sha512-iOEzdTtdfsCGo42S6Xc7n8b0WTyGZgFHaSC7J/gWbEbuopXtE+c50kR7mh5M2OjsxYp0BALWbF/pPzzQqDUtLQ==", "signatures": [{"sig": "MEYCIQCoUz/W7F0nPZl70O4CMezE/JrgrPERzmulvrQQ+Vt8ogIhANfYXqzveODmxTnz9pkHw3k6ietocyzxMuJbpPSGmTtr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "31cbd2d89383bfa1885da260ad1fc023df13662c", "engines": {"node": ">=0.10.0"}, "gitHead": "d5c656bceb50dc9008ef223bc58b918adcf05352", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.4.2": {"name": "http-proxy", "version": "1.4.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.4.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "4ebe93aedd7f00ea3cd60dec86fb2db6764b971a", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.4.2.tgz", "integrity": "sha512-cN8jGEzL9A5ki46jD0GH+MT5xxOrr7f0KrsqISjLLI6OOE/4d5Uii33a027INZjhVUqzrUdQQxL0C9s7fFB53g==", "signatures": [{"sig": "MEUCIQDCWeOKqqm+8EYruQZGQylbIK0pn/yXGUXsuEmibcqoqgIgGCwnSTDcMaPCZSKzdJt719tzpo+j+RvtVrXvwDoUD3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4ebe93aedd7f00ea3cd60dec86fb2db6764b971a", "engines": {"node": ">=0.10.0"}, "gitHead": "df12aeb12de79de1157898d45f4347fd0037dd70", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.4.3": {"name": "http-proxy", "version": "1.4.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.4.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "27d3e4978a5e64641ed810ecc4a415fd00067020", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.4.3.tgz", "integrity": "sha512-/52yRi2krOe4W3BHEjASufBjUVs2qZ9bSIFM6ObKHNqYolJ6r/mNq/LqUPCOjzIPbFNAf6JOLqMEhapNT1JUfQ==", "signatures": [{"sig": "MEUCIQDBiw/w6gmgSS2Jmie4FMvRc9pKusSC+8vh9AdTc8/EHQIgAaBeF8lkupRzJwJJYcO13u/TqipHeP8zH3PsxF/c4jY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "27d3e4978a5e64641ed810ecc4a415fd00067020", "engines": {"node": ">=0.10.0"}, "gitHead": "554f59c5182d58b359df0159a29ff5ea35dd3830", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "cronopio", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.13", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.5.0": {"name": "http-proxy", "version": "1.5.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.5.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "11d4c81100f5cbd7303b4bf8b62478fda7af728e", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.5.0.tgz", "integrity": "sha512-uGbGkPLzfcH5xfb+Ybh4diUN5Nue9WBpTsDTQKvj0f3ltBzSzjWBwS/t4p1BqZxKiAsiK08jHP33HZP+3dNmdg==", "signatures": [{"sig": "MEYCIQDH/p1YocaytY1Zjwlbl3lT26jizjmaP1qauXw68Hmn/AIhAM3xJ30UVlVWki4ickVVX+WV0fAQLTYG+1Ey7X24/JFc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "11d4c81100f5cbd7303b4bf8b62478fda7af728e", "engines": {"node": ">=0.10.0"}, "gitHead": "232258b6ec2229497fe557454a121d917968f5e8", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.5.1": {"name": "http-proxy", "version": "1.5.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.5.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "40e09f6a3311a28936dfa704d86584c8b6185bde", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.5.1.tgz", "integrity": "sha512-+SvLmNK4dHOO869u/Ytz08eGstcjcU3tZMIrLQq7rX6aduLKOcuxSECc23gckDxvSPnVjy84FHtaS4WrfpOCrg==", "signatures": [{"sig": "MEUCIDB2nUEl4K5j+X1DaC/8tWozCW3d9IsF6nPkFOq8c0+QAiEAvcxjvA7angCbXT9Oi2vjZcRHPqlbmLlFzmYTQZrroKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "40e09f6a3311a28936dfa704d86584c8b6185bde", "engines": {"node": ">=0.10.0"}, "gitHead": "f0bf7418156db2cb87a616b0a34bb1f028db9142", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.5.2": {"name": "http-proxy", "version": "1.5.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.5.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "60afc612cfd82c9d19a502835192e3c5f530aff9", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.5.2.tgz", "integrity": "sha512-IbawsleSvLMHg1/t9c83YnOrBd3LthOeB+urtkrXKztDZXoMmEVouSNIJTRqzdh4JLjI9cYVcD0IIB489o+CVA==", "signatures": [{"sig": "MEUCIQDt2H4MhQEYmEIziSztvJVNYmZVDPdEaM8khonAIMpI3wIgDJUFB1YzNOCLq3r7DIDpNLPdv0ghKEsPfGcr+NXNQ5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "60afc612cfd82c9d19a502835192e3c5f530aff9", "engines": {"node": ">=0.10.0"}, "gitHead": "43c6f0c7c06d25a670c410500a8623531df458b1", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.5.3": {"name": "http-proxy", "version": "1.5.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.5.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "d89aee8f1304caf847723d2456a18d7422ef33df", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.5.3.tgz", "integrity": "sha512-mmNCuGhABo0PZkYsMUi0B+/rgtKZYfmkqUKuTW04C2eCxZ764MJTiqKvgg/ULYqb4B2tnGLst4bCmIS1bAMQZQ==", "signatures": [{"sig": "MEMCIFxAT68k/ZKQJhUjiLz1GcKSl11rCLesLPjyLAZ325WDAh83ZJlT5idegDAWVze4VHfzoxmDld3O6LW4IHHhX1Vs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d89aee8f1304caf847723d2456a18d7422ef33df", "engines": {"node": ">=0.10.0"}, "gitHead": "9577a0faf2b78af606168673407ac47a851c084c", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.6.0": {"name": "http-proxy", "version": "1.6.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.6.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "1cc1614be2a09593d936a26013546e485c4b1d35", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.6.0.tgz", "integrity": "sha512-OHtTcwbTVovYXv9xBk9Z3YkygD+HvrzXB+hd3b4d5z10F90G7NYXux4B3A6WJG2RsPq+iCdriwPzML756zHV4w==", "signatures": [{"sig": "MEYCIQDKVwHt9secXxTarlmCbYyUWvDavIHUZEFo7LM3ZLV4gQIhAPs3mpv3yYPTJSBaS7utmWZ06ioR7KJuno+oq7AiG9On", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1cc1614be2a09593d936a26013546e485c4b1d35", "engines": {"node": ">=0.10.0"}, "gitHead": "43641b00b34ccc05bdf09f904695061d7c857aeb", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.6.1": {"name": "http-proxy", "version": "1.6.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.6.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "8b163a57114f419f42476effa4d9aec83eec73f5", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.6.1.tgz", "integrity": "sha512-NfLqS2wZ1RxYD2RaZ6C5DhLHiNmgPuzRFUvJ1krFZ91x6h/owAnCU5f4To0ZZ5sq/p8LQDu0fjR4N7GQqgrr6g==", "signatures": [{"sig": "MEQCIBCk1W9Y8LyMKf0e1428CGIJedEejzU5QKiAvADm2MPJAiBk0PigCkhrffKyTPYKuIEmQ35CA0QxGyRMIjDHQ2XD1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8b163a57114f419f42476effa4d9aec83eec73f5", "engines": {"node": ">=0.10.0"}, "gitHead": "fa797fca900c10ebc848a2b445204b47da799483", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.6.2": {"name": "http-proxy", "version": "1.6.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.6.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "bfbc07134806b6790368c31d88048c5575228d04", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.6.2.tgz", "integrity": "sha512-xMlf/W31N9yLS3YnffhI7QoJpXC/TWyIXyQ8MtPDPCJcTzOEhZEzt6brQpLCRdiMjEROsrHOqbi+P0sd+3Yd0Q==", "signatures": [{"sig": "MEQCIDl8Zlj4Ge0VznKrNCXEKWYiLXUX4/0y9ZFgTO9AKncDAiBI+GfUVQCETF+8I16crFarK3FdsGnicBquFvViSZRv9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "bfbc07134806b6790368c31d88048c5575228d04", "engines": {"node": ">=0.10.0"}, "gitHead": "709b3e96560d619fab2617f9ddb902b4982b4103", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.7.0": {"name": "http-proxy", "version": "1.7.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.7.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "ec05dd041207ef038922af38948d8b6a4114074d", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.7.0.tgz", "integrity": "sha512-jxSSZJ3gi+9imkuBe2TlCm0r8ddxhTbXaPnBM7mdgv9MudnY7oiek7zoVx8u1SSZVsLf6iTza73nA9K1nzKJqQ==", "signatures": [{"sig": "MEQCIBP0W1w2iArDIsxaxQOMZWjpcJRMZJ461s7qC4svtZSDAiBRAF6tiDN+PDM6YT13RZ9943SSBxH+ey2ADg3labni2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ec05dd041207ef038922af38948d8b6a4114074d", "engines": {"node": ">=0.10.0"}, "gitHead": "276f65a3b810ded01757ec4bfd4fe2b00a1e66a8", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.7.1": {"name": "http-proxy", "version": "1.7.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.7.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "5fabe2e1d617beb09cc108b3a45c416372c3d36a", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.7.1.tgz", "integrity": "sha512-nsT49XyTJwajDqNGwoovbZH6E6uwBEOY5VymfvFslmnXsyyjshJMWAf/k0lMQjm47kEBIiXY+I9CrPqbGKAipQ==", "signatures": [{"sig": "MEUCIF4fah/SGHNmwkFu4X+xxZXBgLkQjS//P44nXl9/n45UAiEAwPfrR4+BvNeEe/hfnHRm0GiEOhDOzpjQW4i9IG1/KtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5fabe2e1d617beb09cc108b3a45c416372c3d36a", "engines": {"node": ">=0.10.0"}, "gitHead": "56a7b77645b13d337c1a2f879460193d310454c8", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"eventemitter3": "*"}, "devDependencies": {"ws": "*", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.7.2": {"name": "http-proxy", "version": "1.7.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.7.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "5538cc6ff89266c6eeaa793c28e62d43fa60118b", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.7.2.tgz", "integrity": "sha512-tPvvUUsEoFwPti0T6AEqz9npw+yKWePYczXhqUEXBv/yAVkMh5DteCuEEbW8L1E4757XYhOK52S1o6S8DGS0QQ==", "signatures": [{"sig": "MEUCIG2GZBqp5K/2J8Z1oaGyJ4Rg6IyvPYugVBvaPk5u34BuAiEA0vwJ0Pie18tE8tPuMGWaUeIIe9AfjJxhwnQ9tKJaQ8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5538cc6ff89266c6eeaa793c28e62d43fa60118b", "engines": {"node": ">=0.10.0"}, "gitHead": "2086e4917c97f347f84c54b166799bc8db9f4162", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.7.3": {"name": "http-proxy", "version": "1.7.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.7.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "9e4b6eabae3e94f10130db97d858063fb6197718", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.7.3.tgz", "integrity": "sha512-qxv/OlR7bvv+I+iasTvwE4tjqwPtrH5mzXQDfWLjAFvwAInAgzwZrY+wbF/gLEQNcu1M8Up8HYXTfwDg2bmRpg==", "signatures": [{"sig": "MEUCIQDIHjpFGW3UDeu8kXGb6u61TzaT+J79p08PBdEce4LLaAIgZ+OOJzO+pTkLhUpQ/9FQAZxETZtR9A3FenFYg1Cfqjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9e4b6eabae3e94f10130db97d858063fb6197718", "engines": {"node": ">=0.10.0"}, "gitHead": "6a330ff904d02a41f9a1cac338a98da1849c54ca", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.8.0": {"name": "http-proxy", "version": "1.8.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.8.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "60cae018462824084b27f0840d9c366d599e7077", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.8.0.tgz", "integrity": "sha512-3SoN1Rn6GjsZUDOOJwbbEaU+8ZhsTF4JJdlpLx4Z9l9XDKal3jt75JHfJ32iXPpv4nwc8gxSYEyFdoJG/Or7Kw==", "signatures": [{"sig": "MEYCIQCjhlkJKut50dO4/vZRXmgCRSEhdqgPlRgRGwAAzQCa4wIhAIK5Wl9ucq4toH16CH9/Pwd6kLLW1T5cU1vKW+/plPBA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "60cae018462824084b27f0840d9c366d599e7077", "engines": {"node": ">=0.10.0"}, "gitHead": "f0db5b3f708b0858f617d472dfdd0ba211b774ef", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.8.1": {"name": "http-proxy", "version": "1.8.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.8.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "95ee083305c719dba858aee01da1bfc8d7ab9efe", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.8.1.tgz", "integrity": "sha512-3a0oCFrKhNZ1P+8CirMlYT6nASl7tTctxrgRXd+CWhpn3DA9OCCadb1Ibv8Vz8IXodC7ylUK9skkOMkDDKRozQ==", "signatures": [{"sig": "MEQCIGOaI1UyXPWirJqO2VeGVD75e3hkOly6ftCm/7sJWXoXAiB/DKLkk2hquklLKIfHOi6VhgHt8NsbkGw8fl3DXk4owA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "95ee083305c719dba858aee01da1bfc8d7ab9efe", "engines": {"node": ">=0.10.0"}, "gitHead": "3311106c2c2346f3ac1ffe402b80bca3c7c59275", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP proxying for the masses", "directories": {}, "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.9.0": {"name": "http-proxy", "version": "1.9.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.9.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "adbb99b55027e15e802ef32737d2de3b88ef52a6", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.9.0.tgz", "integrity": "sha512-9ph3ZOnE6OXvv5m4pIlyQmyxj2DI1XwK6yDNBCuUbdbam9KBEjQBkgtxnaaIVJwqgvEa5eMAekAP68oRc1cUbQ==", "signatures": [{"sig": "MEYCIQC9BakYzTg9ybC+vMsaeqcmx0US2ISsd5EHkFsPbPRZYwIhAKsRNUEhjX60wpGRzt0DrNuXZbdbBB4JPK2P/lUmC8EM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "adbb99b55027e15e802ef32737d2de3b88ef52a6", "engines": {"node": ">=0.10.0"}, "gitHead": "87a92a72802a27f817fcba87382d55831fd04ddb", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.1.7", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.9.1": {"name": "http-proxy", "version": "1.9.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.9.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "4026281e35e4cf93f169a6fcd8ab55a0b5cc7d87", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.9.1.tgz", "integrity": "sha512-d+W3LSP0uu1tGHsiH5MJI0Z59sunvoojbACoQM+8FxJnd+4u3T93xZyz+fIsfZrhs2/B5XpeJUI8jtyIapseHQ==", "signatures": [{"sig": "MEQCIBisY7DKf/EK8+pqhJ3g/FiLD6l8vqtCf1I/3OzUsdg3AiAmonmSI8qeuRB1g/DNUpBRfmaGVbslamcDpxyVN4W9UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4026281e35e4cf93f169a6fcd8ab55a0b5cc7d87", "engines": {"node": ">=0.10.0"}, "gitHead": "21b30b754db4f6410c3d2052bc123b3fdae57c46", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.37", "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.10.0": {"name": "http-proxy", "version": "1.10.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.10.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "406762e8a22bbeab387d3a7df24cb1e9f3b91a78", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.10.0.tgz", "integrity": "sha512-ANI41vH6uq8PoW4r+uZ5rVq1aBVt9C4qt/zaqWrbErJFikWsK7O/QRmmCCi66nzbSaM/sAL7WWp3D5PwuPnoeg==", "signatures": [{"sig": "MEQCIAyx+FMi96Z4B5/qYSLaiXbb9b+5tiTdsS8bKHBnpn6xAiBX9LE56FXeuJK6I59bDI/ybox688KzuB/mSysWNKx1Dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "406762e8a22bbeab387d3a7df24cb1e9f3b91a78", "engines": {"node": ">=0.10.0"}, "gitHead": "1dabda241f3b93eb9195134042e7a3b84fd0ef57", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.37", "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.10.1": {"name": "http-proxy", "version": "1.10.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.10.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "0bb51c2727bdc094722cff569c7cf1455bd18d59", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.10.1.tgz", "integrity": "sha512-af/IrM7WTI9+XwjMdfe+T2pBkzFKFt6RdAFXIdT+Z5VXf5jlwY4wK13fTjzJFsrIciZR3WxV27GKtQCVKHVxsA==", "signatures": [{"sig": "MEYCIQCxYoZq+Aaxh5N4y+o2q0Q88ZhDogp1xQU8C9xnjzRrhQIhAO77bMZ+Fb+7645KPTQAIY/RZd2+nNHZmbyNapTiKksm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0bb51c2727bdc094722cff569c7cf1455bd18d59", "engines": {"node": ">=0.10.0"}, "gitHead": "0bd446c680e9991accfaa3a6a70e411fdac79164", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "1.6.3", "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "semver": "^4.3.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.11.0": {"name": "http-proxy", "version": "1.11.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.11.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "66fb73ece772e6b789287b542829cdb6e628f263", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.11.0.tgz", "integrity": "sha512-9PheQDb25sedBMfFiAm5LShFJJDyyQZUAJRcjiwdx61XxUFi7MnQbBDt7dfJarGmouBcTvs7P7T+ogPzWG2sXg==", "signatures": [{"sig": "MEUCIQDlrEopZ0eG+Vrbvs6vM5WDNSWk5sCBc51ZPwGHkZ3DVAIgUI1xl5glwtfashKsDxLVAwiQpQu8yfBQrWIo6NkUcsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "66fb73ece772e6b789287b542829cdb6e628f263", "engines": {"node": ">=0.10.0"}, "gitHead": "934e6c4d54292a1b961452074e02fb5d45da729a", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.37", "dependencies": {"eventemitter3": "0.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "semver": "^4.3.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.11.1": {"name": "http-proxy", "version": "1.11.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.11.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "71df55757e802d58ea810df2244019dda05ae85d", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.11.1.tgz", "integrity": "sha512-qz7jZarkVG3G6GMq+4VRJPSN4NkIjL4VMTNhKGd8jc25BumeJjWWvnY3A7OkCGa8W1TTxbaK3dcE0ijFalITVA==", "signatures": [{"sig": "MEUCICgyK70plaGFyRsj2D0+H6pI+msnzuUyXhFjHfmf0hLqAiEA48BYlCdf2z357dKqSThBmuqS0hEGWSVspR8HjxofgiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "71df55757e802d58ea810df2244019dda05ae85d", "engines": {"node": ">=0.10.0"}, "gitHead": "7e6c66a7e485a6c0ec3a1c567bbe800fdc56c9fd", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.37", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "semver": "^4.3.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.11.2": {"name": "http-proxy", "version": "1.11.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.11.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "c50d2fb06eca79d4238e66fd94393d2e41e63740", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.11.2.tgz", "integrity": "sha512-TT6GSdvaIJJ1BQaqgAw2g8MByFaqfTzBgefCdaF6QIDkVZIsfzD0cRP/M1mjy834u9BwKfxSPMmVMnVavhNG5g==", "signatures": [{"sig": "MEQCIDKBP8cOEKxzKrrQWv0GuqqQ3Vr0W4YblbBYsie3Txv7AiBDRKxF9qzqtBnb5H+FWosjgDrunDAIVTwDJkttd4GqRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c50d2fb06eca79d4238e66fd94393d2e41e63740", "engines": {"node": ">=0.10.0"}, "gitHead": "30e3b371de0116e40e15156394f31c7e0b0aa9f1", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "semver": "^4.3.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.11.3": {"name": "http-proxy", "version": "1.11.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.11.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "1915dc888751e2a6bf3c2abfcb1808fa86c72353", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.11.3.tgz", "integrity": "sha512-dVzC6JlPUaRBONwMwVa/x1H5T0Gxn7VEldmfXuA++6+dE1a18VxfZoFIL2qHDVW4Eu+5m9ppPhm9ozeG/EhkmA==", "signatures": [{"sig": "MEUCIQC4asu7j+hRH9LROzu/2/VQtgEmy/Hsv0p97W2Au+qaSwIgb9+isNeSI8k0VnMcdXQbRyEFqLK4r5hIdv7u4wYcS84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1915dc888751e2a6bf3c2abfcb1808fa86c72353", "engines": {"node": ">=0.10.0"}, "gitHead": "60baca5aed4f45ef1d7b3f7edd909375853d344b", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "semver": "^4.3.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.12.0": {"name": "http-proxy", "version": "1.12.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.12.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "4f02ea971e79e6affa12fa5f10ca2aebb5e3b17c", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.12.0.tgz", "integrity": "sha512-eJTEn7SnaiQcIlKfXe6YlojI6h6BsG+YakSRF4x512Eq20ercRruIBx6n00Fl8Bg5ThI86miV8twYwlQVSRM8A==", "signatures": [{"sig": "MEQCIGY0wAGhFlx2ZfE1VT/nFpK8SpS4lIBTIrt2OrSJ7LBtAiAIrpBRbdKYxAky6kVkyodGVF2rA0gXKEttFKEqZrpKRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4f02ea971e79e6affa12fa5f10ca2aebb5e3b17c", "engines": {"node": ">=0.10.0"}, "gitHead": "b5a6d0e58396363f4c457f6d1654614bdfcfcb73", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "0.x.x"}, "devDependencies": {"ws": "~0.5.0", "dox": "*", "async": "*", "mocha": "*", "semver": "^4.3.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.12.1": {"name": "http-proxy", "version": "1.12.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.12.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "23b4244d1b7f5a77ae333b8b189c13b4d2ccc468", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.12.1.tgz", "integrity": "sha512-ExOhgzHtcV0Z2mC5EecJ+FmbX0I5VmgqEEZP4HoddZP8gCkOMRXx+TZGsyLHwG5RuNBD+N9dlqFd1fyUgvJSPQ==", "signatures": [{"sig": "MEYCIQCOxSf4tPB3R/X9jgNOHMXHjMaM9SXZTNQfzHrEqLBqigIhAL4/q6MLL4Gh+0YGVFe9n/R8xeX90ZnODKal+0hdiotR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "23b4244d1b7f5a77ae333b8b189c13b4d2ccc468", "engines": {"node": ">=0.10.0"}, "gitHead": "db576d75c18148e6ec3cdc455e6e8254cff40ada", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.13.0": {"name": "http-proxy", "version": "1.13.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.13.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "c29aa326dccd078d3e13c08c4cf474ce53f47c20", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.13.0.tgz", "integrity": "sha512-LzRZxrnW0LTBcHe6FfsbW37XaUkGsOedmruY1wsTlBMJbSY+cjL0ZTuoEvwARn31fwQd9W8DtOBVOjL+kO1EwQ==", "signatures": [{"sig": "MEUCIHTpakUZMeE8soELe7jK/WykWLx0XQ9nnmRjp/Y9VqIDAiEAxmhFfgRrONXrk4duE1SdQmoWcqu2bnWuJ64HghtwtNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c29aa326dccd078d3e13c08c4cf474ce53f47c20", "engines": {"node": ">=0.10.0"}, "gitHead": "268994ea45d9f8737343001ab9542e03023a5c96", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}}, "1.13.1": {"name": "http-proxy", "version": "1.13.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.13.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "d3eaa54f0d8d9d444ae0d9523c94391cb8bd6a43", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.13.1.tgz", "integrity": "sha512-ZjlZ7bQBEZjp80TuqdUR7BWMpuEAoxddKMidVg4/Vt8vgXzZ9IbgWhyDyWiTyICTcc4lFC/fXXIxucrjcrvEHw==", "signatures": [{"sig": "MEQCIC1HawOTsLpQIq/qn2pr85Id2mM0hO94DK7sBHH1LePFAiBORXd1mjVGnMtMM5rRPVsjol6Vmnta0Ntx6R51o7NrgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d3eaa54f0d8d9d444ae0d9523c94391cb8bd6a43", "engines": {"node": ">=0.10.0"}, "gitHead": "9d9fa940cff3aa6134c60732c23aea8171fc7296", "scripts": {"test": "mocha -R landing test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.13.1.tgz_1454389381998_0.16280735889449716", "host": "packages-8-eu.internal.npmjs.com"}}, "1.13.2": {"name": "http-proxy", "version": "1.13.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.13.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "636bcd09f3e7045377a5e919e92d16d29fdbff09", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.13.2.tgz", "integrity": "sha512-/WuL8/hX0wrEZr/isUeBHEPpzFxb0jcGTTYBrwDHh61rP9W9Q5vIJ4RHMn//iDJjzLyaZnzZWnjIhOUVJjk6Kg==", "signatures": [{"sig": "MEUCICOlBtE6UImkpBcxabBzu5czQ0/IDMDBCgl1weQZh3bqAiEAhU3v7XBIGypUPNhRgJsfEBCdpCQ98pepF0X7ch364AI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "636bcd09f3e7045377a5e919e92d16d29fdbff09", "engines": {"node": ">=0.10.0"}, "gitHead": "e1b2f4c31b34464431db251b3b6169689dadf518", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.14.18", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.13.2.tgz_1455724809490_0.5310903904028237", "host": "packages-6-west.internal.npmjs.com"}}, "1.13.3": {"name": "http-proxy", "version": "1.13.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.13.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "d5ec0e25da0c4b2edaeaa9476672640deda59623", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.13.3.tgz", "integrity": "sha512-puN261xDfxaSVKoXkbgh0ZIPkbpFkZu/cTI0UMqfSCpOqF9+GtDR5zan7v67KvdZkRxYjOndSgUd4rQepJuuvQ==", "signatures": [{"sig": "MEUCIQCvZkFpZxSYJIaazZ/XBjJ51ZamYRUpzA6+c9yPTGViEwIgLpz+yxL20CksCwFEweLJtHvgQZgLO8PcnSsuSS2geJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d5ec0e25da0c4b2edaeaa9476672640deda59623", "engines": {"node": ">=0.10.0"}, "gitHead": "5082acc067bbf287f503bbd5b776f798ab169db1", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.13.3.tgz_1463368496695_0.25714756455272436", "host": "packages-16-east.internal.npmjs.com"}}, "1.14.0": {"name": "http-proxy", "version": "1.14.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.14.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "be32ab34dd5229e87840f4c27cb335ee195b2a83", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.14.0.tgz", "integrity": "sha512-qYNY+l15FPx4UAihhJxrl1XaiFMANRzlIvQwqyb6YmUdcqAmmbqPbsLXXgj3RyOmNsGi7r8SrhY1a3WLzWXVUQ==", "signatures": [{"sig": "MEUCIQD+qHBGZIBtdowvhL8UvUp4twCZXC+b4Y0LS/epP/6FcgIgCSiZvRaIYmQSoWJznu6Jno5dcsXbF0AgYHbYMUv6b/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "be32ab34dd5229e87840f4c27cb335ee195b2a83", "engines": {"node": ">=0.10.0"}, "gitHead": "fcfb0b37f6ac61369565507446377f91d955cf29", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.14.0.tgz_1466002382159_0.5383694211486727", "host": "packages-16-east.internal.npmjs.com"}}, "1.15.0": {"name": "http-proxy", "version": "1.15.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.15.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "306c5e1a0e2df519ab4f4f71b0bd610d26bc4620", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.15.0.tgz", "integrity": "sha512-9LBriaxTF1V2bWydKs4BX3znrtQ2nMp7zQwhZ7d6xcjdC/Gw2m3NHeBvq+Dwv8Kvnoeq8VzRQ284GnxNzeP+ng==", "signatures": [{"sig": "MEQCIGP68JDwmB0LbkC7gzxuoIJ5jXlW5BF9sYFrfYctqmZ6AiBBgznOaK0XHLHPWdco66+CUJchiw2+5eXInMWjijz8mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "306c5e1a0e2df519ab4f4f71b0bd610d26bc4620", "engines": {"node": ">=0.10.0"}, "gitHead": "b98c75b1ff3ebdf7f78224eb0d9aa857af2db1d9", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.15.0.tgz_1473872763037_0.9233860978856683", "host": "packages-12-west.internal.npmjs.com"}}, "1.15.1": {"name": "http-proxy", "version": "1.15.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.15.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "91a6088172e79bc0e821d5eb04ce702f32446393", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.15.1.tgz", "integrity": "sha512-Vw5sLrcCsmXm9wIWVLZ2LnMT3or+9qey2zxJMLFce8KtHFO2duOQUrg1UX6ppmFSEqf1nNPdoqtV6CoDaK1YqA==", "signatures": [{"sig": "MEQCIGZ1X8f81G+B6FyMlxjBuShtaBv78mA7O+DF1EQDRWJPAiAY3uj/H0hjdYJQe/ssofXjfmKw+V72PtDTeQ93qylSBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "91a6088172e79bc0e821d5eb04ce702f32446393", "engines": {"node": ">=0.10.0"}, "gitHead": "912cd3acaef484f7ea08affc9339250082e04058", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.15.1.tgz_1473887557394_0.4286337874364108", "host": "packages-16-east.internal.npmjs.com"}}, "1.15.2": {"name": "http-proxy", "version": "1.15.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.15.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "642fdcaffe52d3448d2bda3b0079e9409064da31", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.15.2.tgz", "integrity": "sha512-E6A/0XNoSfQuCUtxoHuj788eRfZPIp3eCXpQHe2uV+Ebbkq6dvcWSbb9lrC9NdMBedAZBNMB5JRRA3kiiyRRxg==", "signatures": [{"sig": "MEUCIE4Cd1GOhFJfM9FfNRgmBYLOUqxcUQ8yihhAwZewjo8ZAiEAhFHp5FyXnZf/KqqWFg+iPDvHi6NBTWGR55Smk63rJco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "642fdcaffe52d3448d2bda3b0079e9409064da31", "engines": {"node": ">=0.10.0"}, "gitHead": "d8223884f61a05fabf788a0bd921c7a6197a96ee", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.15.2.tgz_1477151248727_0.9627266463357955", "host": "packages-12-west.internal.npmjs.com"}}, "1.16.0": {"name": "http-proxy", "version": "1.16.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.16.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "f9b52305e9f864811835277e4a486051b5d4a523", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.16.0.tgz", "integrity": "sha512-GbYQ6Z9GWSukj6atSrStzo5CFmL6r7Hvw2f+WWIyz8QvKbQsDFwMvrwP3Lictu9b2EFWygyHVW4p8m7EQSO98A==", "signatures": [{"sig": "MEUCIGKUmtOCxq+JJpnn941wuSh28XVkXYedEMxL6kmWu202AiEAiw9amFSodOjBZwPEJkFgMgn5Iu9ETFyF6YVk+tuv35I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f9b52305e9f864811835277e4a486051b5d4a523", "engines": {"node": ">=0.10.0"}, "gitHead": "c252b32f6c7f832f157cc4647ceaff33dd265d82", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.16.0.tgz_1480687997540_0.21387459617108107", "host": "packages-12-west.internal.npmjs.com"}}, "1.16.1": {"name": "http-proxy", "version": "1.16.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.16.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "734b32de6ca0e36e51b59c1e0115ff860d7668fd", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.16.1.tgz", "integrity": "sha512-ZkOobwkiEdtUr0YLVg1ft6G5edNwSuXE8Ek46/YLz7rkUZcILuXXQ+iYTe2zXdLyoeFMe31QiiSjGRa9ksgJRA==", "signatures": [{"sig": "MEUCIHQB4hmIK0kUHSgVxmhCY71tTq9LiiG8q+qHbReoc9S/AiEAz72bmxICzhWP3aCgPL7vHoTb2kqiBNt6M1Y6jXGx6gM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "734b32de6ca0e36e51b59c1e0115ff860d7668fd", "engines": {"node": ">=0.10.0"}, "gitHead": "ac1a01b1f3caa3a2a9433341bf5e7a95072d6612", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.16.1.tgz_1480867190646_0.43747326801531017", "host": "packages-18-east.internal.npmjs.com"}}, "1.16.2": {"name": "http-proxy", "version": "1.16.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.16.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "cronopio", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "06dff292952bf64dbe8471fa9df73066d4f37742", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.16.2.tgz", "integrity": "sha512-mVtRyhMoqY5UCrvvqTTIfQPgRO+dDR1qHbuBYk8fjUpA51KUzesT++tRQSdiEhjBBobO4PCnP4ITc/BFsBkm6w==", "signatures": [{"sig": "MEUCIQDTAb4pJi2duS5qhVopZ6rLDSxjt1gsssIZYfzLN/7lmgIgLUh3mKSXPUrYBs9YV38IBisM+ChlcQNGtV4BAzMhVeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "06dff292952bf64dbe8471fa9df73066d4f37742", "engines": {"node": ">=0.10.0"}, "gitHead": "c1fb596b856df971d291585ccf105233f7deca51", "scripts": {"test": "mocha test/*-test.js", "test-cov": "mocha --require blanket -R html-cov > cov/coverage.html", "coveralls": "mocha --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"eventemitter3": "1.x.x", "requires-port": "1.x.x"}, "devDependencies": {"ws": "^0.8.0", "dox": "*", "sse": "0.0.6", "async": "*", "mocha": "*", "semver": "^5.0.3", "blanket": "*", "coveralls": "*", "expect.js": "*", "socket.io": "*", "socket.io-client": "*", "mocha-lcov-reporter": "*"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy-1.16.2.tgz_1481039349196_0.5866330966819078", "host": "packages-18-east.internal.npmjs.com"}}, "1.17.0": {"name": "http-proxy", "version": "1.17.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.17.0", "maintainers": [{"name": "cronopio", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejitsu/node-http-proxy#readme", "bugs": {"url": "https://github.com/nodejitsu/node-http-proxy/issues"}, "dist": {"shasum": "7ad38494658f84605e2f6db4436df410f4e5be9a", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.17.0.tgz", "fileCount": 37, "integrity": "sha512-Taqn+3nNvYRfJ3bGvKfBSRwy1v6eePlm3oc/aWVxZp57DQr5Eq3xhKJi7Z4hZpS8PC3H4qI+Yly5EmFacGuA/g==", "signatures": [{"sig": "MEUCIBld+XMAUru1/2v3SjEsPBWa6HsVrnJg6gArYcIXhgLHAiEAr40qjjMz0XvGPvvwovy8LF8JZPwZUTGSTytWBUJtCJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2gqOCRA9TVsSAnZWagAA0pgQAJ4lM4iyzp+3h/SpDlGQ\nMkG6oT+gkdCkdU+J8y0nIkFT6Pr8c61vMXhuzaBsm3SUlxb2myIYxQp1t64p\nOYaBLeKcS2Fsts4xTRDQc+1LNnHHdpWZ0kxGE9hnRZo2aLe4S+RU/6w3tpTC\nVA6VmLW81sVTYEBQ9CEzQLYWzAKjJlX9XoQi4kufIiDnCes7J5IPFev9kPPp\nj0Gy/dzJX6Dy1UjsPqzajcno+1wEU9QYUvuqs73W0UevmmwTWGZ63MKJjqk/\nKZujjPQSp4ofyaQyWFC7KsWN6G84/dRioUnuGCNr6p1hykqO60jM5imRzbyV\n+L5D2gPH1R89G0ECgD1FmMKSFIRhtUc3YSyuDG/TJ3YbGGkLL0Gl0qa/puqB\nDBifu4jANNvNsP8hA5ms9xWYKBqc5DHbmSGmJk54nagVc9kEoaKOMKY4lU/h\nrJ4k4JxOFoDlgxilB6l1Vkjb06EgzCTcGbCeGfbK/hgIG4+gvhGlKAlsj6KC\nIGDPlpxihQ/SZAh5mgyXIkmHvF7wh+oukAMZybgs3hYVLg4/AuCvJbgFOg4X\nytTd/6eBc5IEwANTTiRXB/AZ3AtyRKwpxbSs48mylYb/ChXw0gJej2RPw4V6\nNnWOckAe1+xSx0xA8sgAkwI0Lo3HBEAmmZFkQh5kf4yWeqaZ1VT38EjBNPp6\nDLnN\r\n=Ht4z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "42e8e1e099c086d818d8f62c8f15ec5a8f1a6624", "scripts": {"test": "nyc --reporter=text --reporter=lcov npm run mocha", "mocha": "mocha test/*-test.js"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejitsu/node-http-proxy.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"eventemitter3": "^3.0.0", "requires-port": "^1.0.0", "follow-redirects": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^0.8.0", "nyc": "^11.7.1", "sse": "0.0.6", "async": "^2.0.0", "mocha": "^3.5.3", "semver": "^5.0.3", "expect.js": "~0.3.1", "socket.io": "^2.1.0", "concat-stream": "^1.6.2", "socket.io-client": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy_1.17.0_1524238988902_0.2940207651042317", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "http-proxy", "version": "1.18.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.18.0", "maintainers": [{"name": "cronopio", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-http-proxy#readme", "bugs": {"url": "https://github.com/http-party/node-http-proxy/issues"}, "dist": {"shasum": "dbe55f63e75a347db7f3d99974f2692a314a6a3a", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.0.tgz", "fileCount": 16, "integrity": "sha512-84I2iJM/n1d4Hdgc6y2+qY5mDaz2PUVjlg9znE9byl+q0uC3DeByqBGReQu5tpLK0TAqTIXScRUV+dg7+bUPpQ==", "signatures": [{"sig": "MEQCIDFbr9fLdlVH+VADckaPmQOObR+bx8kTTCVBE4/ZGtDFAiBCd4NXKvlqyNtCQ6lAL9NoNgBt1PFRJkKZo+HL0OSFFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 231272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgYs1CRA9TVsSAnZWagAANs0P/jmblQqi6QxrRR/Ukxkw\npnEYpCE6E/bZ2lrkjMvtoTUpXrZe6YTORPm3ecHYtDXgp0V7NJ75As53XwkT\n5bqECrrRO7YlG6apwtGvTJyu5MO7pdyMQSRAyHgPZkJTZI8YLeKiEZF7Ajma\neoENrmhmb5Xu8y/PlfdHUK+X/5ymPugav9IAZpAEx/DaPfrBcRcIdVdJsS9S\n3dh1wq+4YYycR7RE7/9ZrJfwfAFXOafxhKBdovD7npjXpqvzdzXtYc257FXm\n1Y28CgaW5slwHPg3EpewOV63MABukAu5wDmI8laGnl82jlbMOVjhm3B5ch6Y\nFCuGJGj5C2+heA1boFFtW0oSg3Lqf49MFrFYkwUXoTVSw04IRo+5YM3xBawL\n3pv20ksc/PIQh/qGUhsqMSOmQEaIfLkVlGxw4JHLH1fWTmTbpFXqTo3t9jRn\nPS9SxywCZJs+oneNyVVdEqnQrjPK5ZLpw8543WGy5DPC/rUOeayNhuuJ1fAh\nvM8IRn7PldG4oU9sa+E66OGapINuLkLvqRosKBn/MKQl2et/gqru77cYichm\nso+I/lC2kfgy+3N1kEebCfHaOQYdLRwkx2bO+rfKQvnPqj8mvM9SGRxJtVNf\ns2F94EEtUh5xRm5Sg9zyUuAW62Owc7o2/x5xRjmwdJRqzmzKfJ0537gsm1Dn\ne2Pl\r\n=uhTM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "9bbe486c5efcc356fb4d189ef38eee275bbde345", "scripts": {"test": "nyc --reporter=text --reporter=lcov npm run mocha", "mocha": "mocha test/*-test.js", "version": "auto-changelog -p && git add CHANGELOG.md"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/http-party/node-http-proxy.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"eventemitter3": "^4.0.0", "requires-port": "^1.0.0", "follow-redirects": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "nyc": "^14.0.0", "sse": "0.0.8", "async": "^3.0.0", "mocha": "^3.5.3", "semver": "^5.0.3", "expect.js": "~0.3.1", "socket.io": "^2.1.0", "concat-stream": "^2.0.0", "auto-changelog": "^1.15.0", "socket.io-client": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy_1.18.0_1568770869081_0.9804964669439871", "host": "s3://npm-registry-packages"}}, "1.18.1": {"name": "http-proxy", "version": "1.18.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-proxy@1.18.1", "maintainers": [{"name": "cronopio", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}], "homepage": "https://github.com/http-party/node-http-proxy#readme", "bugs": {"url": "https://github.com/http-party/node-http-proxy/issues"}, "dist": {"shasum": "401541f0534884bbf95260334e72f88ee3976549", "tarball": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz", "fileCount": 16, "integrity": "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==", "signatures": [{"sig": "MEYCIQDot+vgPgzzVsOumrniXVGuF46coDb8gqyQsRR8jD3OTgIhAIF+fMwqUEYJZ20PpJH5UkW3RPxubSrJnycL3zE6fKzf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 231812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewaxFCRA9TVsSAnZWagAA+lIQAI1unze4a1LlWRns8e4c\nGlvLDeTCOq+wSHTgRvJXOpHyt+3x7mulAxkLrgtKOUPQ1hSb1SLJmlQcVHfh\nMpxfgHUufP2vSBIwDaM1J0VQj92sMovQvAhiSjK8rBdBfL1e8SY7vtRabF+A\nEXrKC3k94+p3enSVK0bIgGypJLfhGYTo/6mSlpfVO5hV1E4qIU3c4eL/M/p5\nFlfwEss1ASreRUy23/3oWJdnylEUJM5/BROiW8nnZ+DOWZZmdOktrdB9tPjA\n93i2TTvIPcNW4MVpwF2andNrPffCBocfvbfVLjbSwK8jY7wZhm325lzng9wO\nHLCrX2hiRe6HqYyGrCKFmWzzh5RpBa3/X5j7RFhCLYpDp2tQMoyWnXUj1Kwl\n/cA3jnc7pipeLPnI2ay0wre4mxo9kgGq/SlJxF82AOuN19x/XhXY6FqIdkeu\ndWEE32/8BnjVXEY8lVmEeObD+YEESufvtWhqJnwMPcL2le6k+fr3WBw8SUvI\nj64sA+44iNOPPcOr2xZOO/yIWovwtsT2tvonunHYTIdmCHwqGma2/8xBxrqH\nSClvDxKkexgQw59sdWiruGw0iQoilW2fAmCPSowYI52zojEA6c/iBoJbljUa\ns+WanFKm8fo+KfWSRpDBKTE3Tprbe+yHPXBtGjXpbIekq8p7QjsaLyPt+FOA\nwoEv\r\n=Z69L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8.0.0"}, "gitHead": "9b96cd725127a024dabebec6c7ea8c807272223d", "scripts": {"test": "nyc --reporter=text --reporter=lcov npm run mocha", "mocha": "mocha test/*-test.js", "version": "auto-changelog -p && git add CHANGELOG.md"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/http-party/node-http-proxy.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "HTTP proxying for the masses", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"eventemitter3": "^4.0.0", "requires-port": "^1.0.0", "follow-redirects": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "nyc": "^14.0.0", "sse": "0.0.8", "async": "^3.0.0", "mocha": "^3.5.3", "semver": "^5.0.3", "expect.js": "~0.3.1", "socket.io": "^2.1.0", "concat-stream": "^2.0.0", "auto-changelog": "^1.15.0", "socket.io-client": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-proxy_1.18.1_1589750852626_0.652572838441352", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-03-20T18:37:42.115Z", "modified": "2024-12-13T19:06:02.075Z", "0.3.1": "2011-03-20T18:37:42.115Z", "0.2.0": "2011-03-20T18:37:42.115Z", "0.4.0": "2011-03-20T18:37:42.115Z", "0.3.0": "2011-03-20T18:37:42.115Z", "0.1.5": "2011-03-20T18:37:42.115Z", "0.4.1": "2011-03-20T21:42:39.710Z", "0.4.2": "2011-04-13T21:24:43.807Z", "0.5.0": "2011-04-18T01:37:47.127Z", "0.5.1": "2011-05-10T22:31:25.797Z", "0.5.2": "2011-05-17T22:40:59.948Z", "0.5.3": "2011-05-18T01:38:07.066Z", "0.5.4": "2011-05-19T01:10:09.614Z", "0.5.5": "2011-05-19T04:38:37.820Z", "0.5.6": "2011-05-19T06:00:15.396Z", "0.5.7": "2011-05-19T06:46:03.717Z", "0.5.8": "2011-05-21T14:43:40.621Z", "0.5.9": "2011-05-23T06:19:07.714Z", "0.5.10": "2011-06-13T06:53:20.983Z", "0.5.11": "2011-06-26T17:26:08.499Z", "0.6.0": "2011-07-26T01:21:02.357Z", "0.6.1": "2011-08-02T14:08:09.011Z", "0.7.0": "2011-08-04T13:00:37.432Z", "0.6.2": "2011-08-09T10:35:13.520Z", "0.6.4": "2011-08-28T23:34:01.455Z", "0.6.5": "2011-08-29T01:15:15.681Z", "0.6.6": "2011-08-31T15:49:12.708Z", "0.7.2": "2011-09-30T08:22:30.123Z", "0.7.3": "2011-10-04T19:18:15.451Z", "0.7.4": "2011-11-10T06:01:09.076Z", "0.7.5": "2011-11-11T01:28:13.015Z", "0.7.6": "2011-11-14T20:44:10.580Z", "0.8.0": "2011-12-23T06:42:46.315Z", "0.8.1": "2012-06-05T22:04:55.630Z", "0.0.0": "2012-06-09T04:39:14.601Z", "0.8.2": "2012-07-22T21:25:11.761Z", "0.8.3": "2012-09-20T15:58:56.473Z", "0.8.4": "2012-10-23T20:03:32.147Z", "0.8.5": "2012-11-16T05:22:12.255Z", "0.8.6": "2012-12-21T15:53:23.000Z", "0.8.7": "2012-12-23T00:02:31.258Z", "0.9.0": "2013-03-09T10:00:18.512Z", "0.9.1": "2013-03-09T10:09:39.807Z", "0.10.0": "2013-03-18T05:53:33.812Z", "0.10.1": "2013-04-12T08:55:48.088Z", "0.10.2": "2013-04-21T21:07:15.779Z", "0.10.3": "2013-06-20T13:29:53.381Z", "0.10.4": "2013-12-27T08:08:12.488Z", "1.0.0": "2014-01-17T17:40:53.312Z", "1.0.1": "2014-01-17T21:20:42.852Z", "1.0.2": "2014-01-28T19:53:09.257Z", "1.0.3": "2014-03-27T02:41:54.073Z", "1.1.0": "2014-04-09T17:38:47.920Z", "1.1.1": "2014-04-11T00:03:08.371Z", "1.1.2": "2014-04-14T17:17:56.828Z", "1.1.3": "2014-05-11T05:03:07.262Z", "1.1.4": "2014-05-11T23:02:33.492Z", "1.1.5": "2014-07-10T03:26:58.943Z", "1.1.6": "2014-07-17T14:56:54.267Z", "1.2.0": "2014-08-05T21:26:15.190Z", "1.2.1": "2014-08-14T17:39:24.604Z", "1.3.0": "2014-08-14T21:26:42.455Z", "1.3.1": "2014-09-09T17:20:29.027Z", "1.4.0": "2014-09-11T22:49:43.531Z", "1.4.1": "2014-09-11T22:50:22.495Z", "1.4.2": "2014-09-12T11:48:42.965Z", "1.4.3": "2014-09-12T17:39:53.684Z", "1.5.0": "2014-09-30T02:39:19.553Z", "1.5.1": "2014-09-30T19:23:19.995Z", "1.5.2": "2014-10-01T01:21:22.695Z", "1.5.3": "2014-10-01T11:11:08.444Z", "1.6.0": "2014-10-29T02:55:12.833Z", "1.6.1": "2014-11-04T23:15:42.020Z", "1.6.2": "2014-11-11T16:48:00.045Z", "1.7.0": "2014-11-25T22:32:18.245Z", "1.7.1": "2014-12-02T17:29:08.462Z", "1.7.2": "2014-12-08T21:17:23.000Z", "1.7.3": "2014-12-09T04:06:21.442Z", "1.8.0": "2014-12-17T07:59:53.862Z", "1.8.1": "2014-12-17T17:13:20.879Z", "1.9.0": "2015-03-12T22:59:35.397Z", "1.9.1": "2015-04-01T16:09:50.130Z", "1.10.0": "2015-04-01T16:24:30.797Z", "1.10.1": "2015-04-02T16:41:11.463Z", "1.11.0": "2015-04-20T20:48:19.302Z", "1.11.1": "2015-04-22T15:09:42.215Z", "1.11.2": "2015-08-30T21:29:34.435Z", "1.11.3": "2015-10-19T13:30:29.496Z", "1.12.0": "2015-10-22T23:27:39.971Z", "1.12.1": "2016-01-24T20:20:39.588Z", "1.13.0": "2016-01-26T22:12:02.896Z", "1.13.1": "2016-02-02T05:03:05.566Z", "1.13.2": "2016-02-17T16:00:11.846Z", "1.13.3": "2016-05-16T03:14:58.875Z", "1.14.0": "2016-06-15T14:53:05.632Z", "1.15.0": "2016-09-14T17:06:05.133Z", "1.15.1": "2016-09-14T21:12:38.554Z", "1.15.2": "2016-10-22T15:47:30.866Z", "1.16.0": "2016-12-02T14:13:19.709Z", "1.16.1": "2016-12-04T15:59:51.283Z", "1.16.2": "2016-12-06T15:49:10.117Z", "1.17.0": "2018-04-20T15:43:09.143Z", "1.18.0": "2019-09-18T01:41:09.233Z", "1.18.1": "2020-05-17T21:27:32.770Z"}, "bugs": {"url": "https://github.com/http-party/node-http-proxy/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/http-party/node-http-proxy#readme", "repository": {"url": "git+https://github.com/http-party/node-http-proxy.git", "type": "git"}, "description": "HTTP proxying for the masses", "maintainers": [{"name": "cronopio", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "yawnt", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n  <img src=\"https://raw.github.com/http-party/node-http-proxy/master/doc/logo.png\"/>\n</p>\n\n# node-http-proxy [![Build Status](https://travis-ci.org/http-party/node-http-proxy.svg?branch=master)](https://travis-ci.org/http-party/node-http-proxy) [![codecov](https://codecov.io/gh/http-party/node-http-proxy/branch/master/graph/badge.svg)](https://codecov.io/gh/http-party/node-http-proxy)\n\n`node-http-proxy` is an HTTP programmable proxying library that supports\nwebsockets. It is suitable for implementing components such as reverse\nproxies and load balancers.\n\n### Table of Contents\n  * [Installation](#installation)\n  * [Upgrading from 0.8.x ?](#upgrading-from-08x-)\n  * [Core Concept](#core-concept)\n  * [Use Cases](#use-cases)\n    * [Setup a basic stand-alone proxy server](#setup-a-basic-stand-alone-proxy-server)\n    * [Setup a stand-alone proxy server with custom server logic](#setup-a-stand-alone-proxy-server-with-custom-server-logic)\n    * [Setup a stand-alone proxy server with proxy request header re-writing](#setup-a-stand-alone-proxy-server-with-proxy-request-header-re-writing)\n    * [Modify a response from a proxied server](#modify-a-response-from-a-proxied-server)\n    * [Setup a stand-alone proxy server with latency](#setup-a-stand-alone-proxy-server-with-latency)\n    * [Using HTTPS](#using-https)\n    * [Proxying WebSockets](#proxying-websockets)\n  * [Options](#options)\n  * [Listening for proxy events](#listening-for-proxy-events)\n  * [Shutdown](#shutdown)\n  * [Miscellaneous](#miscellaneous)\n    * [Test](#test)\n    * [ProxyTable API](#proxytable-api)\n    * [Logo](#logo)\n  * [Contributing and Issues](#contributing-and-issues)\n  * [License](#license)\n\n### Installation\n\n`npm install http-proxy --save`\n\n**[Back to top](#table-of-contents)**\n\n### Upgrading from 0.8.x ?\n\nClick [here](UPGRADING.md)\n\n**[Back to top](#table-of-contents)**\n\n### Core Concept\n\nA new proxy is created by calling `createProxyServer` and passing\nan `options` object as argument ([valid properties are available here](lib/http-proxy.js#L26-L42))\n\n```javascript\nvar httpProxy = require('http-proxy');\n\nvar proxy = httpProxy.createProxyServer(options); // See (†)\n```\n†Unless listen(..) is invoked on the object, this does not create a webserver. See below.\n\nAn object will be returned with four methods:\n\n* web `req, res, [options]` (used for proxying regular HTTP(S) requests)\n* ws `req, socket, head, [options]` (used for proxying WS(S) requests)\n* listen `port` (a function that wraps the object in a webserver, for your convenience)\n* close `[callback]` (a function that closes the inner webserver and stops listening on given port)\n\nIt is then possible to proxy requests by calling these functions\n\n```javascript\nhttp.createServer(function(req, res) {\n  proxy.web(req, res, { target: 'http://mytarget.com:8080' });\n});\n```\n\nErrors can be listened on either using the Event Emitter API\n\n```javascript\nproxy.on('error', function(e) {\n  ...\n});\n```\n\nor using the callback API\n\n```javascript\nproxy.web(req, res, { target: 'http://mytarget.com:8080' }, function(e) { ... });\n```\n\nWhen a request is proxied it follows two different pipelines ([available here](lib/http-proxy/passes))\nwhich apply transformations to both the `req` and `res` object.\nThe first pipeline (incoming) is responsible for the creation and manipulation of the stream that connects your client to the target.\nThe second pipeline (outgoing) is responsible for the creation and manipulation of the stream that, from your target, returns data\nto the client.\n\n**[Back to top](#table-of-contents)**\n\n### Use Cases\n\n#### Setup a basic stand-alone proxy server\n\n```js\nvar http = require('http'),\n    httpProxy = require('http-proxy');\n//\n// Create your proxy server and set the target in the options.\n//\nhttpProxy.createProxyServer({target:'http://localhost:9000'}).listen(8000); // See (†)\n\n//\n// Create your target server\n//\nhttp.createServer(function (req, res) {\n  res.writeHead(200, { 'Content-Type': 'text/plain' });\n  res.write('request successfully proxied!' + '\\n' + JSON.stringify(req.headers, true, 2));\n  res.end();\n}).listen(9000);\n```\n†Invoking listen(..) triggers the creation of a web server. Otherwise, just the proxy instance is created.\n\n**[Back to top](#table-of-contents)**\n\n#### Setup a stand-alone proxy server with custom server logic\nThis example shows how you can proxy a request using your own HTTP server\nand also you can put your own logic to handle the request.\n\n```js\nvar http = require('http'),\n    httpProxy = require('http-proxy');\n\n//\n// Create a proxy server with custom application logic\n//\nvar proxy = httpProxy.createProxyServer({});\n\n//\n// Create your custom server and just call `proxy.web()` to proxy\n// a web request to the target passed in the options\n// also you can use `proxy.ws()` to proxy a websockets request\n//\nvar server = http.createServer(function(req, res) {\n  // You can define here your custom logic to handle the request\n  // and then proxy the request.\n  proxy.web(req, res, { target: 'http://127.0.0.1:5050' });\n});\n\nconsole.log(\"listening on port 5050\")\nserver.listen(5050);\n```\n\n**[Back to top](#table-of-contents)**\n\n#### Setup a stand-alone proxy server with proxy request header re-writing\nThis example shows how you can proxy a request using your own HTTP server that\nmodifies the outgoing proxy request by adding a special header.\n\n```js\nvar http = require('http'),\n    httpProxy = require('http-proxy');\n\n//\n// Create a proxy server with custom application logic\n//\nvar proxy = httpProxy.createProxyServer({});\n\n// To modify the proxy connection before data is sent, you can listen\n// for the 'proxyReq' event. When the event is fired, you will receive\n// the following arguments:\n// (http.ClientRequest proxyReq, http.IncomingMessage req,\n//  http.ServerResponse res, Object options). This mechanism is useful when\n// you need to modify the proxy request before the proxy connection\n// is made to the target.\n//\nproxy.on('proxyReq', function(proxyReq, req, res, options) {\n  proxyReq.setHeader('X-Special-Proxy-Header', 'foobar');\n});\n\nvar server = http.createServer(function(req, res) {\n  // You can define here your custom logic to handle the request\n  // and then proxy the request.\n  proxy.web(req, res, {\n    target: 'http://127.0.0.1:5050'\n  });\n});\n\nconsole.log(\"listening on port 5050\")\nserver.listen(5050);\n```\n\n**[Back to top](#table-of-contents)**\n\n#### Modify a response from a proxied server\nSometimes when you have received a HTML/XML document from the server of origin you would like to modify it before forwarding it on.\n\n[Harmon](https://github.com/No9/harmon) allows you to do this in a streaming style so as to keep the pressure on the proxy to a minimum.\n\n**[Back to top](#table-of-contents)**\n\n#### Setup a stand-alone proxy server with latency\n\n```js\nvar http = require('http'),\n    httpProxy = require('http-proxy');\n\n//\n// Create a proxy server with latency\n//\nvar proxy = httpProxy.createProxyServer();\n\n//\n// Create your server that makes an operation that waits a while\n// and then proxies the request\n//\nhttp.createServer(function (req, res) {\n  // This simulates an operation that takes 500ms to execute\n  setTimeout(function () {\n    proxy.web(req, res, {\n      target: 'http://localhost:9008'\n    });\n  }, 500);\n}).listen(8008);\n\n//\n// Create your target server\n//\nhttp.createServer(function (req, res) {\n  res.writeHead(200, { 'Content-Type': 'text/plain' });\n  res.write('request successfully proxied to: ' + req.url + '\\n' + JSON.stringify(req.headers, true, 2));\n  res.end();\n}).listen(9008);\n```\n\n**[Back to top](#table-of-contents)**\n\n#### Using HTTPS\nYou can activate the validation of a secure SSL certificate to the target connection (avoid self-signed certs), just set `secure: true` in the options.\n\n##### HTTPS -> HTTP\n\n```js\n//\n// Create the HTTPS proxy server in front of a HTTP server\n//\nhttpProxy.createServer({\n  target: {\n    host: 'localhost',\n    port: 9009\n  },\n  ssl: {\n    key: fs.readFileSync('valid-ssl-key.pem', 'utf8'),\n    cert: fs.readFileSync('valid-ssl-cert.pem', 'utf8')\n  }\n}).listen(8009);\n```\n\n##### HTTPS -> HTTPS\n\n```js\n//\n// Create the proxy server listening on port 443\n//\nhttpProxy.createServer({\n  ssl: {\n    key: fs.readFileSync('valid-ssl-key.pem', 'utf8'),\n    cert: fs.readFileSync('valid-ssl-cert.pem', 'utf8')\n  },\n  target: 'https://localhost:9010',\n  secure: true // Depends on your needs, could be false.\n}).listen(443);\n```\n\n##### HTTP -> HTTPS (using a PKCS12 client certificate)\n\n```js\n//\n// Create an HTTP proxy server with an HTTPS target\n//\nhttpProxy.createProxyServer({\n  target: {\n    protocol: 'https:',\n    host: 'my-domain-name',\n    port: 443,\n    pfx: fs.readFileSync('path/to/certificate.p12'),\n    passphrase: 'password',\n  },\n  changeOrigin: true,\n}).listen(8000);\n```\n\n**[Back to top](#table-of-contents)**\n\n#### Proxying WebSockets\nYou can activate the websocket support for the proxy using `ws:true` in the options.\n\n```js\n//\n// Create a proxy server for websockets\n//\nhttpProxy.createServer({\n  target: 'ws://localhost:9014',\n  ws: true\n}).listen(8014);\n```\n\nAlso you can proxy the websocket requests just calling the `ws(req, socket, head)` method.\n\n```js\n//\n// Setup our server to proxy standard HTTP requests\n//\nvar proxy = new httpProxy.createProxyServer({\n  target: {\n    host: 'localhost',\n    port: 9015\n  }\n});\nvar proxyServer = http.createServer(function (req, res) {\n  proxy.web(req, res);\n});\n\n//\n// Listen to the `upgrade` event and proxy the\n// WebSocket requests as well.\n//\nproxyServer.on('upgrade', function (req, socket, head) {\n  proxy.ws(req, socket, head);\n});\n\nproxyServer.listen(8015);\n```\n\n**[Back to top](#table-of-contents)**\n\n### Options\n\n`httpProxy.createProxyServer` supports the following options:\n\n*  **target**: url string to be parsed with the url module\n*  **forward**: url string to be parsed with the url module\n*  **agent**: object to be passed to http(s).request (see Node's [https agent](http://nodejs.org/api/https.html#https_class_https_agent) and [http agent](http://nodejs.org/api/http.html#http_class_http_agent) objects)\n*  **ssl**: object to be passed to https.createServer()\n*  **ws**: true/false, if you want to proxy websockets\n*  **xfwd**: true/false, adds x-forward headers\n*  **secure**: true/false, if you want to verify the SSL Certs\n*  **toProxy**: true/false, passes the absolute URL as the `path` (useful for proxying to proxies)\n*  **prependPath**: true/false, Default: true - specify whether you want to prepend the target's path to the proxy path\n*  **ignorePath**: true/false, Default: false - specify whether you want to ignore the proxy path of the incoming request (note: you will have to append / manually if required).\n*  **localAddress**: Local interface string to bind for outgoing connections\n*  **changeOrigin**: true/false, Default: false - changes the origin of the host header to the target URL\n*  **preserveHeaderKeyCase**: true/false, Default: false - specify whether you want to keep letter case of response header key\n*  **auth**: Basic authentication i.e. 'user:password' to compute an Authorization header.\n*  **hostRewrite**: rewrites the location hostname on (201/301/302/307/308) redirects.\n*  **autoRewrite**: rewrites the location host/port on (201/301/302/307/308) redirects based on requested host/port. Default: false.\n*  **protocolRewrite**: rewrites the location protocol on (201/301/302/307/308) redirects to 'http' or 'https'. Default: null.\n*  **cookieDomainRewrite**: rewrites domain of `set-cookie` headers. Possible values:\n   * `false` (default): disable cookie rewriting\n   * String: new domain, for example `cookieDomainRewrite: \"new.domain\"`. To remove the domain, use `cookieDomainRewrite: \"\"`.\n   * Object: mapping of domains to new domains, use `\"*\"` to match all domains.\n     For example keep one domain unchanged, rewrite one domain and remove other domains:\n     ```\n     cookieDomainRewrite: {\n       \"unchanged.domain\": \"unchanged.domain\",\n       \"old.domain\": \"new.domain\",\n       \"*\": \"\"\n     }\n     ```\n*  **cookiePathRewrite**: rewrites path of `set-cookie` headers. Possible values:\n   * `false` (default): disable cookie rewriting\n   * String: new path, for example `cookiePathRewrite: \"/newPath/\"`. To remove the path, use `cookiePathRewrite: \"\"`. To set path to root use `cookiePathRewrite: \"/\"`.\n   * Object: mapping of paths to new paths, use `\"*\"` to match all paths.\n     For example, to keep one path unchanged, rewrite one path and remove other paths:\n     ```\n     cookiePathRewrite: {\n       \"/unchanged.path/\": \"/unchanged.path/\",\n       \"/old.path/\": \"/new.path/\",\n       \"*\": \"\"\n     }\n     ```\n*  **headers**: object with extra headers to be added to target requests.\n*  **proxyTimeout**: timeout (in millis) for outgoing proxy requests\n*  **timeout**: timeout (in millis) for incoming requests\n*  **followRedirects**: true/false, Default: false - specify whether you want to follow redirects\n*  **selfHandleResponse** true/false, if set to true, none of the webOutgoing passes are called and it's your responsibility to appropriately return the response by listening and acting on the `proxyRes` event\n*  **buffer**: stream of data to send as the request body.  Maybe you have some middleware that consumes the request stream before proxying it on e.g.  If you read the body of a request into a field called 'req.rawbody' you could restream this field in the buffer option:\n\n    ```\n    'use strict';\n\n    const streamify = require('stream-array');\n    const HttpProxy = require('http-proxy');\n    const proxy = new HttpProxy();\n\n    module.exports = (req, res, next) => {\n\n      proxy.web(req, res, {\n        target: 'http://localhost:4003/',\n        buffer: streamify(req.rawBody)\n      }, next);\n\n    };\n    ```\n\n**NOTE:**\n`options.ws` and `options.ssl` are optional.\n`options.target` and `options.forward` cannot both be missing\n\nIf you are using the `proxyServer.listen` method, the following options are also applicable:\n\n *  **ssl**: object to be passed to https.createServer()\n *  **ws**: true/false, if you want to proxy websockets\n\n\n**[Back to top](#table-of-contents)**\n\n### Listening for proxy events\n\n* `error`: The error event is emitted if the request to the target fail. **We do not do any error handling of messages passed between client and proxy, and messages passed between proxy and target, so it is recommended that you listen on errors and handle them.**\n* `proxyReq`: This event is emitted before the data is sent. It gives you a chance to alter the proxyReq request object. Applies to \"web\" connections\n* `proxyReqWs`: This event is emitted before the data is sent. It gives you a chance to alter the proxyReq request object. Applies to \"websocket\" connections\n* `proxyRes`: This event is emitted if the request to the target got a response.\n* `open`: This event is emitted once the proxy websocket was created and piped into the target websocket.\n* `close`: This event is emitted once the proxy websocket was closed.\n* (DEPRECATED) `proxySocket`: Deprecated in favor of `open`.\n\n```js\nvar httpProxy = require('http-proxy');\n// Error example\n//\n// Http Proxy Server with bad target\n//\nvar proxy = httpProxy.createServer({\n  target:'http://localhost:9005'\n});\n\nproxy.listen(8005);\n\n//\n// Listen for the `error` event on `proxy`.\nproxy.on('error', function (err, req, res) {\n  res.writeHead(500, {\n    'Content-Type': 'text/plain'\n  });\n\n  res.end('Something went wrong. And we are reporting a custom error message.');\n});\n\n//\n// Listen for the `proxyRes` event on `proxy`.\n//\nproxy.on('proxyRes', function (proxyRes, req, res) {\n  console.log('RAW Response from the target', JSON.stringify(proxyRes.headers, true, 2));\n});\n\n//\n// Listen for the `open` event on `proxy`.\n//\nproxy.on('open', function (proxySocket) {\n  // listen for messages coming FROM the target here\n  proxySocket.on('data', hybiParseAndLogMessage);\n});\n\n//\n// Listen for the `close` event on `proxy`.\n//\nproxy.on('close', function (res, socket, head) {\n  // view disconnected websocket connections\n  console.log('Client disconnected');\n});\n```\n\n**[Back to top](#table-of-contents)**\n\n### Shutdown\n\n* When testing or running server within another program it may be necessary to close the proxy.\n* This will stop the proxy from accepting new connections.\n\n```js\nvar proxy = new httpProxy.createProxyServer({\n  target: {\n    host: 'localhost',\n    port: 1337\n  }\n});\n\nproxy.close();\n```\n\n**[Back to top](#table-of-contents)**\n\n### Miscellaneous\n\nIf you want to handle your own response after receiving the `proxyRes`, you can do\nso with `selfHandleResponse`. As you can see below, if you use this option, you\nare able to intercept and read the `proxyRes` but you must also make sure to\nreply to the `res` itself otherwise the original client will never receive any\ndata.\n\n### Modify response\n\n```js\n\n    var option = {\n      target: target,\n      selfHandleResponse : true\n    };\n    proxy.on('proxyRes', function (proxyRes, req, res) {\n        var body = [];\n        proxyRes.on('data', function (chunk) {\n            body.push(chunk);\n        });\n        proxyRes.on('end', function () {\n            body = Buffer.concat(body).toString();\n            console.log(\"res from proxied server:\", body);\n            res.end(\"my response to cli\");\n        });\n    });\n    proxy.web(req, res, option);\n\n\n```\n\n#### ProxyTable API\n\nA proxy table API is available through this add-on [module](https://github.com/donasaur/http-proxy-rules), which lets you define a set of rules to translate matching routes to target routes that the reverse proxy will talk to.\n\n#### Test\n\n```\n$ npm test\n```\n\n#### Logo\n\nLogo created by [Diego Pasquali](http://dribbble.com/diegopq)\n\n**[Back to top](#table-of-contents)**\n\n### Contributing and Issues\n\n* Read carefully our [Code Of Conduct](https://github.com/http-party/node-http-proxy/blob/master/CODE_OF_CONDUCT.md)\n* Search on Google/Github\n* If you can't find anything, open an issue\n* If you feel comfortable about fixing the issue, fork the repo\n* Commit to your local branch (which must be different from `master`)\n* Submit your Pull Request (be sure to include tests and update documentation)\n\n**[Back to top](#table-of-contents)**\n\n### License\n\n>The MIT License (MIT)\n>\n>Copyright (c) 2010 - 2016 Charlie Robbins, Jarrett Cruger & the Contributors.\n>\n>Permission is hereby granted, free of charge, to any person obtaining a copy\n>of this software and associated documentation files (the \"Software\"), to deal\n>in the Software without restriction, including without limitation the rights\n>to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n>copies of the Software, and to permit persons to whom the Software is\n>furnished to do so, subject to the following conditions:\n>\n>The above copyright notice and this permission notice shall be included in\n>all copies or substantial portions of the Software.\n>\n>THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n>IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n>FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n>AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n>LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n>OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n>THE SOFTWARE.\n", "readmeFilename": "README.md", "users": {"6174": true, "285858315": true, "306766053": true, "djk": true, "pid": true, "rsp": true, "xpk": true, "cedx": true, "hanq": true, "jsds": true, "kunl": true, "old9": true, "pgte": true, "tztz": true, "al123": true, "ginof": true, "haeck": true, "jruif": true, "laomu": true, "maiah": true, "panlw": true, "pilsy": true, "stany": true, "xunuo": true, "xyyjk": true, "ajduke": true, "bojand": true, "chosan": true, "chrisx": true, "daizch": true, "dkblay": true, "dudley": true, "egantz": true, "eins78": true, "giphoo": true, "iksnae": true, "itsakt": true, "itskdk": true, "joliva": true, "kastor": true, "leesei": true, "loulin": true, "monjer": true, "mr1024": true, "nuwaio": true, "pstoev": true, "sirrah": true, "tcoats": true, "vifird": true, "webbot": true, "ziflex": true, "asaupup": true, "asilvas": true, "devpaul": true, "drewigg": true, "hafiidz": true, "jerrywu": true, "jez9999": true, "jovinbm": true, "mygoare": true, "nogirev": true, "perrywu": true, "sachacr": true, "sahilsk": true, "sprying": true, "ssh0702": true, "staydan": true, "subchen": true, "touskar": true, "trusktr": true, "xfloops": true, "yanghcc": true, "youngmo": true, "ahvonenj": true, "alexkval": true, "amanvirk": true, "baishuiz": true, "dburdese": true, "dcpesses": true, "devalias": true, "dexteryy": true, "dgarlitt": true, "e23jiang": true, "elussich": true, "guilbill": true, "huangkai": true, "kuba0506": true, "leejefon": true, "leodutra": true, "maxzhang": true, "mickaelb": true, "plingply": true, "putaoshu": true, "redbe4rd": true, "sbrajesh": true, "senorsen": true, "ssljivic": true, "wangfeia": true, "xiaobing": true, "yinxulai": true, "abuelwafa": true, "adamkdean": true, "andrelion": true, "aquafadas": true, "ddkothari": true, "erichua23": true, "fgribreau": true, "guumaster": true, "hehaiyang": true, "l8niteowl": true, "markymark": true, "mojaray2k": true, "papasavva": true, "sqrtthree": true, "stone-jin": true, "swmoon203": true, "zhenzhong": true, "abdihaikal": true, "cheapsteak": true, "crisperdue": true, "isaacvitor": true, "joe5yellow": true, "jokesterfr": true, "jungae1000": true, "junjiansyu": true, "manikantag": true, "martinkuba": true, "mmascanlin": true, "rocket0191": true, "shuoshubao": true, "sourcesoft": true, "tszopinski": true, "xieranmaya": true, "amirmehmood": true, "arunrajmony": true, "coolhanddev": true, "craigpatten": true, "ericteng177": true, "hisabimbola": true, "jbdoumenjou": true, "kodekracker": true, "scytalezero": true, "synchronous": true, "undisclosed": true, "wangnan0610": true, "ww522413622": true, "zhangwentao": true, "brentonhouse": true, "danhodkinson": true, "dpjayasekara": true, "ivangaravito": true, "nickeltobias": true, "nikitenok_sl": true, "philiiiiiipp": true, "reecegoddard": true, "ristostevcev": true, "steve-jansen": true, "ataiemajid_63": true, "chhetrisushil": true, "danielpavelic": true, "jian263994241": true, "parkerproject": true, "program247365": true, "stone_breaker": true, "vishnuvathsan": true, "yinyongcom666": true, "natarajanmca11": true, "ryanthejuggler": true, "shanewholloway": true, "yazanrawashdeh": true, "andygreenegrass": true, "ys_sidson_aidson": true, "scott.m.sarsfield": true, "wolfgangschoeffel": true, "programmer.severson": true, "brunocarvalhodearaujo": true}}