0 verbose cli D:\New Folder\node.exe D:\New Folder\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:D:\New Folder\node_modules\npm\npmrc
4 silly config load:file:E:\Demo\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:E:\Demo\zhengwudemobase0722\%USERPROFILE%\npm-global\etc\npmrc
7 verbose title npm exec http-server -p 8000
8 verbose argv "exec" "--" "http-server" "-p" "8000"
9 verbose logfile logs-max:10 dir:E:\Demo\zhengwudemobase0722\%USERPROFILE%\npm-cache\_logs\2025-07-22T02_57_27_384Z-
10 verbose logfile E:\Demo\zhengwudemobase0722\%USERPROFILE%\npm-cache\_logs\2025-07-22T02_57_27_384Z-debug-0.log
11 silly logfile done cleaning log files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 http fetch GET 200 https://registry.npmjs.org/http-server 691ms (cache miss)
14 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
15 verbose Could not read global path E:\Demo\zhengwudemobase0722\%USERPROFILE%\npm-global, ignoring
16 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
17 http fetch GET 200 https://registry.npmjs.org/npm 1117ms
18 silly idealTree buildDeps
19 silly fetch manifest http-server@14.1.1
20 silly packumentCache full:https://registry.npmjs.org/http-server cache-miss
21 http fetch GET 200 https://registry.npmjs.org/http-server 249ms (cache miss)
22 silly packumentCache full:https://registry.npmjs.org/http-server set size:undefined disposed:false
23 silly placeDep ROOT http-server@14.1.1 OK for:  want: 14.1.1
24 silly fetch manifest he@^1.2.0
25 silly packumentCache full:https://registry.npmjs.org/he cache-miss
26 silly fetch manifest mime@^1.6.0
27 silly packumentCache full:https://registry.npmjs.org/mime cache-miss
28 silly fetch manifest chalk@^4.1.2
29 silly packumentCache full:https://registry.npmjs.org/chalk cache-miss
30 silly fetch manifest union@~0.5.0
31 silly packumentCache full:https://registry.npmjs.org/union cache-miss
32 silly fetch manifest corser@^2.0.1
33 silly packumentCache full:https://registry.npmjs.org/corser cache-miss
34 silly fetch manifest opener@^1.5.1
35 silly packumentCache full:https://registry.npmjs.org/opener cache-miss
36 silly fetch manifest minimist@^1.2.6
37 silly packumentCache full:https://registry.npmjs.org/minimist cache-miss
38 silly fetch manifest url-join@^4.0.1
39 silly packumentCache full:https://registry.npmjs.org/url-join cache-miss
40 silly fetch manifest basic-auth@^2.0.1
41 silly packumentCache full:https://registry.npmjs.org/basic-auth cache-miss
42 silly fetch manifest http-proxy@^1.18.1
43 silly packumentCache full:https://registry.npmjs.org/http-proxy cache-miss
44 silly fetch manifest portfinder@^1.0.28
45 silly packumentCache full:https://registry.npmjs.org/portfinder cache-miss
46 silly fetch manifest secure-compare@3.0.1
47 silly packumentCache full:https://registry.npmjs.org/secure-compare cache-miss
48 silly fetch manifest html-encoding-sniffer@^3.0.0
49 silly packumentCache full:https://registry.npmjs.org/html-encoding-sniffer cache-miss
50 http fetch GET 200 https://registry.npmjs.org/he 197ms (cache miss)
51 silly packumentCache full:https://registry.npmjs.org/he set size:undefined disposed:false
52 http fetch GET 200 https://registry.npmjs.org/mime 248ms (cache miss)
53 silly packumentCache full:https://registry.npmjs.org/mime set size:undefined disposed:false
54 http fetch GET 200 https://registry.npmjs.org/basic-auth 683ms (cache miss)
55 silly packumentCache full:https://registry.npmjs.org/basic-auth set size:undefined disposed:false
56 http fetch GET 200 https://registry.npmjs.org/corser 698ms (cache miss)
57 silly packumentCache full:https://registry.npmjs.org/corser set size:undefined disposed:false
58 http fetch GET 200 https://registry.npmjs.org/http-proxy 699ms (cache miss)
59 silly packumentCache full:https://registry.npmjs.org/http-proxy set size:undefined disposed:false
60 http fetch GET 200 https://registry.npmjs.org/opener 702ms (cache miss)
61 silly packumentCache full:https://registry.npmjs.org/opener set size:undefined disposed:false
62 http fetch GET 200 https://registry.npmjs.org/chalk 705ms (cache miss)
63 silly packumentCache full:https://registry.npmjs.org/chalk set size:undefined disposed:false
64 http fetch GET 200 https://registry.npmjs.org/html-encoding-sniffer 705ms (cache miss)
65 silly packumentCache full:https://registry.npmjs.org/html-encoding-sniffer set size:undefined disposed:false
66 http fetch GET 200 https://registry.npmjs.org/union 717ms (cache miss)
67 silly packumentCache full:https://registry.npmjs.org/union set size:undefined disposed:false
68 http fetch GET 200 https://registry.npmjs.org/portfinder 720ms (cache miss)
69 silly packumentCache full:https://registry.npmjs.org/portfinder set size:undefined disposed:false
70 http fetch GET 200 https://registry.npmjs.org/url-join 738ms (cache miss)
71 silly packumentCache full:https://registry.npmjs.org/url-join set size:undefined disposed:false
72 http fetch GET 200 https://registry.npmjs.org/secure-compare 751ms (cache miss)
73 silly packumentCache full:https://registry.npmjs.org/secure-compare set size:undefined disposed:false
74 http fetch GET 200 https://registry.npmjs.org/minimist 800ms (cache miss)
75 silly packumentCache full:https://registry.npmjs.org/minimist set size:undefined disposed:false
76 silly placeDep ROOT basic-auth@2.0.1 OK for: http-server@14.1.1 want: ^2.0.1
77 silly placeDep ROOT chalk@4.1.2 OK for: http-server@14.1.1 want: ^4.1.2
78 silly placeDep ROOT corser@2.0.1 OK for: http-server@14.1.1 want: ^2.0.1
79 silly placeDep ROOT he@1.2.0 OK for: http-server@14.1.1 want: ^1.2.0
80 silly placeDep ROOT html-encoding-sniffer@3.0.0 OK for: http-server@14.1.1 want: ^3.0.0
81 silly placeDep ROOT http-proxy@1.18.1 OK for: http-server@14.1.1 want: ^1.18.1
82 silly placeDep ROOT mime@1.6.0 OK for: http-server@14.1.1 want: ^1.6.0
83 silly placeDep ROOT minimist@1.2.8 OK for: http-server@14.1.1 want: ^1.2.6
84 silly placeDep ROOT opener@1.5.2 OK for: http-server@14.1.1 want: ^1.5.1
85 silly placeDep ROOT portfinder@1.0.37 OK for: http-server@14.1.1 want: ^1.0.28
86 silly placeDep ROOT secure-compare@3.0.1 OK for: http-server@14.1.1 want: 3.0.1
87 silly placeDep ROOT union@0.5.0 OK for: http-server@14.1.1 want: ~0.5.0
88 silly placeDep ROOT url-join@4.0.1 OK for: http-server@14.1.1 want: ^4.0.1
89 silly fetch manifest safe-buffer@5.1.2
90 silly packumentCache full:https://registry.npmjs.org/safe-buffer cache-miss
91 silly fetch manifest ansi-styles@^4.1.0
92 silly packumentCache full:https://registry.npmjs.org/ansi-styles cache-miss
93 silly fetch manifest supports-color@^7.1.0
94 silly packumentCache full:https://registry.npmjs.org/supports-color cache-miss
95 silly fetch manifest whatwg-encoding@^2.0.0
96 silly packumentCache full:https://registry.npmjs.org/whatwg-encoding cache-miss
97 silly fetch manifest eventemitter3@^4.0.0
98 silly packumentCache full:https://registry.npmjs.org/eventemitter3 cache-miss
99 silly fetch manifest requires-port@^1.0.0
100 silly packumentCache full:https://registry.npmjs.org/requires-port cache-miss
101 silly fetch manifest follow-redirects@^1.0.0
102 silly packumentCache full:https://registry.npmjs.org/follow-redirects cache-miss
103 silly fetch manifest async@^3.2.6
104 silly packumentCache full:https://registry.npmjs.org/async cache-miss
105 silly fetch manifest debug@^4.3.6
106 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
107 silly fetch manifest qs@^6.4.0
108 silly packumentCache full:https://registry.npmjs.org/qs cache-miss
109 http fetch GET 200 https://registry.npmjs.org/safe-buffer 199ms (cache miss)
110 silly packumentCache full:https://registry.npmjs.org/safe-buffer set size:undefined disposed:false
111 http fetch GET 200 https://registry.npmjs.org/requires-port 214ms (cache miss)
112 silly packumentCache full:https://registry.npmjs.org/requires-port set size:undefined disposed:false
113 http fetch GET 200 https://registry.npmjs.org/ansi-styles 216ms (cache miss)
114 silly packumentCache full:https://registry.npmjs.org/ansi-styles set size:undefined disposed:false
115 http fetch GET 200 https://registry.npmjs.org/follow-redirects 218ms (cache miss)
116 silly packumentCache full:https://registry.npmjs.org/follow-redirects set size:undefined disposed:false
117 http fetch GET 200 https://registry.npmjs.org/whatwg-encoding 221ms (cache miss)
118 silly packumentCache full:https://registry.npmjs.org/whatwg-encoding set size:undefined disposed:false
119 http fetch GET 200 https://registry.npmjs.org/eventemitter3 221ms (cache miss)
120 silly packumentCache full:https://registry.npmjs.org/eventemitter3 set size:undefined disposed:false
121 http fetch GET 200 https://registry.npmjs.org/qs 223ms (cache miss)
122 silly packumentCache full:https://registry.npmjs.org/qs set size:undefined disposed:false
123 http fetch GET 200 https://registry.npmjs.org/supports-color 226ms (cache miss)
124 silly packumentCache full:https://registry.npmjs.org/supports-color set size:undefined disposed:false
125 http fetch GET 200 https://registry.npmjs.org/debug 227ms (cache miss)
126 silly packumentCache full:https://registry.npmjs.org/debug set size:undefined disposed:false
127 http fetch GET 200 https://registry.npmjs.org/async 229ms (cache miss)
128 silly packumentCache full:https://registry.npmjs.org/async set size:undefined disposed:false
129 silly placeDep ROOT safe-buffer@5.1.2 OK for: basic-auth@2.0.1 want: 5.1.2
130 silly placeDep ROOT ansi-styles@4.3.0 OK for: chalk@4.1.2 want: ^4.1.0
131 silly placeDep ROOT supports-color@7.2.0 OK for: chalk@4.1.2 want: ^7.1.0
132 silly fetch manifest color-convert@^2.0.1
133 silly packumentCache full:https://registry.npmjs.org/color-convert cache-miss
134 silly fetch manifest has-flag@^4.0.0
135 silly packumentCache full:https://registry.npmjs.org/has-flag cache-miss
136 http fetch GET 200 https://registry.npmjs.org/color-convert 196ms (cache miss)
137 silly packumentCache full:https://registry.npmjs.org/color-convert set size:undefined disposed:false
138 http fetch GET 200 https://registry.npmjs.org/has-flag 216ms (cache miss)
139 silly packumentCache full:https://registry.npmjs.org/has-flag set size:undefined disposed:false
140 silly placeDep ROOT color-convert@2.0.1 OK for: ansi-styles@4.3.0 want: ^2.0.1
141 silly fetch manifest color-name@~1.1.4
142 silly packumentCache full:https://registry.npmjs.org/color-name cache-miss
143 http fetch GET 200 https://registry.npmjs.org/color-name 203ms (cache miss)
144 silly packumentCache full:https://registry.npmjs.org/color-name set size:undefined disposed:false
145 silly placeDep ROOT color-name@1.1.4 OK for: color-convert@2.0.1 want: ~1.1.4
146 silly placeDep ROOT whatwg-encoding@2.0.0 OK for: html-encoding-sniffer@3.0.0 want: ^2.0.0
147 silly fetch manifest iconv-lite@0.6.3
148 silly packumentCache full:https://registry.npmjs.org/iconv-lite cache-miss
149 http fetch GET 200 https://registry.npmjs.org/iconv-lite 199ms (cache miss)
150 silly packumentCache full:https://registry.npmjs.org/iconv-lite set size:undefined disposed:false
151 silly placeDep ROOT eventemitter3@4.0.7 OK for: http-proxy@1.18.1 want: ^4.0.0
152 silly placeDep ROOT follow-redirects@1.15.9 OK for: http-proxy@1.18.1 want: ^1.0.0
153 silly placeDep ROOT requires-port@1.0.0 OK for: http-proxy@1.18.1 want: ^1.0.0
154 silly placeDep ROOT async@3.2.6 OK for: portfinder@1.0.37 want: ^3.2.6
155 silly placeDep ROOT debug@4.4.1 OK for: portfinder@1.0.37 want: ^4.3.6
156 silly fetch manifest ms@^2.1.3
157 silly packumentCache full:https://registry.npmjs.org/ms cache-miss
158 http fetch GET 200 https://registry.npmjs.org/ms 229ms (cache miss)
159 silly packumentCache full:https://registry.npmjs.org/ms set size:undefined disposed:false
160 silly placeDep ROOT ms@2.1.3 OK for: debug@4.4.1 want: ^2.1.3
161 silly placeDep ROOT has-flag@4.0.0 OK for: supports-color@7.2.0 want: ^4.0.0
162 silly placeDep ROOT qs@6.14.0 OK for: union@0.5.0 want: ^6.4.0
163 silly fetch manifest side-channel@^1.1.0
164 silly packumentCache full:https://registry.npmjs.org/side-channel cache-miss
165 http fetch GET 200 https://registry.npmjs.org/side-channel 201ms (cache miss)
166 silly packumentCache full:https://registry.npmjs.org/side-channel set size:undefined disposed:false
167 silly placeDep ROOT side-channel@1.1.0 OK for: qs@6.14.0 want: ^1.1.0
168 silly fetch manifest es-errors@^1.3.0
169 silly packumentCache full:https://registry.npmjs.org/es-errors cache-miss
170 silly fetch manifest object-inspect@^1.13.3
171 silly packumentCache full:https://registry.npmjs.org/object-inspect cache-miss
172 silly fetch manifest side-channel-list@^1.0.0
173 silly packumentCache full:https://registry.npmjs.org/side-channel-list cache-miss
174 silly fetch manifest side-channel-map@^1.0.1
175 silly packumentCache full:https://registry.npmjs.org/side-channel-map cache-miss
176 silly fetch manifest side-channel-weakmap@^1.0.2
177 silly packumentCache full:https://registry.npmjs.org/side-channel-weakmap cache-miss
178 http fetch GET 200 https://registry.npmjs.org/side-channel-list 180ms (cache miss)
179 silly packumentCache full:https://registry.npmjs.org/side-channel-list set size:undefined disposed:false
180 http fetch GET 200 https://registry.npmjs.org/side-channel-map 192ms (cache miss)
181 silly packumentCache full:https://registry.npmjs.org/side-channel-map set size:undefined disposed:false
182 http fetch GET 200 https://registry.npmjs.org/side-channel-weakmap 192ms (cache miss)
183 silly packumentCache full:https://registry.npmjs.org/side-channel-weakmap set size:undefined disposed:false
184 http fetch GET 200 https://registry.npmjs.org/es-errors 193ms (cache miss)
185 silly packumentCache full:https://registry.npmjs.org/es-errors set size:undefined disposed:false
186 http fetch GET 200 https://registry.npmjs.org/object-inspect 204ms (cache miss)
187 silly packumentCache full:https://registry.npmjs.org/object-inspect set size:undefined disposed:false
188 silly placeDep ROOT es-errors@1.3.0 OK for: side-channel@1.1.0 want: ^1.3.0
189 silly placeDep ROOT object-inspect@1.13.4 OK for: side-channel@1.1.0 want: ^1.13.3
190 silly placeDep ROOT side-channel-list@1.0.0 OK for: side-channel@1.1.0 want: ^1.0.0
191 silly placeDep ROOT side-channel-map@1.0.1 OK for: side-channel@1.1.0 want: ^1.0.1
192 silly placeDep ROOT side-channel-weakmap@1.0.2 OK for: side-channel@1.1.0 want: ^1.0.2
193 silly fetch manifest call-bound@^1.0.2
194 silly packumentCache full:https://registry.npmjs.org/call-bound cache-miss
195 silly fetch manifest get-intrinsic@^1.2.5
196 silly packumentCache full:https://registry.npmjs.org/get-intrinsic cache-miss
197 silly fetch manifest call-bound@^1.0.2
198 silly packumentCache full:https://registry.npmjs.org/call-bound cache-miss
199 silly fetch manifest get-intrinsic@^1.2.5
200 silly packumentCache full:https://registry.npmjs.org/get-intrinsic cache-miss
201 http fetch GET 200 https://registry.npmjs.org/call-bound 195ms (cache miss)
202 silly packumentCache full:https://registry.npmjs.org/call-bound set size:undefined disposed:false
203 http fetch GET 200 https://registry.npmjs.org/get-intrinsic 196ms (cache miss)
204 silly packumentCache full:https://registry.npmjs.org/get-intrinsic set size:undefined disposed:false
205 http fetch GET 200 https://registry.npmjs.org/get-intrinsic 197ms (cache miss)
206 silly packumentCache full:https://registry.npmjs.org/get-intrinsic set size:undefined disposed:false
207 http fetch GET 200 https://registry.npmjs.org/call-bound 230ms (cache miss)
208 silly packumentCache full:https://registry.npmjs.org/call-bound set size:undefined disposed:false
209 silly placeDep ROOT call-bound@1.0.4 OK for: side-channel-map@1.0.1 want: ^1.0.2
210 silly placeDep ROOT get-intrinsic@1.3.0 OK for: side-channel-map@1.0.1 want: ^1.2.5
211 silly fetch manifest call-bind-apply-helpers@^1.0.2
212 silly packumentCache full:https://registry.npmjs.org/call-bind-apply-helpers cache-miss
213 silly fetch manifest get-intrinsic@^1.3.0
214 silly packumentCache full:https://registry.npmjs.org/get-intrinsic cache-miss
215 silly fetch manifest call-bind-apply-helpers@^1.0.2
216 silly packumentCache full:https://registry.npmjs.org/call-bind-apply-helpers cache-miss
217 silly fetch manifest es-define-property@^1.0.1
218 silly packumentCache full:https://registry.npmjs.org/es-define-property cache-miss
219 silly fetch manifest es-object-atoms@^1.1.1
220 silly packumentCache full:https://registry.npmjs.org/es-object-atoms cache-miss
221 silly fetch manifest function-bind@^1.1.2
222 silly packumentCache full:https://registry.npmjs.org/function-bind cache-miss
223 silly fetch manifest get-proto@^1.0.1
224 silly packumentCache full:https://registry.npmjs.org/get-proto cache-miss
225 silly fetch manifest gopd@^1.2.0
226 silly packumentCache full:https://registry.npmjs.org/gopd cache-miss
227 silly fetch manifest has-symbols@^1.1.0
228 silly packumentCache full:https://registry.npmjs.org/has-symbols cache-miss
229 silly fetch manifest hasown@^2.0.2
230 silly packumentCache full:https://registry.npmjs.org/hasown cache-miss
231 silly fetch manifest math-intrinsics@^1.1.0
232 silly packumentCache full:https://registry.npmjs.org/math-intrinsics cache-miss
233 http cache https://registry.npmjs.org/get-intrinsic 15ms (cache hit)
234 silly packumentCache full:https://registry.npmjs.org/get-intrinsic set size:57687 disposed:false
235 http fetch GET 200 https://registry.npmjs.org/es-define-property 206ms (cache miss)
236 silly packumentCache full:https://registry.npmjs.org/es-define-property set size:undefined disposed:false
237 http fetch GET 200 https://registry.npmjs.org/hasown 204ms (cache miss)
238 silly packumentCache full:https://registry.npmjs.org/hasown set size:undefined disposed:false
239 http fetch GET 200 https://registry.npmjs.org/math-intrinsics 206ms (cache miss)
240 silly packumentCache full:https://registry.npmjs.org/math-intrinsics set size:undefined disposed:false
241 http fetch GET 200 https://registry.npmjs.org/has-symbols 206ms (cache miss)
242 silly packumentCache full:https://registry.npmjs.org/has-symbols set size:undefined disposed:false
243 http fetch GET 200 https://registry.npmjs.org/get-proto 208ms (cache miss)
244 silly packumentCache full:https://registry.npmjs.org/get-proto set size:undefined disposed:false
245 http fetch GET 200 https://registry.npmjs.org/es-object-atoms 210ms (cache miss)
246 silly packumentCache full:https://registry.npmjs.org/es-object-atoms set size:undefined disposed:false
247 http fetch GET 200 https://registry.npmjs.org/gopd 209ms (cache miss)
248 silly packumentCache full:https://registry.npmjs.org/gopd set size:undefined disposed:false
249 http fetch GET 200 https://registry.npmjs.org/function-bind 211ms (cache miss)
250 silly packumentCache full:https://registry.npmjs.org/function-bind set size:undefined disposed:false
251 http fetch GET 200 https://registry.npmjs.org/call-bind-apply-helpers 216ms (cache miss)
252 silly packumentCache full:https://registry.npmjs.org/call-bind-apply-helpers set size:undefined disposed:false
253 http fetch GET 200 https://registry.npmjs.org/call-bind-apply-helpers 224ms (cache miss)
254 silly packumentCache full:https://registry.npmjs.org/call-bind-apply-helpers set size:undefined disposed:false
255 silly placeDep ROOT call-bind-apply-helpers@1.0.2 OK for: call-bound@1.0.4 want: ^1.0.2
256 silly placeDep ROOT function-bind@1.1.2 OK for: call-bind-apply-helpers@1.0.2 want: ^1.1.2
257 silly placeDep ROOT es-define-property@1.0.1 OK for: get-intrinsic@1.3.0 want: ^1.0.1
258 silly placeDep ROOT es-object-atoms@1.1.1 OK for: get-intrinsic@1.3.0 want: ^1.1.1
259 silly placeDep ROOT get-proto@1.0.1 OK for: get-intrinsic@1.3.0 want: ^1.0.1
260 silly placeDep ROOT gopd@1.2.0 OK for: get-intrinsic@1.3.0 want: ^1.2.0
261 silly placeDep ROOT has-symbols@1.1.0 OK for: get-intrinsic@1.3.0 want: ^1.1.0
262 silly placeDep ROOT hasown@2.0.2 OK for: get-intrinsic@1.3.0 want: ^2.0.2
263 silly placeDep ROOT math-intrinsics@1.1.0 OK for: get-intrinsic@1.3.0 want: ^1.1.0
264 silly fetch manifest dunder-proto@^1.0.1
265 silly packumentCache full:https://registry.npmjs.org/dunder-proto cache-miss
266 http fetch GET 200 https://registry.npmjs.org/dunder-proto 225ms (cache miss)
267 silly packumentCache full:https://registry.npmjs.org/dunder-proto set size:undefined disposed:false
268 silly placeDep ROOT dunder-proto@1.0.1 OK for: get-proto@1.0.1 want: ^1.0.1
269 silly placeDep ROOT iconv-lite@0.6.3 OK for: whatwg-encoding@2.0.0 want: 0.6.3
270 silly fetch manifest safer-buffer@>= 2.1.2 < 3.0.0
271 silly packumentCache full:https://registry.npmjs.org/safer-buffer cache-miss
272 http fetch GET 200 https://registry.npmjs.org/safer-buffer 215ms (cache miss)
273 silly packumentCache full:https://registry.npmjs.org/safer-buffer set size:undefined disposed:false
274 silly placeDep ROOT safer-buffer@2.1.2 OK for: iconv-lite@0.6.3 want: >= 2.1.2 < 3.0.0
275 silly reify moves {}
276 silly audit bulk request {
276 silly audit   'http-server': [ '14.1.1' ],
276 silly audit   'basic-auth': [ '2.0.1' ],
276 silly audit   chalk: [ '4.1.2' ],
276 silly audit   corser: [ '2.0.1' ],
276 silly audit   he: [ '1.2.0' ],
276 silly audit   'html-encoding-sniffer': [ '3.0.0' ],
276 silly audit   'http-proxy': [ '1.18.1' ],
276 silly audit   mime: [ '1.6.0' ],
276 silly audit   minimist: [ '1.2.8' ],
276 silly audit   opener: [ '1.5.2' ],
276 silly audit   portfinder: [ '1.0.37' ],
276 silly audit   'secure-compare': [ '3.0.1' ],
276 silly audit   union: [ '0.5.0' ],
276 silly audit   'url-join': [ '4.0.1' ],
276 silly audit   'safe-buffer': [ '5.1.2' ],
276 silly audit   'ansi-styles': [ '4.3.0' ],
276 silly audit   'supports-color': [ '7.2.0' ],
276 silly audit   'color-convert': [ '2.0.1' ],
276 silly audit   'color-name': [ '1.1.4' ],
276 silly audit   'whatwg-encoding': [ '2.0.0' ],
276 silly audit   eventemitter3: [ '4.0.7' ],
276 silly audit   'follow-redirects': [ '1.15.9' ],
276 silly audit   'requires-port': [ '1.0.0' ],
276 silly audit   async: [ '3.2.6' ],
276 silly audit   debug: [ '4.4.1' ],
276 silly audit   ms: [ '2.1.3' ],
276 silly audit   'has-flag': [ '4.0.0' ],
276 silly audit   qs: [ '6.14.0' ],
276 silly audit   'side-channel': [ '1.1.0' ],
276 silly audit   'es-errors': [ '1.3.0' ],
276 silly audit   'object-inspect': [ '1.13.4' ],
276 silly audit   'side-channel-list': [ '1.0.0' ],
276 silly audit   'side-channel-map': [ '1.0.1' ],
276 silly audit   'side-channel-weakmap': [ '1.0.2' ],
276 silly audit   'call-bound': [ '1.0.4' ],
276 silly audit   'get-intrinsic': [ '1.3.0' ],
276 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
276 silly audit   'function-bind': [ '1.1.2' ],
276 silly audit   'es-define-property': [ '1.0.1' ],
276 silly audit   'es-object-atoms': [ '1.1.1' ],
276 silly audit   'get-proto': [ '1.0.1' ],
276 silly audit   gopd: [ '1.2.0' ],
276 silly audit   'has-symbols': [ '1.1.0' ],
276 silly audit   hasown: [ '2.0.2' ],
276 silly audit   'math-intrinsics': [ '1.1.0' ],
276 silly audit   'dunder-proto': [ '1.0.1' ],
276 silly audit   'iconv-lite': [ '0.6.3' ],
276 silly audit   'safer-buffer': [ '2.1.2' ]
276 silly audit }
277 http cache safer-buffer@https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz 0ms (cache hit)
278 http cache iconv-lite@https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz 0ms (cache hit)
279 http cache math-intrinsics@https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz 0ms (cache hit)
280 http cache dunder-proto@https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz 0ms (cache hit)
281 http cache get-proto@https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz 0ms (cache hit)
282 http cache hasown@https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz 0ms (cache hit)
283 http cache has-symbols@https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz 0ms (cache hit)
284 http cache gopd@https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz 0ms (cache hit)
285 http cache get-intrinsic@https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz 0ms (cache hit)
286 http cache es-define-property@https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz 0ms (cache hit)
287 http cache function-bind@https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz 0ms (cache hit)
288 http cache call-bind-apply-helpers@https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz 0ms (cache hit)
289 http cache side-channel-weakmap@https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz 0ms (cache hit)
290 http cache call-bound@https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz 0ms (cache hit)
291 http cache side-channel-map@https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz 0ms (cache hit)
292 http cache side-channel-list@https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz 0ms (cache hit)
293 http cache object-inspect@https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz 0ms (cache hit)
294 http cache es-errors@https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz 0ms (cache hit)
295 http cache side-channel@https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz 0ms (cache hit)
296 http cache has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 0ms (cache hit)
297 http cache debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 0ms (cache hit)
298 http cache qs@https://registry.npmjs.org/qs/-/qs-6.14.0.tgz 0ms (cache hit)
299 http cache async@https://registry.npmjs.org/async/-/async-3.2.6.tgz 0ms (cache hit)
300 http cache ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 0ms (cache hit)
301 http cache requires-port@https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz 0ms (cache hit)
302 http cache follow-redirects@https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz 0ms (cache hit)
303 http cache eventemitter3@https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz 0ms (cache hit)
304 http cache es-object-atoms@https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz 0ms (cache hit)
305 http cache whatwg-encoding@https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz 0ms (cache hit)
306 http cache color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 0ms (cache hit)
307 http cache color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 0ms (cache hit)
308 http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 0ms (cache hit)
309 http cache safe-buffer@https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz 0ms (cache hit)
310 http cache url-join@https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz 0ms (cache hit)
311 http cache supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 0ms (cache hit)
312 http cache union@https://registry.npmjs.org/union/-/union-0.5.0.tgz 0ms (cache hit)
313 http cache secure-compare@https://registry.npmjs.org/secure-compare/-/secure-compare-3.0.1.tgz 0ms (cache hit)
314 http cache portfinder@https://registry.npmjs.org/portfinder/-/portfinder-1.0.37.tgz 0ms (cache hit)
315 http cache opener@https://registry.npmjs.org/opener/-/opener-1.5.2.tgz 0ms (cache hit)
316 http cache minimist@https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz 0ms (cache hit)
317 http cache mime@https://registry.npmjs.org/mime/-/mime-1.6.0.tgz 0ms (cache hit)
318 http cache html-encoding-sniffer@https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz 0ms (cache hit)
319 http cache http-proxy@https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz 0ms (cache hit)
320 http cache he@https://registry.npmjs.org/he/-/he-1.2.0.tgz 0ms (cache hit)
321 http cache corser@https://registry.npmjs.org/corser/-/corser-2.0.1.tgz 0ms (cache hit)
322 silly tarball no local data for safer-buffer@https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz. Extracting by manifest.
323 silly tarball no local data for iconv-lite@https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz. Extracting by manifest.
324 silly tarball no local data for math-intrinsics@https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz. Extracting by manifest.
325 http cache chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 0ms (cache hit)
326 silly tarball no local data for dunder-proto@https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz. Extracting by manifest.
327 http cache basic-auth@https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz 0ms (cache hit)
328 silly tarball no local data for get-proto@https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz. Extracting by manifest.
329 silly tarball no local data for hasown@https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz. Extracting by manifest.
330 silly tarball no local data for has-symbols@https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz. Extracting by manifest.
331 http cache http-server@https://registry.npmjs.org/http-server/-/http-server-14.1.1.tgz 0ms (cache hit)
332 silly tarball no local data for gopd@https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz. Extracting by manifest.
333 silly tarball no local data for get-intrinsic@https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz. Extracting by manifest.
334 silly tarball no local data for es-define-property@https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz. Extracting by manifest.
335 silly tarball no local data for function-bind@https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz. Extracting by manifest.
336 silly tarball no local data for call-bind-apply-helpers@https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz. Extracting by manifest.
337 silly tarball no local data for side-channel-weakmap@https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz. Extracting by manifest.
338 silly tarball no local data for call-bound@https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz. Extracting by manifest.
339 silly tarball no local data for side-channel-map@https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz. Extracting by manifest.
340 silly tarball no local data for side-channel-list@https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz. Extracting by manifest.
341 silly tarball no local data for object-inspect@https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz. Extracting by manifest.
342 silly tarball no local data for es-errors@https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz. Extracting by manifest.
343 silly tarball no local data for side-channel@https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz. Extracting by manifest.
344 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz. Extracting by manifest.
345 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz. Extracting by manifest.
346 silly tarball no local data for qs@https://registry.npmjs.org/qs/-/qs-6.14.0.tgz. Extracting by manifest.
347 silly tarball no local data for async@https://registry.npmjs.org/async/-/async-3.2.6.tgz. Extracting by manifest.
348 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz. Extracting by manifest.
349 silly tarball no local data for requires-port@https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz. Extracting by manifest.
350 silly tarball no local data for follow-redirects@https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz. Extracting by manifest.
351 silly tarball no local data for eventemitter3@https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz. Extracting by manifest.
352 silly tarball no local data for es-object-atoms@https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz. Extracting by manifest.
353 silly tarball no local data for whatwg-encoding@https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz. Extracting by manifest.
354 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz. Extracting by manifest.
355 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz. Extracting by manifest.
356 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz. Extracting by manifest.
357 silly tarball no local data for safe-buffer@https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz. Extracting by manifest.
358 silly tarball no local data for url-join@https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz. Extracting by manifest.
359 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz. Extracting by manifest.
360 silly tarball no local data for union@https://registry.npmjs.org/union/-/union-0.5.0.tgz. Extracting by manifest.
361 silly tarball no local data for secure-compare@https://registry.npmjs.org/secure-compare/-/secure-compare-3.0.1.tgz. Extracting by manifest.
362 silly tarball no local data for portfinder@https://registry.npmjs.org/portfinder/-/portfinder-1.0.37.tgz. Extracting by manifest.
363 silly tarball no local data for opener@https://registry.npmjs.org/opener/-/opener-1.5.2.tgz. Extracting by manifest.
364 silly tarball no local data for minimist@https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz. Extracting by manifest.
365 silly tarball no local data for mime@https://registry.npmjs.org/mime/-/mime-1.6.0.tgz. Extracting by manifest.
366 silly tarball no local data for html-encoding-sniffer@https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz. Extracting by manifest.
367 silly tarball no local data for http-proxy@https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz. Extracting by manifest.
368 silly tarball no local data for he@https://registry.npmjs.org/he/-/he-1.2.0.tgz. Extracting by manifest.
369 silly tarball no local data for corser@https://registry.npmjs.org/corser/-/corser-2.0.1.tgz. Extracting by manifest.
370 silly tarball no local data for chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz. Extracting by manifest.
371 silly tarball no local data for basic-auth@https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz. Extracting by manifest.
372 silly tarball no local data for http-server@https://registry.npmjs.org/http-server/-/http-server-14.1.1.tgz. Extracting by manifest.
373 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 229ms
374 silly audit report {}
375 http fetch GET 200 https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz 231ms (cache miss)
376 http fetch GET 200 https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz 238ms (cache miss)
377 http fetch GET 200 https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz 252ms (cache miss)
378 http fetch GET 200 https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz 253ms (cache miss)
379 http fetch GET 200 https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz 254ms (cache miss)
380 http fetch GET 200 https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz 255ms (cache miss)
381 http fetch GET 200 https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz 255ms (cache miss)
382 http fetch GET 200 https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz 261ms (cache miss)
383 http fetch GET 200 https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz 261ms (cache miss)
384 http fetch GET 200 https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz 264ms (cache miss)
385 http fetch GET 200 https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz 275ms (cache miss)
386 http fetch GET 200 https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz 394ms (cache miss)
387 http fetch GET 200 https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz 393ms (cache miss)
388 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 397ms (cache miss)
389 http fetch GET 200 https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz 407ms (cache miss)
390 http fetch GET 200 https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz 407ms (cache miss)
391 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 408ms (cache miss)
392 http fetch GET 200 https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz 418ms (cache miss)
393 http fetch GET 200 https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz 430ms (cache miss)
394 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 431ms (cache miss)
395 http fetch GET 200 https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz 439ms (cache miss)
396 http fetch GET 200 https://registry.npmjs.org/qs/-/qs-6.14.0.tgz 442ms (cache miss)
397 http fetch GET 200 https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz 451ms (cache miss)
398 http fetch GET 200 https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz 570ms (cache miss)
399 http fetch GET 200 https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz 571ms (cache miss)
400 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 571ms (cache miss)
401 http fetch GET 200 https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz 588ms (cache miss)
402 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 596ms (cache miss)
403 http fetch GET 200 https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz 597ms (cache miss)
404 http fetch GET 200 https://registry.npmjs.org/secure-compare/-/secure-compare-3.0.1.tgz 598ms (cache miss)
405 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 602ms (cache miss)
406 http fetch GET 200 https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz 603ms (cache miss)
407 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 605ms (cache miss)
408 http fetch GET 200 https://registry.npmjs.org/portfinder/-/portfinder-1.0.37.tgz 623ms (cache miss)
409 http fetch GET 200 https://registry.npmjs.org/union/-/union-0.5.0.tgz 640ms (cache miss)
410 http fetch GET 200 https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz 719ms (cache miss)
411 http fetch GET 200 https://registry.npmjs.org/async/-/async-3.2.6.tgz 723ms (cache miss)
412 http fetch GET 200 https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz 727ms (cache miss)
413 http fetch GET 200 https://registry.npmjs.org/opener/-/opener-1.5.2.tgz 744ms (cache miss)
414 http fetch GET 200 https://registry.npmjs.org/mime/-/mime-1.6.0.tgz 753ms (cache miss)
415 http fetch GET 200 https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz 784ms (cache miss)
416 http fetch GET 200 https://registry.npmjs.org/corser/-/corser-2.0.1.tgz 787ms (cache miss)
417 http fetch GET 200 https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz 787ms (cache miss)
418 http fetch GET 200 https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 799ms (cache miss)
419 http fetch GET 200 https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz 804ms (cache miss)
420 http fetch GET 200 https://registry.npmjs.org/he/-/he-1.2.0.tgz 814ms (cache miss)
421 http fetch GET 200 https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz 815ms (cache miss)
422 http fetch GET 200 https://registry.npmjs.org/http-server/-/http-server-14.1.1.tgz 828ms (cache miss)
