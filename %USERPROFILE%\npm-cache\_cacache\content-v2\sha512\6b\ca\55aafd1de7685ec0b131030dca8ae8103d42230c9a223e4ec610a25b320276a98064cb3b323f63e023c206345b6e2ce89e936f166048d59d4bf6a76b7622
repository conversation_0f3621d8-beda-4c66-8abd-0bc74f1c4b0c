{"_id": "corser", "_rev": "32-40f1ab3dab0e7392411cb52b29bc0f21", "name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "dist-tags": {"latest": "2.0.1"}, "versions": {"0.1.0": {"name": "corser", "description": "A CORS-enabled HTTP reverse proxy.", "version": "0.1.0", "dependencies": {"optimist": "0.2.8"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/agrueneberg/Corser.git"}, "bin": {"corser": "./bin/corser"}, "scripts": {"start": "node bin/corser --port 80"}, "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "_id": "corser@0.1.0", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2", "_defaultsLoaded": true, "dist": {"shasum": "a48ee5c28ae58512b1cde09c22181036aac1d7d5", "tarball": "https://registry.npmjs.org/corser/-/corser-0.1.0.tgz", "integrity": "sha512-5cpCn0CY35E9nBe/8AW1pJA0Bzf721+RYocD3a5wtwhwLue2SHjlBmrPAeYJ40vpmbgBqM2h9lOF+/2TK+HgDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE1OiGWZ5KUGnigAvolKXNhg26P3PGv747HhcKXDU6sRAiEA1Fiqf/dGStEWAVxpd5IxoI6JEqD5SyuBTihk4wkbR/w="}]}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "corser", "description": "A CORS-enabled HTTP(S) reverse proxy.", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/agrueneberg/Corser.git"}, "bin": {"corser": "./bin/corser"}, "dependencies": {"optimist": "0.2.x"}, "engines": {"node": "0.4.x || 0.6.x"}, "scripts": {"start": "node bin/corser --port 80"}, "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "_id": "corser@0.2.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "37dd8221254da7826a63dc76735a28c4b770bed1", "tarball": "https://registry.npmjs.org/corser/-/corser-0.2.0.tgz", "integrity": "sha512-v6iDCuegvmZPd50FKP7mBaVkYvuPorj2wnK7YUtxGlwj0KPvycjWuRi9Zc9+mRncRaf9tf+Yd06VNcLJkisYzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICdPE+lnZ/bFpBcZTmRLqZ1jrVzQNcbXL3fgc8kWyp9DAiBjBibEv7s4Lcyb4/wHs9wNESbgLOV8F7D9Ep7Vks6FnA=="}]}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/agrueneberg/Corser.git"}, "main": "./lib/corser.js", "devDependencies": {"mocha": "0.11.x", "expect.js": "0.1.x"}, "engines": {"node": "0.4.x || 0.6.x"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "_id": "corser@1.0.0", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "cfa4cacb4a20dc58a430bac2c27b727115c60930", "tarball": "https://registry.npmjs.org/corser/-/corser-1.0.0.tgz", "integrity": "sha512-75fpEl4mHd8VYtFT3Ed4BUuAgHc4UkaGaXnqDfxssU6RwTKxt9EjbU6ETY0Ih4wt5lxyDpmaXenwpmkj1iedRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAgcGsoOfXl7M+x3Yfc/rGMtl2zuwRrisEcwGCA/OHcBAiEAmOP1ip3W5OT39hGWyqbfr+meumqDY4uksQlPOqYjN5E="}]}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/agrueneberg/Corser.git"}, "licenses": [{"type": "The MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}], "main": "./lib/corser.js", "devDependencies": {"mocha": "1.0.x", "expect.js": "0.1.x"}, "engines": {"node": "0.4.x || 0.6.x || 0.7.x"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "_id": "corser@1.0.1", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.15", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "58e36848893cd0176cf875a52655ff8995578cef", "tarball": "https://registry.npmjs.org/corser/-/corser-1.0.1.tgz", "integrity": "sha512-GPl9D85ydnQfSS8VKyp3F+9BWXTgxl74WWVjocg6JGyOTUCbY2cNUc7/EWbdcCtBVF36/E9qnykM+afoP6WoXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA4v6WzNDHqchF8Tp7WzJBbKgGQ+J31EkJil/Jh88UHNAiBVatAMSlKC0hn/QXt7IyhK+J6feujKSxM66QK7ZF5sHQ=="}]}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/agrueneberg/Corser"}, "licenses": [{"type": "The MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}], "main": "./lib/corser.js", "devDependencies": {"mocha": "1.2.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_id": "corser@1.0.2", "dist": {"shasum": "7f25889630a9a4f49f188bf42cd17c0c4f1425f8", "tarball": "https://registry.npmjs.org/corser/-/corser-1.0.2.tgz", "integrity": "sha512-4y3shk6yqcDKVbI35oN+kuNo5H8c2Njmuwzxjp2tDUhAVxAFFHAB99ju1c7iQwUg07vc2rADnOA6/oGwmXDaiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBza1OcK07LVkGKfVc/gW0IlyVrFfVmv2R1RBwsXsN6QIgbCt5Z5E5kEfswQWqdRP9QrWHssqhUvSJjiq4BNwlArc="}]}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/agrueneberg/Corser"}, "licenses": [{"type": "The MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}], "main": "./lib/corser.js", "devDependencies": {"mocha": "1.3.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_id": "corser@1.1.0", "dist": {"shasum": "90c648847e755e0c2f432b537c2d55415a651520", "tarball": "https://registry.npmjs.org/corser/-/corser-1.1.0.tgz", "integrity": "sha512-0qNjv1I1gQqSZA1VAp4nO+FUDRMIxE9/JyrgsrwxnDAko76czoBxJzKmXGv6Y415VUOKuvKYf55aX88QYDfDyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVm/KdXPWnbU+o6stul/6YA17Z98EtPBE5abnh+1aXbQIhAJ9OuFkyygT6WFTfq8KvPlNexmw3IOFwkKhur7COmoz7"}]}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/agrueneberg/Corser"}, "licenses": [{"type": "The MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}], "main": "./lib/corser.js", "devDependencies": {"mocha": "1.3.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_id": "corser@1.1.1", "dist": {"shasum": "9d80e5efd6f76b4e7c77ff32a91f1eb5caebb905", "tarball": "https://registry.npmjs.org/corser/-/corser-1.1.1.tgz", "integrity": "sha512-DUgHj2yQ/6f+azyhMej+fU3n6Kb4UaJvPr6LPK+o7MJUV8bcNYoBpPJ4g9ByIDr60PjI+WCEnPp2CmYIwXhE/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBU2JSuH4xWF+CPnz2UkoBnoQYq4a+75uCshtAHrNQYuAiBWzIJCKtB4I5cUtD3j3yc7Qkxi4Lkso+KvFxSehkvmMQ=="}]}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "1.1.2": {"name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/agrueneberg/Corser"}, "licenses": [{"type": "The MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}], "main": "./lib/corser.js", "devDependencies": {"mocha": "1.3.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "./node_modules/.bin/mocha", "start": "node server.js"}, "_id": "corser@1.1.2", "dist": {"shasum": "ec8579ef8a81549435f6a9cf1071a8e8c15be942", "tarball": "https://registry.npmjs.org/corser/-/corser-1.1.2.tgz", "integrity": "sha512-VBvFwFHjMCk+cgzeFfCR/+yersTHhDYVAY+5YCJuSCyhkZaaaNPWnxTVqZH3VMUy0LWhLfTgYTFqrgfB03841w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC4d+CNb61a3fe4V20hnQiZmhxAMX/RG+Sz7RBOwaClGAiEA7lfVL9VhlbLPQ1yEDN9yuSoOP5kKCnbTLUmuh00zsEA="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "corser", "description": "A highly configurable, middleware compatible implementation of CORS.", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/agrueneberg/Corser"}, "licenses": [{"type": "The MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}], "main": "./lib/corser.js", "devDependencies": {"mocha": "1.3.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_id": "corser@1.2.0", "dist": {"shasum": "f99a4fe770c7afdfa63db98ef435b67a22149206", "tarball": "https://registry.npmjs.org/corser/-/corser-1.2.0.tgz", "integrity": "sha512-/lB66VqhF9vSNY1ZKLzcEKRDgHkKgMLDP1BA39A4Lr869E6uzPswTYPzhtGKBR/P/In+05yOg/6K8ttzD+HpBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOd/K5Zik6nMyHId0fKBJmlzt0MynU82y4z4fYRecAPwIhANr9jTN6DEXwxOz7Ow3nGTgox7J01gJ6PkJaH3cdvMpe"}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "corser", "version": "2.0.0", "description": "A highly configurable, middleware compatible implementation of CORS.", "keywords": ["cors", "cross-origin resource sharing", "connect", "express", "middleware"], "bugs": {"url": "https://github.com/agrueneberg/Corser/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/corser.js", "repository": {"type": "git", "url": "https://github.com/agrueneberg/Corser.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "1.3.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/agrueneberg/Corser", "_id": "corser@2.0.0", "dist": {"shasum": "aea3c5186d0dbb99d23abab15d08514eb87e3840", "tarball": "https://registry.npmjs.org/corser/-/corser-2.0.0.tgz", "integrity": "sha512-vpg9sWAd0tFKOxeJ77PKtMs6vAlqUGUm2ufDSCtPzbQK9MS3IluEho/EgoW+2QvL+Jo2W8+vIUlx2cHeAi9jCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/0H9XtqOMpuV7RSTUr8A4VHaMBvu5JHbfIircBT1uIAIgTIbQgMsUv3OR/F8lpW5yrT175CsTlsIH4tCZdvl9u4U="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "directories": {}}, "2.0.1": {"name": "corser", "version": "2.0.1", "description": "A highly configurable, middleware compatible implementation of CORS.", "keywords": ["cors", "cross-origin resource sharing", "connect", "express", "middleware"], "bugs": {"url": "https://github.com/agrueneberg/Corser/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/corser.js", "repository": {"type": "git", "url": "git+https://github.com/agrueneberg/Corser.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "1.3.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "gitHead": "ba612c30145eb0245957a135cdb26bf2f2734164", "homepage": "https://github.com/agrueneberg/Corser#readme", "_id": "corser@2.0.1", "_shasum": "8eda252ecaab5840dcd975ceb90d9370c819ff87", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "6.3.1", "_npmUser": {"name": "agrueneberg", "email": "<EMAIL>"}, "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "dist": {"shasum": "8eda252ecaab5840dcd975ceb90d9370c819ff87", "tarball": "https://registry.npmjs.org/corser/-/corser-2.0.1.tgz", "integrity": "sha512-utCYNzRSQIZNPIcGZdQc92UVJYAhtGAteCFg0yRaFm8f0P+CPtyGyHXJcGXnffjCybUCEx3FQ2G7U3/o9eIkVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDpnv8uISl37pGuxlUVy4ZuR3OKd0Wxel6ihPcln+2DnAiA+jzOIwZri1JoT6H+YXgqWanXQvdoOARSzYVhvMRP+hg=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/corser-2.0.1.tgz_1471373999376_0.5666255787946284"}, "directories": {}}}, "readme": "Corser\n=======\n\n[![Project Status: Active - The project has reached a stable, usable state and is being actively developed.](http://www.repostatus.org/badges/0.1.0/active.svg)](http://www.repostatus.org/#active)\n[![Build Status](https://secure.travis-ci.org/agrueneberg/Corser.png)](http://travis-ci.org/agrueneberg/Corser)\n\nA highly configurable, middleware compatible implementation of [CORS](http://www.w3.org/TR/cors/) for [Node.js](http://nodejs.org/).\n\n\nChangelog\n---------\n\n### 2.0.1 (August 16, 2016)\n\n* Add workaround for [Chrome 52 sending empty `Access-Control-Request-Headers` header](https://bugs.chromium.org/p/chromium/issues/detail?id=633729).\n\n### 2.0.0 (March 22, 2014)\n\n* Preflight requests are automatically closed. If there is a need for handling `OPTIONS` requests, check the `endPreflightRequests` option.\n* The parameters of the callback function in dynamic origin checking are now `(err, matches)` instead of just `(matches)`.\n\n\nExamples\n--------\n\n### How to use Corser as a middleware in Express\n\nSee `example/express/` for a working example.\n\n    var express, corser, app;\n\n    express = require(\"express\");\n    corser = require(\"corser\");\n\n    app = express();\n\n    app.use(corser.create());\n\n    app.get(\"/\", function (req, res) {\n        res.writeHead(200);\n        res.end(\"Nice weather today, huh?\");\n    });\n\n    app.listen(1337);\n\n### How to use Corser as a middleware in Connect\n\nSee `example/connect/` for a working example.\n\n    var connect, corser, app;\n\n    connect = require(\"connect\");\n    corser = require(\"corser\");\n\n    app = connect();\n\n    app.use(corser.create());\n\n    app.use(function (req, res) {\n        res.writeHead(200);\n        res.end(\"Nice weather today, huh?\");\n    });\n\n    app.listen(1337);\n\n### How to use Corser with plain `http`\n\n    var http, corser, corserRequestListener;\n\n    http = require(\"http\");\n    corser = require(\"corser\");\n\n    // Create Corser request listener.\n    corserRequestListener = corser.create();\n\n    http.createServer(function (req, res) {\n        // Route req and res through the request listener.\n        corserRequestListener(req, res, function () {\n            res.writeHead(200);\n            res.end(\"Nice weather today, huh?\");\n        });\n    }).listen(1337);\n\n\nAPI\n---\n\n### Creating a Corser request listener\n\nCreating a Corser request listener that generates the appropriate response headers to enable CORS is as simple as:\n\n    corser.create()\n\nThis is the equivalent of setting a response header of `Access-Control-Allow-Origin: *`. If you want to restrict the origins, or allow more sophisticated request or response headers, you have to pass a configuration object to `corser.create`.\n\nCorser will automatically end preflight requests for you. A preflight request is a special `OPTIONS` request that the browser sends under certain conditions to negotiate with the server what methods, request headers and response headers are allowed for a CORS request. If you need to use the `OPTIONS` method for other stuff, just set `endPreflightRequests` to `false` and terminate those requests yourself:\n\n    var corserRequestListener;\n\n    corserRequestListener = corser.create({\n        endPreflightRequests: false\n    });\n\n    corserRequestListener(req, res, function () {\n        if (req.method === \"OPTIONS\") {\n            // End CORS preflight request.\n            res.writeHead(204);\n            res.end();\n        } else {\n            // Implement other HTTP methods.\n        }\n    });\n\n\n#### Configuration Object\n\nA configuration object with the following properties can be passed to `corser.create`.\n\n##### `origins`\n\nA case-sensitive whitelist of origins. Unless unbound, if the request comes from an origin that is not in this list, it will not be handled by CORS.\n\nTo allow for dynamic origin checking, a function `(origin, callback)` can be passed instead of an array. `origin` is the Origin header, `callback` is a function `(err, matches)`, where `matches` is a boolean flag that indicates whether the given Origin header matches or not.\n\nDefault: unbound, i.e. every origin is accepted.\n\n##### `methods`\n\nAn uppercase whitelist of methods. If the request uses a method that is not in this list, it will not be handled by CORS.\n\nSetting a value here will overwrite the list of default simple methods. To not lose them, concat the methods you want to add with `corser.simpleMethods`: `corser.simpleMethods.concat([\"PUT\", \"DELETE\"])`.\n\nDefault: simple methods (`GET`, `HEAD`, `POST`).\n\n##### `requestHeaders`\n\nA case-insensitive whitelist of request headers. If the request uses a request header that is not in this list, it will not be handled by CORS.\n\nSetting a value here will overwrite the list of default simple request headers. To not lose them, concat the request headers you want to add with `corser.simpleRequestHeaders`: `corser.simpleRequestHeaders.concat([\"Authorization\"])`.\n\nDefault: simple request headers (`Accept`, `Accept-Language`, `Content-Language`, `Content-Type`, `Last-Event-ID`).\n\n##### `responseHeaders`\n\nA case-insensitive whitelist of response headers. Any response header that is not in this list will be filtered out by the user-agent (the browser).\n\nSetting a value here will overwrite the list of default simple response headers. To not lose them, concat the response headers you want to add with `corser.simpleResponseHeaders`: `corser.simpleResponseHeaders.concat([\"ETag\"])`.\n\nDefault: simple response headers (`Cache-Control`, `Content-Language`, `Content-Type`, `Expires`, `Last-Modified`, `Pragma`).\n\n##### `supportsCredentials`\n\nA boolean that indicates if cookie credentials can be transferred as part of a CORS request. Currently, only a few HTML5 elements can benefit from this setting.\n\nDefault: `false`.\n\n##### `maxAge`\n\nAn integer that indicates the maximum amount of time in seconds that a preflight request is kept in the client-side preflight result cache.\n\nDefault: not set.\n\n##### `endPreflightRequests`\n\nA boolean that indicates if CORS preflight requests should be automatically closed.\n\nDefault: `true`.\n\n\nFAQ\n---\n\n### Ajax call returns `Origin X is not allowed by Access-Control-Allow-Origin`\n\nCheck if the `Origin` header of your request matches one of the origins provided in the `origins` property of the configuration object. If you didn't set any `origins` property, jump to the next question.\n\n\n### Ajax call still returns `Origin X is not allowed by Access-Control-Allow-Origin`\n\nYour request might use a non-simple method or one or more non-simple headers. According to the specification, the set of simple methods is `GET`, `HEAD`, and `POST`, and the set of simple request headers is `Accept`, `Accept-Language`, `Content-Language`, `Content-Type`, and `Last-Event-ID`. If your request uses **any** other method or header, you have to explicitly list them in the `methods` or `requestHeaders` property of the configuration object.\n\n\n#### Example\n\nYou want to allow requests that use an `X-Requested-With` header. Pass the following configuration object to `corser.create`:\n\n    corser.create({\n        requestHeaders: corser.simpleRequestHeaders.concat([\"X-Requested-With\"])\n    });\n\n\n### Getting a response header returns `Refused to get unsafe header \"X\"`\n\nYour browser blocks every non-simple response headers that was not explicitly allowed in the preflight request. The set of simple response headers is `Cache-Control`, `Content-Language`, `Content-Type`, `Expires`, `Last-Modified`, `Pragma`. If you want to access **any** other response header, you have to explicitly list them in the `responseHeaders` property of the configuration object.\n\n#### Example\n\nYou want to allow clients to read the `ETag` header of a response. Pass the following configuration object to `corser.create`:\n\n    corser.create({\n        responseHeaders: corser.simpleResponseHeaders.concat([\"ETag\"])\n    });\n", "maintainers": [{"name": "agrueneberg", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T01:30:29.252Z", "created": "2011-11-28T23:29:23.758Z", "0.1.0": "2011-11-28T23:29:24.343Z", "0.2.0": "2011-12-20T20:12:46.705Z", "1.0.0": "2012-02-17T22:20:26.432Z", "1.0.1": "2012-04-09T15:14:36.050Z", "1.0.2": "2012-06-26T19:11:24.189Z", "1.1.0": "2012-08-07T17:13:23.126Z", "1.1.1": "2012-08-07T17:31:27.624Z", "1.1.2": "2012-10-26T05:48:15.686Z", "1.2.0": "2012-11-19T21:43:40.658Z", "2.0.0": "2014-03-20T18:35:41.114Z", "2.0.1": "2016-08-16T19:00:02.248Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/agrueneberg/Corser.git"}, "users": {"maxogden": true, "daizch": true}, "homepage": "https://github.com/agrueneberg/Corser#readme", "keywords": ["cors", "cross-origin resource sharing", "connect", "express", "middleware"], "bugs": {"url": "https://github.com/agrueneberg/Corser/issues"}, "license": "MIT", "readmeFilename": "README.md"}