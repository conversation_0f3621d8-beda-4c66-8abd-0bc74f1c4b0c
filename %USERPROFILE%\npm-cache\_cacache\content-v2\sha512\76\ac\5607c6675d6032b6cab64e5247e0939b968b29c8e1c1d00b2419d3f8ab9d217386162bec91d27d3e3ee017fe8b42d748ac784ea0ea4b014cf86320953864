{"_id": "url-join", "_rev": "40-20da650377e33692ec40286397df2cd4", "name": "url-join", "description": "Join urls and normalize as in path.join.", "dist-tags": {"latest": "5.0.0"}, "versions": {"0.0.1": {"name": "url-join", "version": "0.0.1", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"should": "~1.2.1", "mocha": "~1.8.1"}, "_id": "url-join@0.0.1", "dist": {"shasum": "1db48ad422d3402469a87f7d97bdebfe4fb1e3c8", "tarball": "https://registry.npmjs.org/url-join/-/url-join-0.0.1.tgz", "integrity": "sha512-H6dnQ/yPAAVzMQRvEvyz01hhfQL5qRWSEt7BX8t9DqnPw9BjMb64fjIRq76Uvf1hkHp+mTZvEVJ5guXOT0Xqaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFkz6DSUbZgAIFWQlP+w48K094bgBlZwibDKhMdt5Af0AiEAtWM8kh2nTpVec2Vpq7i/S4BReGox8WGvN7+ZXq/L7Bc="}]}, "_npmVersion": "1.1.69", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "url-join", "version": "1.0.0", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"should": "~1.2.1", "mocha": "~1.8.1"}, "gitHead": "197bfe89b196f937d7331ef60b9b04a660944bb0", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@1.0.0", "_shasum": "886f5e9f9266db72af3dd4b218fdab1a3bfaccc7", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "886f5e9f9266db72af3dd4b218fdab1a3bfaccc7", "tarball": "https://registry.npmjs.org/url-join/-/url-join-1.0.0.tgz", "integrity": "sha512-Ea+/X98num+1NdcAgZ5b0yLM20VlneWsFIDTo/S2glX2WyvQR63aDbgNoI31nWYk5ZExzAqg/bGU+Dw0K+KKlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcPZ+mskg3Im1fJJ0z7Qz0n00cQhaYBPcpH0/8WtjOQwIhAI08Lh8m+Dfe4eksgPiWzON7VgW+tjssqJdK8yiqglkX"}]}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/url-join-1.0.0.tgz_1458738449340_0.8096554670482874"}, "directories": {}}, "1.1.0": {"name": "url-join", "version": "1.1.0", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"should": "~1.2.1", "mocha": "~1.8.1"}, "gitHead": "3144ba1acbfcee988d6abb3b2be1df532f0e151c", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@1.1.0", "_shasum": "741c6c2f4596c4830d6718460920d0c92202dc78", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "741c6c2f4596c4830d6718460920d0c92202dc78", "tarball": "https://registry.npmjs.org/url-join/-/url-join-1.1.0.tgz", "integrity": "sha512-zz1wZk4Lb5PTVwZ3HWDmm8XnlPvmOof6/fjdDPA5yBrUcbtV64U6bV832Zf1BtU2WkBBWaUT46wCs+l0HP5nhg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCnQtejhIMcTqt9H+wQv0EsUWAe8AN8/aAwFaIFcpW4gIgWXfr0DIC0Y8YCgDUi4l1bHTwbUcXvkTsYNbe1rFzfYY="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/url-join-1.1.0.tgz_1459856865965_0.46360294660553336"}, "directories": {}}, "2.0.0": {"name": "url-join", "version": "2.0.0", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"should": "~1.2.1", "mocha": "~1.8.1"}, "gitHead": "f8961b5e088ad240609e751509e74f9655f4fd73", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@2.0.0", "_shasum": "6e1b8da09cc7fd42f1f0da901f975a392789380c", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6e1b8da09cc7fd42f1f0da901f975a392789380c", "tarball": "https://registry.npmjs.org/url-join/-/url-join-2.0.0.tgz", "integrity": "sha512-P2A3w1ZENoS+JjDiTVWxSo83GUTRI6czcgwc1eM92cyEJ6RutF760fk4qW2ucmFUFaOPeis00uD4cpO2OK5CNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDugHnibv7Fyjv7ONTZ0wkesci/+qHb8NUkrnD9tiIBtwIhALp0tK7yCHYKzdVJsSYqsydmHV565Kl4GNvIny0pDzv3"}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/url-join-2.0.0.tgz_1491919207360_0.24599577975459397"}, "directories": {}}, "2.0.1": {"name": "url-join", "version": "2.0.1", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "6f5adc60206a276604a7265637c70d440061761e", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@2.0.1", "_shasum": "04b446fcd359a2fa42cc9b6962b219b91ddfe1a8", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "04b446fcd359a2fa42cc9b6962b219b91ddfe1a8", "tarball": "https://registry.npmjs.org/url-join/-/url-join-2.0.1.tgz", "integrity": "sha512-NRyxrbUD7WrySN0KeJQAcbh5QG47+N1NR8NRawQkhy6cGLcdKEP9aguMSjABPB09ASaf/+AQ6TBx5w+dgh3sRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHz1btnxrd8s9xW7A2goek66pXIVTXhhgOADIGaUEU6KAiEA0XoX+sDwHW4yTf6uDflYIeS33CPQ+pWVDOQr7hxgJAM="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/url-join-2.0.1.tgz_1492002770779_0.21299825655296445"}, "directories": {}}, "2.0.2": {"name": "url-join", "version": "2.0.2", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "c4485862572d6235978e28c52b3b6210b46aa79b", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@2.0.2", "_shasum": "c072756967ad24b8b59e5741551caac78f50b8b7", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c072756967ad24b8b59e5741551caac78f50b8b7", "tarball": "https://registry.npmjs.org/url-join/-/url-join-2.0.2.tgz", "integrity": "sha512-RC0XmKAqIiYcwNQMpmgW0O+RzbO1AH6zUIzZJBm3eINHfSaDHdQxFtLAXSG+X+24HbRtrY68IU03LvwAVNy30w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXv8oQov7UfTJtoEbNZY+fGifsZz+r8DUkiwh4heJsKwIhAIUhkijMA0jONLsvaorkcCvpMy7AehdyUSKlrfhDaox0"}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/url-join-2.0.2.tgz_1495070025626_0.6997621071059257"}, "directories": {}}, "2.0.3": {"name": "url-join", "version": "2.0.3", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "7b7806b21cf81a3476e39ddb8a6f51272a276186", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@2.0.3", "_shasum": "15db1fdde905651c3a221a78da5feb8fc809d372", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "15db1fdde905651c3a221a78da5feb8fc809d372", "tarball": "https://registry.npmjs.org/url-join/-/url-join-2.0.3.tgz", "integrity": "sha512-Pg2xrdgEIRuv+NS3saFYixWdy9DCoGoB/xD13Rju1Fq+HAMnoCjkiZRBlE3EjRINrYfhamiOF8k460ckRJm2jA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDgQBpfUl/IqvRgC3tAnesTqLyajn8y+Oque5cB+wADHAiAEa1K2OxQM+CdJ4MCPoASakxKPaOueyR82GuVuYn7hqg=="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url-join-2.0.3.tgz_1515523681085_0.41285488684661686"}, "directories": {}}, "2.0.4": {"name": "url-join", "version": "2.0.4", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "a691369cbde65db50696878ff07d150c9b3e6d26", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@2.0.4", "_shasum": "842ac4f7d7c27ab73cfaae06c4613bef10fd6560", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "842ac4f7d7c27ab73cfaae06c4613bef10fd6560", "tarball": "https://registry.npmjs.org/url-join/-/url-join-2.0.4.tgz", "integrity": "sha512-QgEwiFlxOj1qZJj4V+T9xBeaiPAgGHyktJ5f0GzXIhjpnEwIl2Dtp/3yTiKth3Czlm7UO8gH7eHXkGpDPeqo0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHvlWEe/ylJkttfrm9KEG3TWEJS6XDQEU6Jv13NA9CgXAiEAo87MtRQ5Q0yF5R4fEUWrNzNAfX8PShcxYO5AK3umgl4="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url-join-2.0.4.tgz_1515599041533_0.5529877943918109"}, "directories": {}}, "2.0.5": {"name": "url-join", "version": "2.0.5", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "25736f86613e1456f1f62028d6aca7526afc191f", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@2.0.5", "_shasum": "5af22f18c052a000a48d7b82c5e9c2e2feeda728", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5af22f18c052a000a48d7b82c5e9c2e2feeda728", "tarball": "https://registry.npmjs.org/url-join/-/url-join-2.0.5.tgz", "integrity": "sha512-c2H1fIgpUdwFRIru9HFno5DT73Ok8hg5oOb5AT3ayIgvCRfxgs2jyt5Slw8kEB7j3QUr6yJmMPDT/odjk7jXow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUz/f3rAmjrDT1N8c1PwAQkrSUYJ0CmSlzt89XnmoAVwIgMtJ4bMFYgQWsJgHsvnPjcdk0VhGy9lUCQAswSQUfgSU="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url-join-2.0.5.tgz_1515608774190_0.863440845394507"}, "directories": {}}, "3.0.0": {"name": "url-join", "version": "3.0.0", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "b4cb72791879dcb3a96d5a72dfcdba12de577589", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@3.0.0", "_shasum": "26e8113ace195ea30d0fc38186e45400f9cea672", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "26e8113ace195ea30d0fc38186e45400f9cea672", "tarball": "https://registry.npmjs.org/url-join/-/url-join-3.0.0.tgz", "integrity": "sha512-HPK12oY2BUzZDsenkms5LNC+Uger4o8jAuZbH5sLA6oKEbJqjKlo9v4o6loiSnNNQMmEZ8dDt60hX71J1G122A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3trPxvbQicx5SukOR/I6szBBlQCT6DxezkwvJMQhUSwIgLVWqx9U/i1TKrXAM2ws1VECAZIYEkMHTgf8xgKrzrao="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url-join-3.0.0.tgz_1516194409563_0.7553204703144729"}, "directories": {}}, "4.0.0": {"name": "url-join", "version": "4.0.0", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "a560e231fbe1aa9f77e5ab9ec42768056c4aa791", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@4.0.0", "_shasum": "4d3340e807d3773bda9991f8305acdcc2a665d2a", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4d3340e807d3773bda9991f8305acdcc2a665d2a", "tarball": "https://registry.npmjs.org/url-join/-/url-join-4.0.0.tgz", "integrity": "sha512-EGXjXJZhIHiQMK2pQukuFcL303nskqIRzWvPvV5O8miOfwoUb9G+a/Cld60kUyeaybEI94wvVClT10DtfeAExA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJ15p/v+cqp7h1wuCiqXJ/699CXAR8g4AJFiE7mJ/N8AiEA+X7U40IRPF0RQGLbGY8m0ZnIsGKJrw6NaICRvuZCe1Q="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url-join-4.0.0.tgz_1517578169647_0.829645907972008"}, "directories": {}}, "4.0.1": {"name": "url-join", "version": "4.0.1", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"conventional-changelog": "^1.1.10", "mocha": "^3.2.0", "should": "~1.2.1"}, "gitHead": "71b89639f194f1fff03b23a334c73b61cae9d839", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@4.0.1", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==", "shasum": "b642e21a2646808ffa178c4c5fda39844e12cde7", "tarball": "https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz", "fileCount": 8, "unpackedSize": 18310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH2KNCRA9TVsSAnZWagAALSAP/0+OeTvpB6TWJdqFcfw7\njJMj9zyvkhgMmAbjeeygTCmC35BmcDcMm7UL7bSI0rjwKMG4ZanxvdkD/4k2\nrRY1yPsTP4VZNlfzGO23ww1lvuqbIKn8kM8I5+hmU788jH3Dm5wFcRbXbAG5\nHYvjZrukcn2rdLnvmBe21kVrmN4M15LgdtOTRkecSos2vVx+mDPmuXSkGZrs\n51jQftibxcy0T89gylgLhRFyZluiq/X2+3RPVt56xv+lfVhvQ2uuTyizltqj\nlYckOkZboStN3x9twjIFG0qalFeIE1aUpnbVH3wUOL1dMMjs7K5lC1eZSeb+\nYvV8EmvuSOka+WhOrceewTHYVJtgZLO/jmfwQOrn7cNHEZeGdzVZUBRXQLE/\n0q5EyUJ3MYfu8536bwQ82aWed6OruqiNQvI9F29bL789Tkfl6tVi2shwery/\nANmpz1Yb++ap5Y57BApNxMDPPxfAY8ZruRkBqhpOOcDB+5u88DElQRT6G4xs\nspceVtKLiVSIqJ8A5tuQPS7SdMhVknK/SuYe/YcY23idy8vk2pCcooZE/rD9\nh+0tYqK0IFc3YxJh0bkh8wQ6Iu366uzRonA7VwQYzjkEjS4ngRb+JfeTrA1R\n27h0052HLKXFOS8QVN3uhEJPNQhH25QyNU8RIaXrCVNH0EjArmh4ULEVVbsa\ndDYU\r\n=itmM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIErReUPABTd2+jmYk7cX2qwy7V7mpn1H0Pvp/Kj3iUzkAiEA+RXHZhijddCSwV/U7suqTdEftAYlI5TtNhMMvs5Co0o="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url-join_4.0.1_1562337932457_0.47777741766157167"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "url-join", "version": "5.0.0", "description": "Join urls and normalize as in path.join.", "type": "module", "main": "./lib/url-join.js", "exports": "./lib/url-join.js", "types": "./lib/url-join.d.ts", "sideEffects": false, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "license": "MIT", "devDependencies": {"conventional-changelog": "^3.1.25", "mocha": "^9.2.2", "should": "~13.2.3"}, "gitHead": "adccb32f1ab7c9ae7b39a671f8179b0fcd5625a7", "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "_id": "url-join@5.0.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.5.3", "dist": {"integrity": "sha512-n2huDr9h9yzd6exQVnH/jU5mr+Pfx08LRXXZhkLLetAMESRj+anQsTAh940iMrIetKAmry9coFuZQ2jY8/p3WA==", "shasum": "c2f1e5cbd95fa91082a93b58a1f42fecb4bdbcf1", "tarball": "https://registry.npmjs.org/url-join/-/url-join-5.0.0.tgz", "fileCount": 5, "unpackedSize": 4742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiO4scACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0Hg//W198DadCMMOdVAa0qwCzY3t9sMLXc5qGJCEYKJz+EjCMBvPC\r\ndbTM6ZbmE898B/Bp6RdmDZ8k6RAaEgWsoudeauuiao9hogWjms4BTWEz3NqF\r\ncs0ffYhs1vr6hTSiGQGKnOSQqTUZQAcpym64l5S2KjM5MMwbqa8enRJi3+Pl\r\ne1xNromp0KW9JaxIPL2Rj2z50EDaqSjKuF3sT7NfllQe7MRiT4zz7lDdS5vz\r\nA5aiax5IGFyXoOKm3Fmq5UEvWba/yvWI7IXQtqfRld5FqGOWed7QPpZsyIOx\r\nzDvacvL6oVlhv6XDU070FUW9gEKuP0nolbJsNe5wVcdUuzLpDXAzcuw8axb9\r\nXCf0CvDLlUuClHDMMZCOdiww6glKIOH/SZWxTUXPhiubhWFK/eNj1N5hA4vs\r\nMjSbTaGSd273N0viKA3Nklra737SRXJvysS2GcMnGAH1BDSNjaOHoyHmHONo\r\nnkOLZo9AdYSKsgwPgELd3/UK95ogv8IQsZDCwOcJguUqDzqOIOIQwQO2q+D2\r\nJqAck40L1ZbX5QY6kSz6RhNIE9e+WSBfPpLqtjTR0/8vSNgUX4ld88f3Fquw\r\nhjRxsG0HukLOf0xubIuSagZUdFQMrRApF4CabbSz868wwwlpfNHf9hQYoSBh\r\naYgnL9YG4GP2wqNribjQQy0QJEcPtBo/WA4=\r\n=gGu6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9sDVWEJgITaKUVQ3AddhYlmBO+HwlCAU2kxXZVQlTegIhAKFkDXh2ciIGpPOApZihqYlkxUiRIXTy2pVlSpS5Kcv2"}]}, "_npmUser": {"name": "jonko<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jonko<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/url-join_5.0.0_1648069404594_0.2947669241200497"}, "_hasShrinkwrap": false}}, "readme": "Join all arguments together and normalize the resulting URL.\n\n## Install\n\n```bash\nnpm install url-join\n```\n\nIf you want to use it directly in a browser use a CDN like [Skypack](https://www.skypack.dev/view/url-join).\n\n## Usage\n\n```javascript\nimport url<PERSON>oin from 'url-join';\n\nconst fullUrl = urlJoin('http://www.google.com', 'a', '/b/cd', '?foo=123');\n\nconsole.log(fullUrl);\n```\n\nPrints:\n\n```\n'http://www.google.com/a/b/cd?foo=123'\n```\n\n## License\n\nMIT\n", "maintainers": [{"email": "<EMAIL>", "name": "jkoops"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "time": {"modified": "2023-07-15T21:26:24.297Z", "created": "2013-01-29T15:47:51.977Z", "0.0.1": "2013-01-29T15:47:55.916Z", "1.0.0": "2016-03-23T13:07:31.637Z", "1.1.0": "2016-04-05T11:47:48.154Z", "2.0.0": "2017-04-11T14:00:07.632Z", "2.0.1": "2017-04-12T13:12:52.649Z", "2.0.2": "2017-05-18T01:13:45.872Z", "2.0.3": "2018-01-09T18:48:01.145Z", "2.0.4": "2018-01-10T15:44:01.602Z", "2.0.5": "2018-01-10T18:26:14.340Z", "3.0.0": "2018-01-17T13:06:49.696Z", "4.0.0": "2018-02-02T13:29:29.743Z", "4.0.1": "2019-07-05T14:45:32.627Z", "5.0.0": "2022-03-23T21:03:24.801Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "users": {"passcod": true, "gztomas": true, "joeyespo": true, "xiechao06": true, "docksteaderluke": true, "bojand": true, "jybleau": true, "scottfreecode": true, "langri-sha": true, "yageek": true, "kodekracker": true, "tcrowe": true, "ierceg": true, "orenschwartz": true, "justjavac": true, "wisetc": true, "zuojiang": true, "flumpus-dev": true}, "homepage": "https://github.com/jfromaniello/url-join#readme", "keywords": ["url", "join"], "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "license": "MIT", "readmeFilename": "README.md"}