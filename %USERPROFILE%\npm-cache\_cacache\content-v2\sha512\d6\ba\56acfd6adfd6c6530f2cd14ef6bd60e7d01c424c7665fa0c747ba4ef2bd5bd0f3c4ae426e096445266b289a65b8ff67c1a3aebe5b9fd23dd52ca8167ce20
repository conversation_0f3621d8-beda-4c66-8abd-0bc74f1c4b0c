{"_id": "get-proto", "_rev": "1-fb55047c66343413eea8b52de307da9f", "name": "get-proto", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "get-proto", "version": "1.0.0", "keywords": ["get", "proto", "prototype", "getPrototypeOf", "[[Prototype]]"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-proto@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/get-proto#readme", "bugs": {"url": "https://github.com/ljharb/get-proto/issues"}, "dist": {"shasum": "2f14be4ab6a5ba2ca65b49defb544280730d9c5c", "tarball": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.0.tgz", "fileCount": 15, "integrity": "sha512-TtLgOcKaF1nMP2ijJnITkE4nRhbpshHhmzKiuhmSniiwWzovoqwqQ8rNuhf0mXJOqIY5iU+QkUe0CkJYrLsG9w==", "signatures": [{"sig": "MEQCIF7U9jP+C95ayaI8l3y9f+/a5PHvwNSzL0zE7fXNc5bwAiA6cqpBRmjSSelAqzNZ6VlQOewlETzPIzzG6Zxbr2PbgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8714}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json", "./Object.getPrototypeOf": "./Object.getPrototypeOf.js", "./Reflect.getPrototypeOf": "./Reflect.getPrototypeOf.js"}, "gitHead": "ae0906c73d38b6b0d750d685935c1d7eb9e80646", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc && attw -P", "posttest": "npx npm@\">=10.2\" audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/ljharb/get-proto.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Robustly get the [[Prototype]] of an object", "directories": {}, "_nodeVersion": "23.5.0", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.3", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/get-proto_1.0.0_1735799732799_0.8974534818128452", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.1": {"name": "get-proto", "version": "1.0.1", "description": "Robustly get the [[Prototype]] of an object", "main": "index.js", "exports": {".": "./index.js", "./Reflect.getPrototypeOf": "./Reflect.getPrototypeOf.js", "./Object.getPrototypeOf": "./Object.getPrototypeOf.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "npx npm@\">=10.2\" audit --production", "tests-only": "nyc tape 'test/**/*.js'", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-proto.git"}, "keywords": ["get", "proto", "prototype", "getPrototypeOf", "[[Prototype]]"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/get-proto/issues"}, "homepage": "https://github.com/ljharb/get-proto#readme", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "testling": {"files": "test/index.js"}, "_id": "get-proto@1.0.1", "gitHead": "1b92a72e80c026d391589a807272c50469d37c43", "types": "./index.d.ts", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "shasum": "150b3f2743869ef3e851ec0c49d15b1d14d00ee1", "tarball": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "fileCount": 15, "unpackedSize": 10840, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFT3PrhKnimTDdnIb5/vcR/AXLpeB0IOoSJyJpKyNczlAiEAhiQpJLCTGdTtdx675mtSRGma7fHG4Pv9MC310sW4SaI="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/get-proto_1.0.1_1735848482761_0.710652854201721"}, "_hasShrinkwrap": false}}, "time": {"created": "2025-01-02T06:35:32.797Z", "modified": "2025-01-02T20:08:03.153Z", "1.0.0": "2025-01-02T06:35:32.977Z", "1.0.1": "2025-01-02T20:08:02.949Z"}, "bugs": {"url": "https://github.com/ljharb/get-proto/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/get-proto#readme", "keywords": ["get", "proto", "prototype", "getPrototypeOf", "[[Prototype]]"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-proto.git"}, "description": "Robustly get the [[Prototype]] of an object", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# get-proto <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nRobustly get the [[Prototype]] of an object. Uses the best available method.\n\n## Getting started\n\n```sh\nnpm install --save get-proto\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst getProto = require('get-proto');\n\nconst a = { a: 1, b: 2, [Symbol.toStringTag]: 'foo' };\nconst b = { c: 3, __proto__: a };\n\nassert.equal(getProto(b), a);\nassert.equal(getProto(a), Object.prototype);\nassert.equal(getProto({ __proto__: null }), null);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/get-proto\n[npm-version-svg]: https://versionbadg.es/ljharb/get-proto.svg\n[deps-svg]: https://david-dm.org/ljharb/get-proto.svg\n[deps-url]: https://david-dm.org/ljharb/get-proto\n[dev-deps-svg]: https://david-dm.org/ljharb/get-proto/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/get-proto#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/get-proto.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/get-proto.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/get-proto.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=get-proto\n[codecov-image]: https://codecov.io/gh/ljharb/get-proto/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/get-proto/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/get-proto\n[actions-url]: https://github.com/ljharb/get-proto/actions\n", "readmeFilename": "README.md"}