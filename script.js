// 政务问答页面交互功能
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const chatInput = document.querySelector('.chat-input');
    const sendBtn = document.querySelector('.send-btn');
    const chatMessages = document.querySelector('.chat-messages');
    const actionBtns = document.querySelectorAll('.action-btn');
    const quickItems = document.querySelectorAll('.quick-item');
    const faqItems = document.querySelectorAll('.faq-item');
    const infoCards = document.querySelectorAll('.info-card');

    // 问答模式状态管理 - 新的组合模式
    // 政务问答模式一直开启，不需要单独的状态变量
    let knowledgeSearchEnabled = false;      // 知识库检索模式（可与政务问答组合）
    let onlineSearchEnabled = false;         // 联网搜索模式（可与政务问答组合）

    // 智能导办状态（初始化为关闭）
    let smartGuideActive = false;             // 智能导办状态（默认关闭）

    // 打字机效果控制
    let currentTypewriterTimeouts = [];

    // 异步操作控制
    let currentProcessTimeouts = [];
    let currentFetchController = null;
    let thinkingMessageId = null;

    // 页面可见性状态
    let isPageVisible = !document.hidden;
    let pendingTypewriterTasks = [];
    let pendingValidationTasks = [];
    
    // 检查后端服务状态
    async function checkBackendStatus() {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        try {
            const response = await fetch('http://127.0.0.1:5000/qa_stats', {
                method: 'GET',
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response.ok;
        } catch {
            clearTimeout(timeoutId);
            return false;
        }
    }

    // 添加思考动画
    function addThinkingAnimation(customText = '正在思考中') {
        const thinkingHTML = `
            <div class="message ai-message thinking-message" data-thinking="true">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="thinking-animation">
                        <div class="thinking-text">
                            <i class="fas fa-brain thinking-icon"></i>
                            ${customText}
                            <span class="thinking-dots">
                                <span class="dot">.</span>
                                <span class="dot">.</span>
                                <span class="dot">.</span>
                            </span>
                        </div>
                        <div class="thinking-progress">
                            <div class="progress-bar"></div>
                        </div>
                    </div>
                </div>
                <div class="message-time">刚刚</div>
            </div>
        `;
        
        chatMessages.insertAdjacentHTML('beforeend', thinkingHTML);
        scrollToBottom();
        
        // 返回消息ID用于后续删除
        const thinkingElement = chatMessages.lastElementChild;
        const messageId = 'thinking-' + Date.now();
        thinkingElement.setAttribute('data-message-id', messageId);
        
        return messageId;
    }

    // 移除思考动画
    function removeThinkingAnimation(messageId) {
        const thinkingElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (thinkingElement) {
            thinkingElement.remove();
        }
    }

    // 政务问答模式（一直选中，不可切换）
    function toggleQAListMode() {
        // 政务问答按钮一直选中，不执行任何操作
        showNotification('💬 政务问答模式为基础模式，一直开启', 'info');
    }

    // 更新政务问答按钮样式（一直选中状态）
    function updateQAListButton() {
        const qaListBtn = document.getElementById('qaListBtn');
        if (qaListBtn) {
            qaListBtn.classList.add('active');
            qaListBtn.innerHTML = '<i class="fas fa-comments"></i> 政务问答';
            qaListBtn.style.background = '#4caf50';
            qaListBtn.style.borderColor = '#4caf50';
            qaListBtn.style.color = 'white';
            qaListBtn.style.cursor = 'default'; // 不可点击样式
        }
    }

    // 切换联网搜索状态
    async function toggleOnlineSearch() {
        if (!onlineSearchEnabled) {
            // 开启前检查后端状态
            const backendOk = await checkBackendStatus();
            if (!backendOk) {
                showCustomAlert('知识库+联网搜索', '⚠️ 后端服务未启动，无法开启知识库+联网搜索功能', 'warning');
                return;
            }

            // 显示用户须知协议
            const agreed = await showOnlineSearchAgreement();
            if (!agreed) {
                // 用户取消或拒绝协议，不开启联网搜索
                return;
            }

            // 如果知识库检索已开启，先关闭知识库检索
            if (knowledgeSearchEnabled) {
                knowledgeSearchEnabled = false;
            }

            onlineSearchEnabled = true;

            // 开启联网搜索时自动关闭智能导办
            if (smartGuideActive) {
                console.log('🎯 联网搜索开启，关闭智能导办');
                smartGuideActive = false;

                // 隐藏服务项目区域
                const serviceSection = document.querySelector('.service-section');
                if (serviceSection) {
                    serviceSection.style.display = 'none';
                }

                showNotification('智能导办已自动关闭', 'info');
            }

            showCustomAlert('知识库+联网搜索', '政务问答+知识库+联网搜索模式已开启', 'success');
        } else {
            // 关闭联网搜索
            onlineSearchEnabled = false;
            showNotification('🌐 知识库+联网搜索已关闭，回到政务问答模式', 'info');
        }

        smartPolicyMode = false;
        smartDataMode = false;
        document.body.classList.remove('smart-policy-mode');
        document.body.classList.remove('smart-data-mode');

        // 更新所有按钮状态
        updateQAListButton();
        updateKnowledgeSearchButton();
        updateOnlineSearchButton();
    }

    // 更新联网搜索按钮样式
    function updateOnlineSearchButton() {
        // 重新获取按钮，因为innerHTML会改变
        const allActionBtns = document.querySelectorAll('.action-btn');
        const onlineSearchBtn = Array.from(allActionBtns).find(btn =>
            btn.getAttribute('aria-label') === '知识库+联网搜索' ||
            btn.textContent.includes('知识库+联网搜索')
        );

        if (onlineSearchBtn) {
            if (onlineSearchEnabled) {
                onlineSearchBtn.classList.add('active');
                onlineSearchBtn.innerHTML = '<i class="fas fa-globe"></i> 知识库+联网搜索 (已开启)';
                onlineSearchBtn.style.background = '#4caf50';
                onlineSearchBtn.style.borderColor = '#4caf50';
                onlineSearchBtn.style.color = 'white';
            } else {
                onlineSearchBtn.classList.remove('active');
                onlineSearchBtn.innerHTML = '<i class="fas fa-globe"></i> 知识库+联网搜索';
                onlineSearchBtn.style.background = '';
                onlineSearchBtn.style.borderColor = '';
                onlineSearchBtn.style.color = '';
            }
        }
    }











    // 切换知识库检索状态
    function toggleKnowledgeSearch() {
        // 如果联网搜索已开启，先关闭联网搜索
        if (onlineSearchEnabled) {
            onlineSearchEnabled = false;
        }

        // 切换知识库检索状态
        knowledgeSearchEnabled = !knowledgeSearchEnabled;
        smartPolicyMode = false;
        smartDataMode = false;
        document.body.classList.remove('smart-policy-mode');
        document.body.classList.remove('smart-data-mode');

        // 如果智能导办开启，则关闭智能导办
        if (smartGuideActive) {
            console.log('🎯 知识库检索状态变化，关闭智能导办');
            smartGuideActive = false;

            // 隐藏服务项目区域
            const serviceSection = document.querySelector('.service-section');
            if (serviceSection) {
                serviceSection.style.display = 'none';
            }

            showNotification('智能导办已自动关闭', 'info');
        }

        // 更新所有按钮状态
        updateQAListButton();
        updateKnowledgeSearchButton();
        updateOnlineSearchButton();

        if (knowledgeSearchEnabled) {
            showNotification('知识库检索已开启', 'success');
        } else {
            showNotification('知识库检索已关闭', 'info');
        }
    }

    // 更新知识库检索按钮样式
    function updateKnowledgeSearchButton() {
        // 重新获取按钮，因为innerHTML可能会改变
        const allActionBtns = document.querySelectorAll('.action-btn');
        const knowledgeSearchBtn = Array.from(allActionBtns).find(btn =>
            btn.getAttribute('aria-label') === '知识库检索' ||
            btn.textContent.includes('知识库检索') ||
            btn.id === 'knowledgeSearchBtn'
        );

        if (knowledgeSearchBtn) {
            if (knowledgeSearchEnabled) {
                knowledgeSearchBtn.classList.add('active');
                knowledgeSearchBtn.innerHTML = '<i class="fas fa-database"></i> 知识库检索 (已开启)';
                knowledgeSearchBtn.style.background = '#4caf50';
                knowledgeSearchBtn.style.borderColor = '#4caf50';
                knowledgeSearchBtn.style.color = 'white';
            } else {
                knowledgeSearchBtn.classList.remove('active');
                knowledgeSearchBtn.innerHTML = '<i class="fas fa-database"></i> 知识库检索';
                knowledgeSearchBtn.style.background = '';
                knowledgeSearchBtn.style.borderColor = '';
                knowledgeSearchBtn.style.color = '';
            }
        }
    }

    // 处理知识库检索按钮点击
    function handleKnowledgeSearch() {
        toggleKnowledgeSearch();
    }

    // 直接发送问题（不通过输入框）
    function sendQuestionDirectly(question) {
        if (!question || question.trim() === '') return;

        const message = question.trim();
        processQuestion(message);
    }

    // 发送消息功能（对接后端）
    function sendMessage() {
        const message = chatInput.value.trim();
        if (message === '') return;

        console.log('🔍 发送消息，当前模式状态:', {
            smartPolicyMode,
            smartDataMode,
            knowledgeSearchEnabled,
            onlineSearchEnabled,
            message: message.substring(0, 50) + '...'
        });

        // 添加用户消息
        addUserMessage(message);
        chatInput.value = '';

        processQuestion(message);
    }

    // 处理问题的核心逻辑
    function processQuestion(message) {

        // 根据检索模式选择API端点
        let apiEndpoint, requestBody;

        if (smartPolicyMode) {
            // 智能问策模式：调用智能问策服务
            console.log('🔍 智能问策模式激活，调用 /ask_policy 接口');
            apiEndpoint = '/ask_policy';
            requestBody = { question: message };
        } else if (smartDataMode) {
            // 智能问数模式：调用智能问数服务
            console.log('📊 智能问数模式激活，调用 /ask_data 接口');
            apiEndpoint = '/ask_data';
            requestBody = { question: message };
        } else if (knowledgeSearchEnabled && onlineSearchEnabled) {
            // 政务问答+知识库+联网搜索模式：使用online_search_app_id
            apiEndpoint = '/ask_ai';
            requestBody = {
                question: message,
                online_search: true,
                app_id: 'de8fccb2d5f54cf58631229d0ee8196a'
            };
        } else if (knowledgeSearchEnabled) {
            // 政务问答+知识库检索模式：使用knowledge_app_id
            apiEndpoint = '/ask_knowledge';
            requestBody = {
                question: message,
                app_id: 'fd00ab6eaf7c47a6a9ab1d651b3be0b1'
            };
        } else if (onlineSearchEnabled) {
            // 政务问答+联网搜索模式：使用online_search_app_id
            apiEndpoint = '/ask_ai';
            requestBody = {
                question: message,
                online_search: true,
                app_id: 'de8fccb2d5f54cf58631229d0ee8196a'
            };
        } else {
            // 纯政务问答模式：本地QAlist检索
            apiEndpoint = '/ask';
            requestBody = { question: message };
        }

        // 如果开启特殊模式，先显示思考动画
        thinkingMessageId = null;
        if (smartPolicyMode || smartDataMode || onlineSearchEnabled || knowledgeSearchEnabled) {
            let thinkingText;
            if (smartPolicyMode) {
                thinkingText = '正在分析政策内容...';
            } else if (smartDataMode) {
                thinkingText = '正在分析数据内容...';
            } else if (knowledgeSearchEnabled && onlineSearchEnabled) {
                thinkingText = '正在搜索知识库和联网资源...';
            } else if (knowledgeSearchEnabled) {
                thinkingText = '正在搜索知识库资源...';
            } else if (onlineSearchEnabled) {
                thinkingText = '正在搜索知识库和联网资源...';
            }
            thinkingMessageId = addThinkingAnimation(thinkingText);
        }

        // 创建新的AbortController
        currentFetchController = new AbortController();

        // 显示AI校验流程
        fetch(`http://127.0.0.1:5000${apiEndpoint}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody),
            signal: currentFetchController.signal
        })
        .then(res => {
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`);
            }
            return res.json();
        })
        .then(data => {
            // 移除思考动画（如果存在）
            if (thinkingMessageId) {
                removeThinkingAnimation(thinkingMessageId);
            }

            const steps = data.steps;
            const answer = data.answer;

            // 检查是否为城市介绍或景点介绍（跳过校验动画）
            if ((data.source === 'city_info' || data.source === 'attraction_info') && (!steps || steps.length === 0)) {
                // 城市介绍或景点介绍直接显示答案，不显示校验流程
                const hideConsultButton = data.hide_consult_button || false;
                addAIMessage(answer, true, hideConsultButton, [], false, null, null, false);
                return;
            }

            // 创建校验流程消息
            const processMessageHTML = `
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="process-steps">
                            ${steps.map((step, index) => `
                                <div class="step-item" data-step="${index}">
                                    <div class="step-icon">
                                        <i class="fas fa-circle-notch fa-spin" style="display: none;"></i>
                                        <i class="fas fa-check" style="display: none;"></i>
                                        <i class="fas fa-times" style="display: none;"></i>
                                    </div>
                                    <span class="step-text">${step}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="message-time">刚刚</div>
                </div>
            `;

            chatMessages.insertAdjacentHTML('beforeend', processMessageHTML);
            scrollToBottom();
            
            // 创建校验任务 - 支持后台运行
            const validationTask = {
                lastMessage: chatMessages.lastElementChild,
                stepItems: chatMessages.lastElementChild.querySelectorAll('.step-item'),
                currentStep: 0,
                startTime: Date.now(),
                isActive: true,
                stepDuration: 800,
                stepInterval: 300
            };

            // 添加到校验任务列表
            pendingValidationTasks.push(validationTask);

            function animateStep() {
                if (!validationTask.isActive) return;

                if (validationTask.currentStep < validationTask.stepItems.length) {
                    const stepItem = validationTask.stepItems[validationTask.currentStep];
                    const stepText = stepItem.querySelector('.step-text').textContent;
                    const spinner = stepItem.querySelector('.fa-spin');
                    const checkIcon = stepItem.querySelector('.fa-check');
                    const timesIcon = stepItem.querySelector('.fa-times');

                    // 显示加载动画
                    spinner.style.display = 'inline-block';
                    stepItem.classList.add('processing');

                    const timeoutId1 = setTimeout(() => {
                        requestAnimationFrame(() => {
                            if (!validationTask.isActive) return;

                            spinner.style.display = 'none';
                            stepItem.classList.remove('processing');

                            // 检查是否是失败步骤（包含❌）
                            if (stepText.includes('❌')) {
                                // 显示错误图标
                                timesIcon.style.display = 'inline-block';
                                stepItem.classList.add('failed');

                                // 如果是校验失败，后续步骤直接跳过
                                if (stepText.includes('校验') || stepText.includes('敏感词')) {
                                    validationTask.currentStep = validationTask.stepItems.length; // 跳过后续步骤
                                } else {
                                    validationTask.currentStep++;
                                }
                            } else {
                                // 显示成功图标
                                checkIcon.style.display = 'inline-block';
                                stepItem.classList.add('completed');
                                validationTask.currentStep++;
                            }

                            const timeoutId2 = setTimeout(() => {
                                requestAnimationFrame(animateStep);
                            }, validationTask.stepInterval);
                            currentProcessTimeouts.push(timeoutId2);
                        });
                    }, validationTask.stepDuration);
                    currentProcessTimeouts.push(timeoutId1);
                } else {
                    // 所有步骤完成后，立即开始移除校验流程
                    const timeoutId3 = setTimeout(() => {
                        requestAnimationFrame(() => {
                            if (validationTask.lastMessage && validationTask.lastMessage.parentNode) {
                                // 添加淡出动画
                                validationTask.lastMessage.style.transition = 'opacity 0.1s ease-out, transform 0.1s ease-out';
                                validationTask.lastMessage.style.opacity = '0';
                                validationTask.lastMessage.style.transform = 'translateY(-10px)';

                                // 动画完成后移除元素
                                const timeoutId4 = setTimeout(() => {
                                    if (validationTask.lastMessage && validationTask.lastMessage.parentNode) {
                                        validationTask.lastMessage.remove();
                                    }

                                    // 从任务列表中移除
                                    const taskIndex = pendingValidationTasks.indexOf(validationTask);
                                    if (taskIndex > -1) {
                                        pendingValidationTasks.splice(taskIndex, 1);
                                    }
                                }, 200);
                                currentProcessTimeouts.push(timeoutId4);
                            }
                        });
                    }, 300); // 校验完成后300ms开始移除
                    currentProcessTimeouts.push(timeoutId3);

                    // 在校验流程开始消失的同时显示答案
                    const timeoutId5 = setTimeout(() => {
                        // 检查是否需要隐藏操作按钮：知识库回答、联网搜索回答、特殊问题(id=27)或城市介绍
                        const isKnowledgeResponse = data.source === 'ali_knowledge';
                        const isOnlineSearchResponse = data.source === 'ali_online_search' || data.is_online_search;
                        const isSpecialQuestion = data.qa_id === 27;
                        const isCityInfo = data.hide_consult_button || false;
                        const hideButtons = isKnowledgeResponse || isOnlineSearchResponse || isSpecialQuestion || isCityInfo;

                        // 传递文档引用信息（如果有的话）和联网搜索标识
                        const docReferences = data.doc_references || [];
                        const isOnlineSearch = data.is_online_search || data.source === 'ali_online_search' || false;
                        // 检查是否为智能问策或智能问数模式的响应
                        const isSmartPolicy = smartPolicyMode;
                        const isSmartData = smartDataMode;
                        const qaUrl = data.url || null;
                        const qaPhone = data.phone || null;
                        console.log('🔍 智能模式响应处理:', {
                            smartPolicyMode,
                            smartDataMode,
                            isSmartPolicy,
                            isSmartData,
                            source: data.source,
                            answerLength: answer.length,
                            answerPreview: answer.substring(0, 100) + '...'
                        });
                        console.log('调用addAIMessage:', {
                            isOnlineSearch,
                            isSmartPolicy,
                            isSmartData,
                            source: data.source,
                            is_online_search: data.is_online_search
                        });
                        // 智能问策和智能问数模式的特殊处理
                        const isSmartGuide = false; // 智能导办不使用新的API模式

                        // 获取图表数据（如果有的话）
                        const charts = data.charts || [];

                        if (smartPolicyMode || smartDataMode) {
                            console.log('🔍 智能模式，直接显示答案（无打字机效果）');
                            if (smartDataMode && charts.length > 0) {
                                console.log('📊 智能问数模式包含图表数据:', charts.length, '个图表');
                            }
                            addAIMessage(answer, false, hideButtons, docReferences, isOnlineSearch, qaUrl, qaPhone, isSmartPolicy, isSmartData, isSmartGuide, charts);
                        } else {
                            addAIMessage(answer, true, hideButtons, docReferences, isOnlineSearch, qaUrl, qaPhone, isSmartPolicy, isSmartData, isSmartGuide, charts);
                        }
                    }, 500);
                    currentProcessTimeouts.push(timeoutId5);
                }
            }
            
            animateStep();
        })
        .catch(error => {
            // 如果是用户主动取消的请求，不显示错误消息
            if (error.name === 'AbortError') {
                return;
            }
            
            // 移除思考动画（如果存在）
            if (thinkingMessageId) {
                removeThinkingAnimation(thinkingMessageId);
            }
            
            console.error('API调用错误:', error);
            let errorMessage = 'AI服务暂时不可用，请稍后再试。';
            
            // 根据错误类型提供具体的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage = '🔌 无法连接到服务器，请确保后端服务正在运行。';
            } else if (error.name === 'SyntaxError') {
                errorMessage = '📄 服务器响应格式错误，请检查后端服务状态。';
            } else if (onlineSearchEnabled) {
                errorMessage = '🌐 知识库+联网搜索服务暂时不可用，您可以切换到政务问答模式。';
            }
            
            addAIMessage(errorMessage);
        });
    }

    // 添加用户消息
    function addUserMessage(message) {
        const messageHTML = `
            <div class="message user-message">
                <div class="message-content">
                    <p>${message}</p>
                </div>
                <div class="message-time">刚刚</div>
            </div>
        `;
        chatMessages.insertAdjacentHTML('beforeend', messageHTML);
        scrollToBottom();
    }

    // 检测是否需要markdown渲染
    function needsMarkdownRendering(message, isOnlineSearch, isSmartPolicy = false, isSmartData = false, isSmartGuide = false) {
        // 联网搜索模式下的消息通常包含markdown格式
        if (isOnlineSearch) {
            // 对于联网搜索结果，总是使用markdown渲染
            // 因为联网搜索的结果通常包含结构化的markdown内容
            console.log('联网搜索结果，启用Markdown渲染:', {
                isOnlineSearch,
                messageLength: message.length,
                preview: message.substring(0, 200) + '...'
            });
            return true;
        }

        // 智能问策和智能问数模式下的消息也使用markdown渲染
        if (isSmartPolicy || smartPolicyMode || isSmartData || smartDataMode) {
            console.log('🔍 智能模式结果，启用Markdown渲染:', {
                isSmartPolicy,
                smartPolicyMode,
                isSmartData,
                smartDataMode,
                messageLength: message.length,
                preview: message.substring(0, 200) + '...'
            });
            return true;
        }

        // 检查是否包含markdown图片语法
        if (message.includes('![') && message.includes('](')) {
            console.log('🖼️ 检测到图片内容，启用Markdown渲染');
            return true;
        }

        // 检查是否包含markdown标题或其他格式
        if (message.includes('##') || message.includes('**') || message.includes('---')) {
            console.log('📝 检测到Markdown格式，启用Markdown渲染');
            return true;
        }

        // 检测城市介绍内容（包含markdown语法）
        if (message.includes('**') || message.includes('##') || message.includes('某某市简介')) {
            console.log('🏙️ 检测到城市介绍内容，启用Markdown渲染:', {
                messageLength: message.length,
                preview: message.substring(0, 100) + '...'
            });
            return true;
        }

        return false;
    }

    // 渲染markdown内容
    function renderMarkdown(message) {
        try {
            // 检查marked库是否可用
            if (typeof window.marked === 'undefined') {
                console.warn('Marked库未加载，使用基础HTML转换');
                return message.replace(/\n/g, '<br>');
            }

            // 预处理图片路径，将相对路径转换为完整URL
            let processedMessage = message.replace(/!\[([^\]]*)\]\(pic\/([^)]+)\)/g, (match, alt, filename) => {
                const fullUrl = `http://127.0.0.1:5000/pic/${filename}`;
                console.log(`🖼️ 转换图片路径: ${match} -> ![${alt}](${fullUrl})`);
                return `![${alt}](${fullUrl})`;
            });

            // 配置marked选项
            window.marked.setOptions({
                breaks: true,        // 支持换行
                gfm: true,          // 支持GitHub风格markdown
                sanitize: false,    // 不过滤HTML（因为我们信任内容源）
                smartLists: true,   // 智能列表
                smartypants: false  // 不转换引号
            });

            const rendered = window.marked.parse(processedMessage);
            console.log('Markdown渲染结果:', {
                original: message.substring(0, 200) + '...',
                processed: processedMessage.substring(0, 200) + '...',
                rendered: rendered.substring(0, 200) + '...'
            });
            return rendered;
        } catch (error) {
            console.error('Markdown渲染错误:', error);
            // 如果渲染失败，返回原始文本（转义HTML）
            return message.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
        }
    }

    // 添加AI消息（带打字机特效）
    function addAIMessage(message, isTyping = false, hideActionButtons = false, docReferences = [], isOnlineSearch = false, qaUrl = null, qaPhone = null, isSmartPolicy = false, isSmartData = false, isSmartGuide = false, charts = []) {
        // 检查是否是敏感词错误消息
        const isSensitiveWordError = message.includes('⚠️') && message.includes('不当内容');

        // 检查是否是AI回答（联网搜索的结果）或学习中的回答或特殊问题 - 用于隐藏操作按钮
        const isSpecialWelcomeMessage = message.includes('您好，我是联通数科智能小政！很高兴为您服务');
        const isAIResponse = message.includes('📞 如需更多帮助') ||
                           message.includes('这个问题我还在学习') ||
                           isSpecialWelcomeMessage ||
                           hideActionButtons;
        
        // 生成文档引用HTML
        const docReferencesHTML = docReferences && docReferences.length > 0 ? `
            <div class="doc-references" style="display: ${isTyping ? 'none' : 'block'}">
                <div class="doc-references-header">
                    <i class="fas fa-file-text"></i>
                    <span>本答复由AI结合以下资料生成，仅供参考 (${docReferences.length})</span>
                </div>
                <div class="doc-references-list">
                    ${docReferences.map((doc, index) => {
                        // 判断是否是网络内容
                        // 网络内容的特征：联网搜索结果 + 有URL但没有详细文本内容
                        const hasUrl = doc.doc_url || doc.url;
                        const hasDetailedText = doc.text && doc.text.length > 100; // 详细文本内容
                        const isWebContent = isOnlineSearch && hasUrl && !hasDetailedText;

                        if (isWebContent) {
                            // 网络内容：显示网页标题，可点击跳转
                            const webUrl = doc.doc_url || doc.url;
                            const webTitle = doc.title || doc.doc_name || '网页内容';
                            return `
                                <div class="doc-reference-item">
                                    <div class="doc-reference-header">
                                        <span class="doc-reference-index">${index + 1}</span>
                                        <a href="${webUrl}" target="_blank" rel="noopener noreferrer" class="doc-reference-web-title">
                                            <i class="fas fa-globe"></i> ${webTitle}
                                            <i class="fas fa-external-link-alt" style="margin-left: 5px; font-size: 12px;"></i>
                                        </a>
                                    </div>
                                </div>
                            `;
                        } else {
                            // 知识库文档：按现有模式显示
                            return `
                                <div class="doc-reference-item">
                                    <div class="doc-reference-header">
                                        <span class="doc-reference-index">${index + 1}</span>
                                        <span class="doc-reference-title">${doc.title || doc.doc_name || '未命名文档'}</span>
                                        ${doc.text ? `
                                            <button class="doc-expand-btn" onclick="toggleDocContent(this)" style="margin-left: 10px;">
                                                <i class="fas fa-chevron-down"></i> 展开全文
                                            </button>
                                        ` : ''}
                                    </div>
                                    ${doc.text ? `
                                        <div class="doc-reference-content-wrapper">
                                            <div class="doc-reference-content collapsed" data-full-text="${encodeURIComponent(doc.text)}" style="display: none;">
                                                ${doc.text}
                                            </div>
                                        </div>
                                    ` : ''}
                                    ${(doc.url && !isWebContent) ? `
                                        <div class="doc-reference-link">
                                            <a href="${doc.url}" target="_blank" rel="noopener noreferrer">
                                                <i class="fas fa-external-link-alt"></i> 查看原文
                                            </a>
                                        </div>
                                    ` : ''}
                                </div>
                            `;
                        }
                    }).join('')}
                </div>
            </div>
        ` : '';

        // 生成图表HTML
        const chartsHTML = charts && charts.length > 0 ? `
            <div class="charts-container" style="display: ${isTyping ? 'none' : 'block'}">
                <div class="charts-header">
                    <i class="fas fa-chart-bar"></i>
                    <span>数据可视化 (${charts.length})</span>
                </div>
                <div class="charts-list">
                    ${charts.map((chart, index) => `
                        <div class="chart-item" id="chart-${Date.now()}-${index}">
                            <div class="chart-title">${chart.title || '数据图表'}</div>
                            <div class="chart-canvas-container">
                                <canvas class="chart-canvas" data-chart-config='${JSON.stringify(chart)}'></canvas>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        ` : '';

        // 检测是否需要markdown渲染
        const useMarkdown = needsMarkdownRendering(message, isOnlineSearch, isSmartPolicy, isSmartData, isSmartGuide);

        const messageHTML = `
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    ${useMarkdown ?
                        `<div class="markdown-content">${isTyping ? '' : renderMarkdown(message)}</div>` :
                        `<p class="typing-text">${isTyping ? message : ''}</p>`
                    }
                    ${chartsHTML}
                    ${docReferencesHTML}
                    ${!isSensitiveWordError && !isAIResponse ? `
                    <div class="message-options" style="display: ${isTyping ? 'none' : 'flex'}" data-qa-url="${qaUrl || ''}" data-qa-phone="${qaPhone || ''}">
                        ${qaUrl ? '<span class="option-tag" data-action="handle">【立刻办理】</span>' : ''}
                        ${!smartPolicyMode && !smartDataMode && !hideActionButtons ? '<span class="option-tag" data-action="consult">【人工咨询】</span>' : ''}
                    </div>
                    ` : ''}
                    ${!isSensitiveWordError ? `
                    <div class="feedback-buttons" style="display: ${isTyping ? 'none' : 'flex'}">
                        <button class="feedback-btn like-btn" data-action="like">
                            <i class="fas fa-thumbs-up"></i> 满意
                        </button>
                        <button class="feedback-btn dislike-btn" data-action="dislike">
                            <i class="fas fa-thumbs-down"></i> 不满意
                        </button>
                        <button class="feedback-btn correction-btn" data-action="correction">
                            <i class="fas fa-edit"></i> 内容纠错
                        </button>
                    </div>
                    ` : ''}
                </div>
                <div class="message-time">刚刚</div>
            </div>
        `;
        chatMessages.insertAdjacentHTML('beforeend', messageHTML);
        scrollToBottom();
        
        if (isTyping) {
            const typingElement = useMarkdown ?
                chatMessages.lastElementChild.querySelector('.markdown-content') :
                chatMessages.lastElementChild.querySelector('.typing-text');
            const optionsElement = chatMessages.lastElementChild.querySelector('.message-options');
            const feedbackElement = chatMessages.lastElementChild.querySelector('.feedback-buttons');
            const docReferencesElement = chatMessages.lastElementChild.querySelector('.doc-references');
            const chartsElement = chatMessages.lastElementChild.querySelector('.charts-container');

            if (useMarkdown) {
                // 对于markdown内容，使用特殊的打字机效果
                typeWriterMarkdown(typingElement, message, () => {
                    // 打字完成后显示图表
                    if (chartsElement) {
                        chartsElement.style.display = 'block';
                        renderCharts(chartsElement);
                    }

                    // 打字完成后显示文档引用
                    if (docReferencesElement) {
                        docReferencesElement.style.display = 'block';
                    }

                    // 操作按钮：仅本地知识库回答显示
                    if (!isSensitiveWordError && !isAIResponse && optionsElement) {
                        optionsElement.style.display = 'flex';
                        addOptionTagEvents(optionsElement);
                    }

                    // 反馈按钮：所有正常回答都显示
                    if (!isSensitiveWordError && feedbackElement) {
                        feedbackElement.style.display = 'flex';
                        addFeedbackButtonEvents(feedbackElement);
                    }
                });
            } else {
                // 普通文本使用原有的打字机效果
                typeWriter(typingElement, message, 50, () => {
                // 打字完成后显示图表
                if (chartsElement) {
                    chartsElement.style.display = 'block';
                    renderCharts(chartsElement);
                }

                // 打字完成后显示文档引用
                if (docReferencesElement) {
                    docReferencesElement.style.display = 'block';
                }

                // 操作按钮：仅本地知识库回答显示
                if (!isSensitiveWordError && !isAIResponse && optionsElement) {
                    optionsElement.style.display = 'flex';
                    addOptionTagEvents(optionsElement);
                }

                // 反馈按钮：所有正常回答都显示
                if (!isSensitiveWordError && feedbackElement) {
                    feedbackElement.style.display = 'flex';
                    addFeedbackButtonEvents(feedbackElement);
                }
            });
            }
        } else {
            // 非打字模式下直接添加按钮事件和渲染图表
            if (!isSensitiveWordError) {
                const feedbackElement = chatMessages.lastElementChild.querySelector('.feedback-buttons');
                const optionsElement = chatMessages.lastElementChild.querySelector('.message-options');
                const chartsElement = chatMessages.lastElementChild.querySelector('.charts-container');

                // 渲染图表
                if (chartsElement) {
                    renderCharts(chartsElement);
                }

                // 反馈按钮：所有正常回答都显示
                if (feedbackElement) addFeedbackButtonEvents(feedbackElement);

                // 操作按钮：仅本地知识库回答显示
                if (!isAIResponse && optionsElement) addOptionTagEvents(optionsElement);
            }
        }
    }

    // 添加反馈按钮事件
    function addFeedbackButtonEvents(container) {
        const buttons = container.querySelectorAll('.feedback-btn');
        
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                const action = this.getAttribute('data-action');
                const isActive = this.classList.contains('active');
                
                // 移除其他按钮的active状态
                buttons.forEach(btn => btn.classList.remove('active'));
                
                if (!isActive) {
                    // 添加active状态
                    this.classList.add('active');
                    
                    // 显示反馈消息
                    let message = '';
                    switch(action) {
                        case 'like':
                            message = '感谢您的评价！';
                            break;
                        case 'dislike':
                            // 显示反馈弹窗而不是直接显示通知
                            showFeedbackModal();
                            return; // 不执行后续的通知逻辑
                        case 'correction':
                            // 显示纠错弹窗而不是直接显示通知
                            showCorrectionModal();
                            return; // 不执行后续的通知逻辑
                    }
                    showNotification(message, action === 'like' ? 'success' : action === 'dislike' ? 'warning' : action === 'correction' ? 'warning' : 'info');
                } else {
                    // 取消active状态
                    this.classList.remove('active');
                    showNotification('已取消操作');
                }
            });
        });
    }

    // 停止所有正在进行的打字机效果
    function stopAllTypewriter() {
        currentTypewriterTimeouts.forEach(timeoutId => {
            clearTimeout(timeoutId);
        });
        currentTypewriterTimeouts = [];

        // 停止所有打字机任务
        pendingTypewriterTasks.forEach(task => {
            task.isActive = false;
        });
        pendingTypewriterTasks = [];
    }

    // 停止所有异步操作
    function stopAllAsyncOperations() {
        // 停止打字机效果
        stopAllTypewriter();

        // 停止校验过程动画
        currentProcessTimeouts.forEach(timeoutId => {
            clearTimeout(timeoutId);
        });
        currentProcessTimeouts = [];

        // 停止所有校验任务并清理DOM元素
        pendingValidationTasks.forEach(task => {
            task.isActive = false;
            // 立即移除校验流程的DOM元素
            if (task.lastMessage && task.lastMessage.parentNode) {
                task.lastMessage.remove();
            }
        });
        pendingValidationTasks = [];

        // 取消正在进行的网络请求
        if (currentFetchController) {
            currentFetchController.abort();
            currentFetchController = null;
        }

        // 清理可能残留的思考动画
        if (thinkingMessageId) {
            const thinkingElement = document.getElementById(thinkingMessageId);
            if (thinkingElement) {
                thinkingElement.remove();
            }
            thinkingMessageId = null;
        }
    }

    // Markdown打字机特效函数 - 流式输出版本
    function typeWriterMarkdown(element, text, onComplete = null) {
        console.log('🔍 typeWriterMarkdown 开始执行:', {
            element,
            textLength: text.length,
            textPreview: text.substring(0, 100) + '...'
        });

        let currentIndex = 0;
        const speed = 30; // 打字速度（毫秒）

        function typeNextChar() {
            if (currentIndex < text.length) {
                // 获取当前要显示的文本片段
                const currentText = text.substring(0, currentIndex + 1);

                // 渲染当前的markdown内容
                const renderedContent = renderMarkdown(currentText);
                element.innerHTML = renderedContent;

                currentIndex++;

                // 继续下一个字符
                setTimeout(typeNextChar, speed);
            } else {
                // 打字完成
                element.classList.add('completed');
                console.log('🔍 typeWriterMarkdown 完成，调用回调');

                if (onComplete) {
                    setTimeout(onComplete, 100);
                }
            }
        }

        // 开始打字
        typeNextChar();
    }

    // 打字机特效函数 - 支持后台运行
    function typeWriter(element, text, speed = 50, onComplete = null) {
        element.innerHTML = '';

        // 将HTML标签转换为普通文本
        const cleanText = text.replace(/<br>/g, '\n').replace(/<[^>]*>/g, '');

        // 创建任务对象
        const task = {
            element,
            cleanText,
            speed,
            onComplete,
            currentIndex: 0,
            startTime: Date.now(),
            isActive: true
        };

        // 添加到任务列表
        pendingTypewriterTasks.push(task);

        function type() {
            if (!task.isActive) return;

            if (task.currentIndex < task.cleanText.length) {
                const char = task.cleanText.charAt(task.currentIndex);
                if (char === '\n') {
                    task.element.innerHTML += '<br>';
                } else {
                    task.element.innerHTML += char;
                }
                task.currentIndex++;

                // 使用 requestAnimationFrame 替代 setTimeout 以确保后台运行
                const timeoutId = setTimeout(() => {
                    requestAnimationFrame(type);
                }, speed);
                currentTypewriterTimeouts.push(timeoutId);
            } else {
                // 打字完成后移除光标
                task.element.classList.add('completed');
                task.isActive = false;

                // 从任务列表中移除
                const taskIndex = pendingTypewriterTasks.indexOf(task);
                if (taskIndex > -1) {
                    pendingTypewriterTasks.splice(taskIndex, 1);
                }

                if (task.onComplete) task.onComplete();
            }
        }

        // 开始打字
        requestAnimationFrame(type);
    }



    // 滚动到底部
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 发送按钮点击事件
    sendBtn.addEventListener('click', sendMessage);

    // 输入框回车事件
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    // 输入框焦点效果
    chatInput.addEventListener('focus', function() {
        this.parentElement.style.borderColor = '#2196f3';
    });

    chatInput.addEventListener('blur', function() {
        this.parentElement.style.borderColor = '#dee2e6';
    });

    // 操作按钮点击事件
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const btnText = this.textContent.trim();
            const ariaLabel = this.getAttribute('aria-label');
            const btnId = this.id;

            // 检查按钮类型，优先使用aria-label，然后检查文本内容
            if (ariaLabel === '政务问答' || btnId === 'qaListBtn') {
                toggleQAListMode();
                return;
            }

            if (ariaLabel === '知识库+联网搜索' || btnText.includes('知识库+联网搜索')) {
                toggleOnlineSearch();
                return;
            }

            // 处理知识库检索按钮
            if (btnId === 'knowledgeSearchBtn' || ariaLabel === '知识库检索') {
                handleKnowledgeSearch();
                return;
            }



            switch(btnText) {
                case '新建对话':
                    if (confirm('确定要开始新对话吗？当前对话记录将被清除。')) {
                        // 停止所有异步操作（包括打字机效果、校验动画、网络请求）
                        stopAllAsyncOperations();
                        
                        // 清空对话记录
                        chatMessages.innerHTML = '';
                        
                        // 重置所有模式状态，政务问答模式一直开启
                        knowledgeSearchEnabled = false;
                        onlineSearchEnabled = false;
                        smartPolicyMode = false;
                        smartDataMode = false;
                        smartGuideActive = false;
                        document.body.classList.remove('smart-policy-mode');
                        document.body.classList.remove('smart-data-mode');

                        // 隐藏服务项目区域
                        const serviceSection = document.querySelector('.service-section');
                        if (serviceSection) {
                            serviceSection.style.display = 'none';
                        }

                        // 更新所有按钮状态
                        updateQAListButton();
                        updateKnowledgeSearchButton();
                        updateOnlineSearchButton();
                        
                        // 清空输入框
                        if (chatInput) {
                            chatInput.value = '';
                        }
                        
                        // 显示初始欢迎消息
                        showInitialWelcomeMessage();
                    }
                    break;
                case '知识库检索':
                    showCustomAlert('知识库检索', '知识库检索功能正在开发中，敬请期待！', 'info');
                    break;
                case '满意':
                    showNotification('感谢您的评价！', 'success');
                    break;
                case '不满意':
                    showFeedbackModal();
                    break;
                case '内容纠错':
                    showCorrectionModal();
                    break;
            }
        });
    });

    // 快捷事项点击事件
    quickItems.forEach(item => {
        item.addEventListener('click', function() {
            const itemText = this.textContent.trim();
            chatInput.value = `我想了解${itemText}的相关信息`;
            chatInput.focus();
        });
    });

    // 服务项目点击事件 - 智能导办功能
    function handleServiceItemClick(serviceText) {
        // 根据服务类型调用相应的导办处理函数
        switch(serviceText) {
            case '教育入学':
                handleGuideService('教育入学');
                break;
            case '社会保险':
                handleGuideService('社会保险');
                break;
            case '社会救助':
                handleGuideService('社会救助');
                break;
            case '不动产业务':
                handleGuideService('不动产业务');
                break;
            case '法人业务':
                handleGuideService('法人业务');
                break;
            case '公积金业务':
                handleGuideService('公积金业务');
                break;
            case '保障房':
                handleGuideService('保障房');
                break;
            case '人才认定':
                handleGuideService('人才认定');
                break;
            case '监督举报':
                handleGuideService('监督举报');
                break;
            case '出入境':
                handleGuideService('出入境');
                break;
            default:
                // 其他服务的默认处理
                chatInput.value = `我需要办理${serviceText}相关业务`;
                chatInput.focus();
        }
    }

    // 通用导办服务处理函数
    async function handleGuideService(serviceType) {
        try {
            // 显示加载状态
            const loadingMessageId = addThinkingAnimation();

            // 获取指定类型的相关问题（从数据库实时获取）
            const response = await fetch(`http://127.0.0.1:5000/get_questions_by_type/${encodeURIComponent(serviceType)}`);
            const data = await response.json();

            // 移除加载动画
            removeThinkingAnimation(loadingMessageId);

            if (data.status === 'success' && data.questions.length > 0) {
                // 显示智能导办界面
                showGuideInterface(serviceType, data.questions);
                console.log(`✅ 成功加载 ${data.questions.length} 个${serviceType}导办项目`);
            } else {
                // 没有找到相关问题，尝试重新加载数据
                console.log(`⚠️ 未找到${serviceType}相关的导办项目，尝试重新加载数据...`);
                const reloadSuccess = await reloadBackendData();

                if (reloadSuccess) {
                    // 重新加载成功，再次尝试获取数据
                    const retryResponse = await fetch(`http://127.0.0.1:5000/get_questions_by_type/${encodeURIComponent(serviceType)}`);
                    const retryData = await retryResponse.json();

                    if (retryData.status === 'success' && retryData.questions.length > 0) {
                        showGuideInterface(serviceType, retryData.questions);
                        console.log(`✅ 重新加载后成功获取 ${retryData.questions.length} 个${serviceType}导办项目`);
                        return;
                    }
                }

                // 最终还是没有数据，显示默认消息
                addAIMessage(`抱歉，暂时没有找到${serviceType}相关的业务。您可以直接输入您的问题，我会尽力为您解答。`);
            }
        } catch (error) {
            console.error(`获取${serviceType}问题失败:`, error);
            addAIMessage(`获取${serviceType}业务信息失败，请稍后重试或直接输入您的问题。`);
        }
    }

    // 文档内容展开/收起功能
    window.toggleDocContent = function(button) {
        // 找到对应的内容区域（在同一个doc-reference-item中）
        const docItem = button.closest('.doc-reference-item');
        const contentWrapper = docItem.querySelector('.doc-reference-content-wrapper');
        const contentDiv = contentWrapper.querySelector('.doc-reference-content');

        const isHidden = contentDiv.style.display === 'none';

        if (isHidden) {
            // 展开
            contentDiv.style.display = 'block';
            button.innerHTML = '<i class="fas fa-chevron-up"></i> 收起';
        } else {
            // 收起
            contentDiv.style.display = 'none';
            button.innerHTML = '<i class="fas fa-chevron-down"></i> 展开全文';
        }
    };

    // 重新加载后端数据的功能
    async function reloadBackendData() {
        try {
            const response = await fetch('http://127.0.0.1:5000/reload_data', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.status === 'success') {
                console.log(`✅ 数据重新加载成功: ${data.message}`);
                return true;
            } else {
                console.error('❌ 数据重新加载失败:', data.message);
                return false;
            }
        } catch (error) {
            console.error('❌ 重新加载数据时出错:', error);
            return false;
        }
    }

    // 通用智能导办界面显示函数
    function showGuideInterface(serviceType, questions) {
        // 为不同服务类型和问题内容分配图标
        const getIconForQuestion = (serviceType, question) => {
            // 教育入学类图标
            if (serviceType === '教育入学') {
                if (question.includes('小学') || question.includes('入学报名')) return 'fas fa-school';
                if (question.includes('初中') || question.includes('学区')) return 'fas fa-map-marker-alt';
                if (question.includes('高中') || question.includes('招生') || question.includes('分数线')) return 'fas fa-chart-line';
                if (question.includes('转学') || question.includes('手续')) return 'fas fa-exchange-alt';
                if (question.includes('资助') || question.includes('申请')) return 'fas fa-hand-holding-usd';
                if (question.includes('特殊教育')) return 'fas fa-heart';
                if (question.includes('幼儿园')) return 'fas fa-child';
                if (question.includes('证明') || question.includes('证件')) return 'fas fa-id-card';
                if (question.includes('咨询') || question.includes('电话')) return 'fas fa-phone';
                return 'fas fa-graduation-cap';
            }

            // 社会保险类图标
            if (serviceType === '社会保险') {
                if (question.includes('养老') || question.includes('退休')) return 'fas fa-user-clock';
                if (question.includes('医疗') || question.includes('医保')) return 'fas fa-heartbeat';
                if (question.includes('失业') || question.includes('就业')) return 'fas fa-briefcase';
                if (question.includes('工伤') || question.includes('伤残')) return 'fas fa-band-aid';
                if (question.includes('生育') || question.includes('产假')) return 'fas fa-baby';
                if (question.includes('缴费') || question.includes('费用')) return 'fas fa-credit-card';
                if (question.includes('查询') || question.includes('证明')) return 'fas fa-search';
                return 'fas fa-shield-alt';
            }

            // 社会救助类图标
            if (serviceType === '社会救助') {
                if (question.includes('低保') || question.includes('最低生活')) return 'fas fa-hand-holding-heart';
                if (question.includes('临时') || question.includes('应急')) return 'fas fa-first-aid';
                if (question.includes('医疗') || question.includes('救助')) return 'fas fa-hospital';
                if (question.includes('教育') || question.includes('助学')) return 'fas fa-graduation-cap';
                if (question.includes('住房') || question.includes('租房')) return 'fas fa-home';
                if (question.includes('残疾') || question.includes('特困')) return 'fas fa-wheelchair';
                return 'fas fa-hands-helping';
            }

            // 不动产业务类图标
            if (serviceType === '不动产业务') {
                if (question.includes('登记') || question.includes('证书')) return 'fas fa-certificate';
                if (question.includes('转移') || question.includes('过户')) return 'fas fa-exchange-alt';
                if (question.includes('抵押') || question.includes('贷款')) return 'fas fa-key';
                if (question.includes('查询') || question.includes('档案')) return 'fas fa-search';
                if (question.includes('预告') || question.includes('预售')) return 'fas fa-clock';
                return 'fas fa-building';
            }

            // 法人业务类图标
            if (serviceType === '法人业务') {
                if (question.includes('注册') || question.includes('设立')) return 'fas fa-plus-circle';
                if (question.includes('变更') || question.includes('修改')) return 'fas fa-edit';
                if (question.includes('注销') || question.includes('撤销')) return 'fas fa-times-circle';
                if (question.includes('备案') || question.includes('年检')) return 'fas fa-file-alt';
                if (question.includes('许可') || question.includes('资质')) return 'fas fa-award';
                return 'fas fa-building';
            }

            // 公积金业务类图标
            if (serviceType === '公积金业务') {
                if (question.includes('开户') || question.includes('缴存')) return 'fas fa-piggy-bank';
                if (question.includes('提取') || question.includes('支取')) return 'fas fa-hand-holding-usd';
                if (question.includes('贷款') || question.includes('借款')) return 'fas fa-home';
                if (question.includes('查询') || question.includes('余额')) return 'fas fa-search';
                if (question.includes('转移') || question.includes('封存')) return 'fas fa-exchange-alt';
                return 'fas fa-university';
            }

            // 保障房类图标
            if (serviceType === '保障房') {
                if (question.includes('申请') || question.includes('申报')) return 'fas fa-file-alt';
                if (question.includes('公租房') || question.includes('租赁')) return 'fas fa-key';
                if (question.includes('经济适用房') || question.includes('限价房')) return 'fas fa-home';
                if (question.includes('审核') || question.includes('资格')) return 'fas fa-check-circle';
                if (question.includes('分配') || question.includes('选房')) return 'fas fa-random';
                return 'fas fa-shield-alt';
            }

            // 人才认定类图标
            if (serviceType === '人才认定') {
                if (question.includes('申请') || question.includes('申报')) return 'fas fa-file-alt';
                if (question.includes('高层次') || question.includes('专家')) return 'fas fa-star';
                if (question.includes('技能') || question.includes('技术')) return 'fas fa-tools';
                if (question.includes('学历') || question.includes('学位')) return 'fas fa-graduation-cap';
                if (question.includes('职称') || question.includes('资格')) return 'fas fa-award';
                return 'fas fa-user-tie';
            }

            // 监督举报类图标
            if (serviceType === '监督举报') {
                if (question.includes('举报') || question.includes('投诉')) return 'fas fa-exclamation-triangle';
                if (question.includes('违法') || question.includes('违规')) return 'fas fa-ban';
                if (question.includes('腐败') || question.includes('贪污')) return 'fas fa-gavel';
                if (question.includes('环境') || question.includes('污染')) return 'fas fa-leaf';
                if (question.includes('食品') || question.includes('安全')) return 'fas fa-shield-alt';
                return 'fas fa-eye';
            }

            // 出入境类图标
            if (serviceType === '出入境') {
                if (question.includes('护照') || question.includes('签证')) return 'fas fa-passport';
                if (question.includes('港澳') || question.includes('通行证')) return 'fas fa-id-card';
                if (question.includes('台湾') || question.includes('台胞证')) return 'fas fa-address-card';
                if (question.includes('签注') || question.includes('续签')) return 'fas fa-stamp';
                if (question.includes('加急') || question.includes('快速')) return 'fas fa-clock';
                return 'fas fa-plane';
            }

            // 默认图标
            return 'fas fa-file-alt';
        };

        // 获取服务类型对应的图标和标题
        const getServiceInfo = (serviceType) => {
            const serviceMap = {
                '教育入学': { icon: 'fas fa-graduation-cap', title: '教育入学智能导办' },
                '社会保险': { icon: 'fas fa-shield-alt', title: '社会保险智能导办' },
                '社会救助': { icon: 'fas fa-hands-helping', title: '社会救助智能导办' },
                '不动产业务': { icon: 'fas fa-building', title: '不动产业务智能导办' },
                '法人业务': { icon: 'fas fa-building', title: '法人业务智能导办' },
                '公积金业务': { icon: 'fas fa-university', title: '公积金业务智能导办' },
                '保障房': { icon: 'fas fa-shield-alt', title: '保障房智能导办' },
                '人才认定': { icon: 'fas fa-user-tie', title: '人才认定智能导办' },
                '监督举报': { icon: 'fas fa-eye', title: '监督举报智能导办' },
                '出入境': { icon: 'fas fa-plane', title: '出入境智能导办' }
            };
            return serviceMap[serviceType] || { icon: 'fas fa-file-alt', title: '智能导办服务' };
        };

        const serviceInfo = getServiceInfo(serviceType);

        let questionsHTML = '';
        questions.forEach((q) => {
            const icon = getIconForQuestion(serviceType, q.question);
            questionsHTML += `
                <div class="guide-question-item" data-question="${q.question}" role="button" tabindex="0">
                    <i class="${icon}"></i>
                    <span>${q.question}</span>
                </div>
            `;
        });

        const guideHTML = `
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="education-guide">
                        <h4><i class="${serviceInfo.icon}"></i> 欢迎使用${serviceInfo.title}功能，请选择您需要办理的业务：</h4>
                        
                        <div class="guide-questions">
                            ${questionsHTML}
                        </div>
                    </div>
                </div>
                <div class="message-time">刚刚</div>
            </div>
        `;

        chatMessages.insertAdjacentHTML('beforeend', guideHTML);
        scrollToBottom();

        // 为问题项添加点击事件
        const questionItems = chatMessages.querySelectorAll('.guide-question-item');
        questionItems.forEach(item => {
            item.addEventListener('click', function() {
                const question = this.getAttribute('data-question');
                // 添加用户消息
                addUserMessage(question);
                // 自动发送问题
                sendQuestionDirectly(question);
            });

            // 添加键盘支持
            item.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });
    }

    // 常见问题点击事件
    faqItems.forEach(item => {
        item.addEventListener('click', function() {
            const question = this.textContent.trim();
            chatInput.value = question;
            sendMessage();
        });
    });

    // 个性卡片点击事件
    infoCards.forEach(card => {
        card.addEventListener('click', function() {
            const cardText = this.textContent.trim();
            window.open(`#${cardText}`, '_blank');
        });
    });



    // 通知消息函数
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getNotificationColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-out';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // 获取通知图标
    function getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle',
            'error': 'times-circle'
        };
        return icons[type] || 'info-circle';
    }

    // 获取通知颜色
    function getNotificationColor(type) {
        const colors = {
            'success': '#4caf50',
            'warning': '#ff9800',
            'info': '#2196f3',
            'error': '#f44336'
        };
        return colors[type] || '#2196f3';
    }

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .robot-head {
            animation: robotFloat 3s ease-in-out infinite;
        }

        @keyframes robotFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        .robot-eye {
            animation: robotBlink 4s infinite;
        }

        @keyframes robotBlink {
            0%, 90%, 100% {
                transform: scaleY(1);
            }
            95% {
                transform: scaleY(0.1);
            }
        }
    `;
    document.head.appendChild(style);

    // 显示初始欢迎消息（与index.html完全一致）
    function showInitialWelcomeMessage() {
        const messageHTML = `
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>您好，我是联通数科智能小政！很高兴为您服务，有什么问题您可以问我哦，我会尽力帮您解答！<br>如果还没想好，您可以试着问我：<br><span class="suggest-question" data-question="某某市如何办理教育入学？" role="button" tabindex="0" aria-label="点击询问：某某市如何办理教育入学？">某某市如何办理教育入学？</span> <span class="suggest-question" data-question="某某市如何领取养老金？" role="button" tabindex="0" aria-label="点击询问：某某市如何领取养老金？">某某市如何领取养老金？</span></p>
                </div>
                <div class="message-time">刚刚</div>
            </div>
        `;
        
        chatMessages.insertAdjacentHTML('beforeend', messageHTML);
        scrollToBottom();
        
        // 添加点击事件到新添加的建议问题
        const newMessage = chatMessages.lastElementChild;
        const suggestQuestions = newMessage.querySelectorAll('.suggest-question');
        suggestQuestions.forEach(question => {
            question.addEventListener('click', function() {
                const questionText = this.getAttribute('data-question');
                if (chatInput) {
                    chatInput.value = questionText;
                    sendMessage();
                }
            });
        });
    }

    // 页面可见性变化监听器
    document.addEventListener('visibilitychange', function() {
        isPageVisible = !document.hidden;

        if (isPageVisible) {
            // 页面变为可见时，恢复所有暂停的任务
            console.log('页面变为可见，恢复所有动画效果');
            resumeTypewriterTasks();
            resumeValidationTasks();
        } else {
            // 页面变为隐藏时，记录当前状态但不停止任务
            console.log('页面变为隐藏，所有动画效果将继续在后台运行');
        }
    });

    // 恢复打字机任务
    function resumeTypewriterTasks() {
        pendingTypewriterTasks.forEach(task => {
            if (task.isActive && task.currentIndex < task.cleanText.length) {
                // 计算应该已经完成的字符数（基于时间）
                const elapsedTime = Date.now() - task.startTime;
                const expectedIndex = Math.min(
                    Math.floor(elapsedTime / task.speed),
                    task.cleanText.length
                );

                // 如果实际进度落后于预期，快速追赶
                if (task.currentIndex < expectedIndex) {
                    const catchUpText = task.cleanText.substring(task.currentIndex, expectedIndex);
                    task.element.innerHTML += catchUpText.replace(/\n/g, '<br>');
                    task.currentIndex = expectedIndex;
                }
            }
        });
    }

    // 恢复校验任务
    function resumeValidationTasks() {
        pendingValidationTasks.forEach(task => {
            if (task.isActive && task.currentStep < task.stepItems.length) {
                // 计算应该已经完成的步骤数（基于时间）
                const elapsedTime = Date.now() - task.startTime;
                const totalStepTime = task.stepDuration + task.stepInterval;
                const expectedStep = Math.min(
                    Math.floor(elapsedTime / totalStepTime),
                    task.stepItems.length
                );

                // 如果实际进度落后于预期，快速追赶
                if (task.currentStep < expectedStep) {
                    for (let i = task.currentStep; i < expectedStep; i++) {
                        const stepItem = task.stepItems[i];
                        const stepText = stepItem.querySelector('.step-text').textContent;
                        const spinner = stepItem.querySelector('.fa-spin');
                        const checkIcon = stepItem.querySelector('.fa-check');
                        const timesIcon = stepItem.querySelector('.fa-times');

                        // 隐藏加载动画
                        spinner.style.display = 'none';
                        stepItem.classList.remove('processing');

                        // 显示结果图标
                        if (stepText.includes('❌')) {
                            timesIcon.style.display = 'inline-block';
                            stepItem.classList.add('failed');
                        } else {
                            checkIcon.style.display = 'inline-block';
                            stepItem.classList.add('completed');
                        }
                    }
                    task.currentStep = expectedStep;
                }
            }
        });
    }

    // 初始化按钮状态
    updateQAListButton();
    updateKnowledgeSearchButton();
    updateOnlineSearchButton();

    // 初始化欢迎消息
    if (chatMessages.children.length === 0) {
        showInitialWelcomeMessage();
    } else {
        // 如果已有初始消息（来自HTML），为建议问题添加点击事件
        const existingSuggestQuestions = chatMessages.querySelectorAll('.suggest-question');
        existingSuggestQuestions.forEach(question => {
            question.addEventListener('click', function() {
                const questionText = this.getAttribute('data-question');
                if (chatInput) {
                    chatInput.value = questionText;
                    sendMessage();
                }
            });
        });
    }

    // 页面滚动效果
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 0) {
            header.style.boxShadow = '0 4px 20px rgba(255, 71, 87, 0.4)';
        } else {
            header.style.boxShadow = '0 2px 10px rgba(255, 71, 87, 0.3)';
        }
    });

    // 响应式菜单（如果需要）
    const navToggle = document.querySelector('.nav-toggle');
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            const navLinks = document.querySelector('.nav-links');
            navLinks.classList.toggle('active');
        });
    }

    // 主题切换功能（如果需要）
    const themeToggle = document.querySelector('.theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            localStorage.setItem('theme', document.body.classList.contains('dark-theme') ? 'dark' : 'light');
        });

        // 加载保存的主题
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
        }
    }

    // 搜索功能增强
    chatInput.addEventListener('input', function() {
        const value = this.value;
        if (value.length > 0) {
            // 可以添加搜索建议功能
            showSearchSuggestions(value);
        } else {
            hideSearchSuggestions();
        }
    });

    function showSearchSuggestions(query) {
        // 搜索建议功能实现
        const suggestions = [
            '户口迁移手续',
            '社保缴费查询',
            '身份证办理',
            '结婚证办理',
            '房产证查询'
        ];

        const filtered = suggestions.filter(item => 
            item.toLowerCase().includes(query.toLowerCase())
        );

        // 这里可以显示搜索建议下拉框
        console.log('搜索建议:', filtered);
    }

    function hideSearchSuggestions() {
        // 隐藏搜索建议
        console.log('隐藏搜索建议');
    }

    // 添加操作标签点击事件
    function addOptionTagEvents(container) {
        const optionTags = container.querySelectorAll('.option-tag');
        const qaUrl = container.getAttribute('data-qa-url');
        const qaPhone = container.getAttribute('data-qa-phone');

        optionTags.forEach(tag => {
            tag.addEventListener('click', function() {
                const action = this.getAttribute('data-action');

                if (action === 'handle') {
                    // 立刻办理按钮
                    if (qaUrl && qaUrl !== 'null' && qaUrl !== '') {
                        // 如果有URL，直接跳转
                        window.open(qaUrl, '_blank');
                    } else {
                        showCustomAlert('提示', '可对接表单填报，待开发', 'info');
                    }
                } else if (action === 'consult') {
                    // 人工咨询按钮
                    const phoneNumber = (qaPhone && qaPhone !== 'null' && qaPhone !== '') ? qaPhone : '010-88888888';
                    showCustomAlert('人工咨询', `请拨打${phoneNumber}人工咨询`, 'phone');
                }
            });
        });
    }

    // 自定义弹窗函数 - 简约风格
    function showCustomAlert(title, message, type = 'info') {
        const modal = document.createElement('div');
        modal.className = 'custom-modal';

        // 根据类型选择图标
        const getIcon = (type) => {
            switch(type) {
                case 'success': return 'check-circle';
                case 'warning': return 'exclamation-triangle';
                case 'phone': return 'phone';
                case 'info':
                default: return 'info-circle';
            }
        };

        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-${getIcon(type)}"></i> ${title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-btn modal-confirm">确定</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加关闭事件
        const closeBtn = modal.querySelector('.modal-close');
        const confirmBtn = modal.querySelector('.modal-confirm');
        const backdrop = modal.querySelector('.modal-backdrop');

        [closeBtn, confirmBtn, backdrop].forEach(element => {
            element.addEventListener('click', () => {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => document.body.removeChild(modal), 300);
            });
        });

        // 显示动画
        setTimeout(() => modal.classList.add('show'), 10);
    }

    // 联网搜索用户须知确认弹窗
    function showOnlineSearchAgreement() {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'custom-modal agreement-modal';
            modal.innerHTML = `
                <div class="modal-backdrop"></div>
                <div class="modal-content agreement-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-globe"></i> 联网搜索服务协议</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="agreement-text">
                            
                            <div class="agreement-content-text">
                                <p>服务说明</p>
                                <ul>
                                    <li>联网搜索通过AI大模型提供智能问答服务</li>
                                    <li>当本地知识库无法匹配时，系统将调用AI模型</li>
                                    <li>AI模型基于训练数据生成相关回答</li>
                                </ul>

                                <p>免责声明</p>
                                <ul>
                                    <li><span class="text-danger">AI内容仅供参考，不构成官方政策解释</span></li>
                                    <li><span class="text-danger">具体政策法规请以官方文件为准</span></li>
                                    <li><span class="text-danger">AI可能存在理解偏差或信息过时,重要事务请通过官方渠道核实</span></li>
                                    
                                </ul>

                                <p>数据安全</p>
                                <ul>
                                    <li>问题将发送至AI服务提供商</li>
                                    <li>请避免输入敏感信息（身份证、手机号等）</li>
                                    
                                </ul>

                                <div class="agreement-highlight">
                                    <i class="fas fa-lightbulb"></i>
                                    <strong>建议：</strong>重要政务咨询建议优先使用人工服务渠道。
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer agreement-footer">
                        <button class="modal-btn modal-cancel">取消</button>
                        <button class="modal-btn modal-agree">我已阅读并同意</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 添加事件监听
            const closeBtn = modal.querySelector('.modal-close');
            const cancelBtn = modal.querySelector('.modal-cancel');
            const agreeBtn = modal.querySelector('.modal-agree');
            const backdrop = modal.querySelector('.modal-backdrop');

            function closeModal(agreed = false) {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(modal);
                    resolve(agreed);
                }, 300);
            }

            closeBtn.addEventListener('click', () => closeModal(false));
            cancelBtn.addEventListener('click', () => closeModal(false));
            backdrop.addEventListener('click', () => closeModal(false));
            agreeBtn.addEventListener('click', () => closeModal(true));

            // 显示动画
            setTimeout(() => modal.classList.add('show'), 10);
        });
    }

    // 高频办事服务项数据（智能导办）
    const serviceList = [
        { icon: 'fa-graduation-cap', color: 'pink', label: '教育入学', aria: '教育入学服务' },
        { icon: 'fa-heartbeat', color: 'blue', label: '社会保险', aria: '社会保险服务' },
        { icon: 'fa-hands-helping', color: 'green', label: '社会救助', aria: '社会救助服务' },
        { icon: 'fa-home', color: 'orange', label: '不动产业务', aria: '不动产业务服务' },
        { icon: 'fa-building', color: 'purple', label: '法人业务', aria: '法人业务服务' },
        { icon: 'fa-piggy-bank', color: 'cyan', label: '公积金业务', aria: '公积金业务服务' },
        { icon: 'fa-shield-alt', color: 'teal', label: '保障房', aria: '保障房服务' },
        { icon: 'fa-user-tie', color: 'indigo', label: '人才认定', aria: '人才认定服务' },
        { icon: 'fa-exclamation-triangle', color: 'red', label: '监督举报', aria: '监督举报服务' },
        { icon: 'fa-passport', color: 'brown', label: '出入境', aria: '出入境服务' }
    ];



    function renderServiceGrid(page = 1) {
        const serviceGrid = document.querySelector('.service-grid');
        if (!serviceGrid) return;
        serviceGrid.innerHTML = '';
        const itemsPerPage = 7;
        const totalPages = Math.ceil(serviceList.length / itemsPerPage);
        const start = (page - 1) * itemsPerPage;
        const end = start + itemsPerPage;
        const showList = serviceList.slice(start, end);
        showList.forEach(item => {
            const div = document.createElement('div');
            div.className = 'service-item';
            div.setAttribute('role', 'button');
            div.setAttribute('tabindex', '0');
            div.setAttribute('aria-label', item.aria);
            div.innerHTML = `<div class="service-icon ${item.color}"><i class="fas ${item.icon}"></i></div><span>${item.label}</span>`;

            // 添加点击事件
            div.addEventListener('click', function() {
                handleServiceItemClick(item.label);
            });

            // 添加键盘支持
            div.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleServiceItemClick(item.label);
                }
            });

            serviceGrid.appendChild(div);
        });
        // 翻页按钮
        const btn = document.createElement('button');
        btn.type = 'button';
        btn.className = 'service-item service-next-btn';
        btn.tabIndex = 0;
        if (page < totalPages) {
            btn.innerHTML = '<div class="service-icon gray"><i class="fas fa-chevron-right"></i></div><span>下一页</span>';
            btn.setAttribute('aria-label', '查看更多服务');
        } else {
            btn.innerHTML = '<div class="service-icon gray"><i class="fas fa-chevron-left"></i></div><span>上一页</span>';
            btn.setAttribute('aria-label', '返回第一页');
        }
        btn.onclick = function() {
            renderServiceGrid(page < totalPages ? page + 1 : 1);
        };
        btn.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                btn.click();
            }
        });
        serviceGrid.appendChild(btn);
    }



    // 智能问策模式状态
    let smartPolicyMode = false;

    // 智能问数模式状态
    let smartDataMode = false;



    // 页面加载后初始化
    // 注释掉默认渲染，只在用户点击时才渲染
    // renderServiceGrid(1);

    // 初始化按钮状态 - 默认关闭所有功能
    setTimeout(() => {
        const serviceSection = document.querySelector('.service-section');

        // 确保服务区域默认隐藏
        if (serviceSection) {
            serviceSection.style.display = 'none';
        }
    }, 100);

    console.log('智能政务平台已加载完成');

    // ==================== 个性卡片功能 ====================

    // 初始化个性卡片点击事件
    function initPersonalityCards() {
        const personalityCards = document.querySelectorAll('.personality-card');
        personalityCards.forEach(card => {
            card.addEventListener('click', function() {
                const service = this.dataset.service;
                handlePersonalityCardClick(service);
            });
        });
    }

    // 处理个性卡片点击
    function handlePersonalityCardClick(service) {
        // 点击任何个性卡片时，先停止所有正在进行的异步操作（包括校验流程）
        console.log('🛑 个性卡片点击，停止所有正在进行的异步操作');
        stopAllAsyncOperations();

        // 点击任何个性卡片时，关闭知识库检索和联网搜索模式
        if (knowledgeSearchEnabled || onlineSearchEnabled) {
            console.log('🔄 个性卡片点击，关闭知识库检索和联网搜索模式');

            // 关闭知识库检索模式
            if (knowledgeSearchEnabled) {
                knowledgeSearchEnabled = false;
                console.log('📚 知识库检索模式已关闭');
            }

            // 关闭联网搜索模式
            if (onlineSearchEnabled) {
                onlineSearchEnabled = false;
                console.log('🌐 联网搜索模式已关闭');
            }

            // 更新所有按钮状态
            updateQAListButton();
            updateKnowledgeSearchButton();
            updateOnlineSearchButton();

            // 显示提示信息
            showNotification('已自动关闭知识库检索和联网搜索模式', 'info');
        }

        // 点击其他个性卡片时，如果智能导办开启，则关闭智能导办
        if (smartGuideActive && service !== '智能导办') {
            console.log('🎯 点击其他卡片，关闭智能导办');
            smartGuideActive = false;

            // 隐藏服务项目区域
            const serviceSection = document.querySelector('.service-section');
            if (serviceSection) {
                serviceSection.style.display = 'none';
            }

            showNotification('智能导办已自动关闭', 'info');
        }

        switch(service) {
            case '市情概况': {
                // 市情概况功能
                console.log('🏙️ 激活市情概况模式');

                // 清除现有的对话记录
                chatMessages.innerHTML = '';

                // 显示市情概况欢迎消息和选项
                const cityInfoMessageHTML = `
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="city-info-header">
                                <div class="city-info-icon">
                                    <i class="fas fa-city"></i>
                                </div>
                                <h3>市情概况</h3>
                            </div>
                            <p class="city-info-description">
                                为您提供全面的城市信息服务。您可以了解城市基本情况、领导班子信息以及特色景点推荐。
                            </p>
                            <div class="city-info-options">
                                <div class="city-option-item" data-option="城市介绍">
                                    <div class="option-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <span>城市介绍</span>
                                </div>
                                <div class="city-option-item" data-option="领导班子">
                                    <div class="option-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <span>领导班子</span>
                                </div>
                                <div class="city-option-item" data-option="景点推荐">
                                    <div class="option-icon">
                                        <i class="fas fa-map-marked-alt"></i>
                                    </div>
                                    <span>景点推荐</span>
                                </div>
                            </div>
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                `;
                chatMessages.insertAdjacentHTML('beforeend', cityInfoMessageHTML);
                scrollToBottom();

                // 添加点击事件监听器
                addCityInfoOptionEvents();

                showNotification('市情概况功能已启用', 'info');
                break;
            }

            case '智能问策': {
                // 开启智能问策模式
                console.log('🔍 激活智能问策模式');
                smartPolicyMode = true;
                smartDataMode = false; // 关闭智能问数模式
                document.body.classList.add('smart-policy-mode');
                document.body.classList.remove('smart-data-mode');
                console.log('🔍 智能问策模式状态:', smartPolicyMode);

                // 清除现有的对话记录
                chatMessages.innerHTML = '';

                // 显示智能问策欢迎消息
                const policyMessageHTML = `
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p> <strong>智能问策</strong> 模式已开启！我可以帮您分析相关政策内容。您可以直接<strong> 发送政策链接 </strong>或<strong> 描述政策问题</strong>。</p>
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                `;
                chatMessages.insertAdjacentHTML('beforeend', policyMessageHTML);
                scrollToBottom();
                showNotification('智能问策模式已开启', 'success');
                break;
            }

            case '智能问数': {
                // 开启智能问数模式
                console.log('📊 激活智能问数模式');
                smartDataMode = true;
                smartPolicyMode = false; // 关闭智能问策模式
                document.body.classList.add('smart-data-mode');
                document.body.classList.remove('smart-policy-mode');
                console.log('📊 智能问数模式状态:', smartDataMode);

                // 清除现有的对话记录
                chatMessages.innerHTML = '';

                // 显示智能问数欢迎消息
                const dataMessageHTML = `
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p> <strong>智能问数</strong> 模式已开启！我可以帮您分析政务公开数据。您可以直接<strong> 向我提问 </strong>或<strong> 描述数据分析需求</strong>。（目前只有蔬菜价格数据）</p>
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                `;
                chatMessages.insertAdjacentHTML('beforeend', dataMessageHTML);
                scrollToBottom();
                showNotification('智能问数模式已开启', 'success');
                break;
            }

            case '智能导办': {
                // 智能导办功能 - 在聊天区域显示所有服务图标
                console.log('🎯 激活智能导办模式');

                // 检查是否只选中政务问答（纯政务问答模式）
                if (knowledgeSearchEnabled || onlineSearchEnabled) {
                    showCustomAlert('智能导办', '智能导办功能只能在纯政务问答模式下使用，请先关闭知识库检索和联网搜索功能', 'warning');
                    return;
                }

                // 关闭其他智能模式
                smartPolicyMode = false;
                smartDataMode = false;
                document.body.classList.remove('smart-policy-mode');
                document.body.classList.remove('smart-data-mode');

                // 激活智能导办模式
                smartGuideActive = true;

                // 清除现有的对话记录
                chatMessages.innerHTML = '';

                // 显示智能导办欢迎消息
                const guideMessageHTML = `
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p><strong>智能导办</strong> 模式已开启！我可以帮您快速找到所需的政务服务。您可以直接<strong> 点击下方服务图标 </strong>或<strong> 描述您的办事需求</strong>。</p>
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                `;
                chatMessages.insertAdjacentHTML('beforeend', guideMessageHTML);
                scrollToBottom();

                // 显示服务项目区域并渲染智能导办内容
                const serviceSection = document.querySelector('.service-section');
                if (serviceSection) {
                    serviceSection.style.display = 'block';
                    renderServiceGrid(1); // 渲染智能导办的服务网格
                }

                showNotification('🎯 智能导办已开启', 'success');
                break;
            }



            default:
                console.log('未知的个性卡片服务:', service);
        }
    }

    // 初始化个性卡片
    initPersonalityCards();



    // ==================== 市情概况功能 ====================

    // 获取市情概况信息
    async function fetchCityInfo(infoType) {
        try {
            console.log(`🏙️ 获取${infoType}信息`);

            const response = await fetch(`http://127.0.0.1:5000/city_info/${infoType}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // 使用打字机效果渲染内容
                addAIMessage(data.content, true, false, [], false, '', '', false);
            } else {
                addAIMessage('抱歉，无法获取相关信息。请稍后再试。', false);
            }

        } catch (error) {
            console.error(`获取${infoType}信息失败:`, error);
            addAIMessage('抱歉，获取信息时出现错误。请稍后再试。', false);
        }
    }

    // 添加市情概况选项事件监听器
    function addCityInfoOptionEvents() {
        const cityOptions = document.querySelectorAll('.city-option-item');
        cityOptions.forEach(option => {
            option.addEventListener('click', function() {
                const optionType = this.getAttribute('data-option');
                handleCityInfoOption(optionType);
            });
        });
    }

    // 处理市情概况选项点击
    function handleCityInfoOption(optionType) {
        console.log('🏙️ 点击市情概况选项:', optionType);

        let responseMessage = '';

        switch(optionType) {
            case '城市介绍':
                // 添加用户提问消息
                addUserMessage('城市介绍');

                // 直接发送"城市介绍"问题
                sendQuestionDirectly('城市介绍');
                return; // 直接返回，不显示responseMessage

            case '领导班子':
                // 添加用户提问消息
                addUserMessage('领导班子');

                // 调用领导班子API
                fetchCityInfo('leadership');
                return; // 直接返回，不显示responseMessage

            case '景点推荐':
                // 添加用户提问消息
                addUserMessage('景点推荐');

                // 显示景点推荐选项卡片
                showAttractionOptions();
                return; // 直接返回，不显示responseMessage
        }

        // 添加AI回复消息
        const aiMessageHTML = `
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    ${responseMessage}
                </div>
                <div class="message-time">刚刚</div>
            </div>
        `;

        chatMessages.insertAdjacentHTML('beforeend', aiMessageHTML);
        scrollToBottom();
    }

    // 显示景点推荐选项卡片
    function showAttractionOptions() {
        const attractionOptionsHTML = `
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="city-info-card">
                        <div class="city-info-header">
                            <div class="city-info-icon">
                                <i class="fas fa-map-marked-alt"></i>
                            </div>
                            <h3>景点推荐</h3>
                        </div>
                        <p class="city-info-description">
                            为您推荐热门景点，点击下方选项了解详细信息。
                        </p>
                        <div class="city-info-options">
                            <div class="city-option-item" data-attraction="故宫">
                                <div class="option-icon">
                                    <i class="fas fa-landmark"></i>
                                </div>
                                <span>故宫</span>
                            </div>
                            <div class="city-option-item" data-attraction="西湖">
                                <div class="option-icon">
                                    <i class="fas fa-water"></i>
                                </div>
                                <span>西湖</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="message-time">刚刚</div>
            </div>
        `;

        chatMessages.insertAdjacentHTML('beforeend', attractionOptionsHTML);
        scrollToBottom();

        // 为景点选项添加点击事件
        addAttractionOptionEvents();
    }

    // 添加景点选项点击事件
    function addAttractionOptionEvents() {
        const attractionOptions = chatMessages.querySelectorAll('.city-option-item[data-attraction]');
        attractionOptions.forEach(option => {
            option.addEventListener('click', function() {
                const attraction = this.dataset.attraction;
                handleAttractionClick(attraction);
            });
        });
    }

    // 处理景点点击
    function handleAttractionClick(attraction) {
        // 添加用户提问消息
        addUserMessage(attraction);

        // 直接发送景点问题，参考城市介绍的调用方式
        sendQuestionDirectly(attraction);
    }

    // ==================== 反馈弹窗功能 ====================

    // 显示反馈弹窗
    function showFeedbackModal() {
        const modal = document.getElementById('feedbackModal');
        if (modal) {
            // 清除之前的状态
            modal.classList.remove('hiding');
            modal.style.display = 'flex';

            // 强制重绘
            modal.offsetHeight;

            modal.classList.add('show');

            // 重置弹窗状态
            resetFeedbackModal();
        }
    }

    // 隐藏反馈弹窗
    function hideFeedbackModal() {
        const modal = document.getElementById('feedbackModal');
        if (modal && modal.classList.contains('show')) {
            modal.classList.remove('show');
            modal.classList.add('hiding');

            setTimeout(() => {
                modal.style.display = 'none';
                modal.classList.remove('hiding');
            }, 200);
        }
    }

    // 重置弹窗状态
    function resetFeedbackModal() {
        // 清除所有选中的原因按钮
        document.querySelectorAll('.feedback-reason-btn').forEach(btn => {
            btn.classList.remove('selected');
        });

        // 清空文本框
        const textarea = document.getElementById('feedbackText');
        if (textarea) {
            textarea.value = '';
            updateCharCount();
        }
    }

    // 更新字符计数
    function updateCharCount() {
        const textarea = document.getElementById('feedbackText');
        const charCount = document.getElementById('charCount');
        if (textarea && charCount) {
            charCount.textContent = textarea.value.length;
        }
    }

    // 提交反馈
    function submitFeedback() {
        const selectedReasons = Array.from(document.querySelectorAll('.feedback-reason-btn.selected'))
            .map(btn => btn.dataset.reason);
        const feedbackText = document.getElementById('feedbackText').value.trim();

        // 构建反馈内容
        let feedbackContent = '';
        if (selectedReasons.length > 0) {
            feedbackContent += '不满意原因：' + selectedReasons.join('、');
        }
        if (feedbackText) {
            if (feedbackContent) feedbackContent += '\n';
            feedbackContent += '详细意见：' + feedbackText;
        }

        // 这里可以发送反馈到后端
        console.log('用户反馈：', {
            reasons: selectedReasons,
            text: feedbackText,
            content: feedbackContent
        });

        // 隐藏弹窗并显示感谢消息
        hideFeedbackModal();
        showNotification('感谢您的反馈，我们会认真对待并持续改进！', 'success');
    }

    // 初始化反馈弹窗事件监听器
    function initFeedbackModal() {
        // 原因按钮点击事件
        document.querySelectorAll('.feedback-reason-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('selected');
            });
        });

        // 文本框字符计数
        const textarea = document.getElementById('feedbackText');
        if (textarea) {
            textarea.addEventListener('input', updateCharCount);
        }

        // 取消按钮
        const cancelBtn = document.getElementById('cancelFeedback');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', hideFeedbackModal);
        }

        // 确定按钮
        const confirmBtn = document.getElementById('confirmFeedback');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', submitFeedback);
        }

        // 点击弹窗外部关闭
        const modal = document.getElementById('feedbackModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideFeedbackModal();
                }
            });
        }

        // ESC键关闭弹窗的处理已移到initCorrectionModal中统一处理
    }

    // 在页面加载完成后初始化反馈弹窗
    initFeedbackModal();

    // ==================== 内容纠错弹窗功能 ====================

    // 显示纠错弹窗
    function showCorrectionModal() {
        const modal = document.getElementById('correctionModal');
        if (modal) {
            // 清除之前的状态
            modal.classList.remove('hiding');
            modal.style.display = 'flex';

            // 强制重绘
            modal.offsetHeight;

            modal.classList.add('show');

            // 重置弹窗状态
            resetCorrectionModal();
        }
    }

    // 隐藏纠错弹窗
    function hideCorrectionModal() {
        const modal = document.getElementById('correctionModal');
        if (modal && modal.classList.contains('show')) {
            modal.classList.remove('show');
            modal.classList.add('hiding');

            setTimeout(() => {
                modal.style.display = 'none';
                modal.classList.remove('hiding');
            }, 200);
        }
    }

    // 重置纠错弹窗状态
    function resetCorrectionModal() {
        // 清空所有输入框
        const originalContent = document.getElementById('originalContent');
        const errorReason = document.getElementById('errorReason');
        const referenceSource = document.getElementById('referenceSource');
        const contactInfo = document.getElementById('contactInfo');

        if (originalContent) {
            originalContent.value = '';
            updateCorrectionCharCount('originalContent', 'originalCharCount');
        }
        if (errorReason) {
            errorReason.value = '';
            updateCorrectionCharCount('errorReason', 'errorCharCount');
        }
        if (referenceSource) {
            referenceSource.value = '';
            updateCorrectionCharCount('referenceSource', 'referenceCharCount');
        }
        if (contactInfo) {
            contactInfo.value = '';
        }

        // 重置确定按钮状态
        updateCorrectionSubmitButton();
    }

    // 更新纠错弹窗字符计数
    function updateCorrectionCharCount(textareaId, countId) {
        const textarea = document.getElementById(textareaId);
        const charCount = document.getElementById(countId);
        if (textarea && charCount) {
            charCount.textContent = textarea.value.length;
        }

        // 更新提交按钮状态
        updateCorrectionSubmitButton();
    }

    // 更新提交按钮状态
    function updateCorrectionSubmitButton() {
        const originalContent = document.getElementById('originalContent');
        const errorReason = document.getElementById('errorReason');
        const confirmBtn = document.getElementById('confirmCorrection');

        if (originalContent && errorReason && confirmBtn) {
            const isValid = originalContent.value.trim() && errorReason.value.trim();
            confirmBtn.disabled = !isValid;
        }
    }

    // 提交纠错反馈
    function submitCorrection() {
        const originalContent = document.getElementById('originalContent').value.trim();
        const errorReason = document.getElementById('errorReason').value.trim();
        const referenceSource = document.getElementById('referenceSource').value.trim();
        const contactInfo = document.getElementById('contactInfo').value.trim();

        // 验证必填字段
        if (!originalContent || !errorReason) {
            showNotification('请填写原文内容和错误原因', 'warning');
            return;
        }

        // 构建纠错数据
        const correctionData = {
            originalContent,
            errorReason,
            referenceSource,
            contactInfo,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        };

        // 这里可以发送纠错数据到后端
        console.log('用户纠错反馈：', correctionData);

        // 隐藏弹窗并显示感谢消息
        hideCorrectionModal();
        showNotification('感谢您的纠错反馈！我们会认真核实并及时修正。', 'success');
    }

    // 初始化纠错弹窗事件监听器
    function initCorrectionModal() {
        // 文本框字符计数和验证
        const originalContent = document.getElementById('originalContent');
        const errorReason = document.getElementById('errorReason');
        const referenceSource = document.getElementById('referenceSource');

        if (originalContent) {
            originalContent.addEventListener('input', () => {
                updateCorrectionCharCount('originalContent', 'originalCharCount');
            });
        }

        if (errorReason) {
            errorReason.addEventListener('input', () => {
                updateCorrectionCharCount('errorReason', 'errorCharCount');
            });
        }

        if (referenceSource) {
            referenceSource.addEventListener('input', () => {
                updateCorrectionCharCount('referenceSource', 'referenceCharCount');
            });
        }

    // 图表渲染函数
    function renderCharts(chartsContainer) {
        if (!chartsContainer) return;

        const chartCanvases = chartsContainer.querySelectorAll('.chart-canvas');

        chartCanvases.forEach(canvas => {
            try {
                const chartConfig = JSON.parse(canvas.getAttribute('data-chart-config'));

                // 确保Chart.js已加载
                if (typeof Chart === 'undefined') {
                    console.warn('Chart.js未加载，无法渲染图表');
                    return;
                }

                // 创建图表
                new Chart(canvas, {
                    type: chartConfig.type,
                    data: chartConfig.data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        ...chartConfig.options
                    }
                });

            } catch (error) {
                console.error('图表渲染失败:', error);
                canvas.parentElement.innerHTML = '<div class="chart-error">图表渲染失败</div>';
            }
        });
    }

        // 取消按钮
        const cancelBtn = document.getElementById('cancelCorrection');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', hideCorrectionModal);
        }

        // 确定按钮
        const confirmBtn = document.getElementById('confirmCorrection');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', submitCorrection);
        }

        // 点击弹窗外部关闭
        const modal = document.getElementById('correctionModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideCorrectionModal();
                }
            });
        }

        // ESC键关闭弹窗（更新现有的ESC监听器）
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const feedbackModal = document.getElementById('feedbackModal');
                const correctionModal = document.getElementById('correctionModal');

                if (feedbackModal && feedbackModal.classList.contains('show')) {
                    hideFeedbackModal();
                } else if (correctionModal && correctionModal.classList.contains('show')) {
                    hideCorrectionModal();
                }
            }
        });
    }

    // 在页面加载完成后初始化纠错弹窗
    initCorrectionModal();
});